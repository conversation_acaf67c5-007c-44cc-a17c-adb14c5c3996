import { _decorator, Asset, Component, director, EPhysics2DDrawFlags, find, Game, game, Node, PhysicsSystem2D, Prefab, sp, SpriteAtlas } from 'cc';
import { GameIns } from '../GameIns';
import GameEnum from '../const/GameEnum';
import GameMapRun from '../ui/map/GameMapRun';
import BattleLayer from '../ui/layer/BattleLayer';
import FCollider from '../collider-system/FCollider';
import { GameConst } from '../const/GameConst';

const { ccclass, property } = _decorator;

@ccclass('GameMain')
export class GameMain extends Component {


    @property(GameMapRun)
    GameMapRun: GameMapRun | null = null;
    @property(BattleLayer)
    BattleLayer: BattleLayer | null = null;

    @property(Node)
    gameEnd: Node | null = null;


    protected onLoad(): void {
        GameIns.gameMainUI = this;
        
        GameIns.fColliderManager.enable = true;
        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider)=> {
            colliderA.entity?.onCollide?.(colliderB);
            colliderB.entity?.onCollide?.(colliderA);
        });

        if (GameConst.ColliderDraw) {
            PhysicsSystem2D.instance.enable = true;
            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;
        }
    }

    start() {
        GameIns.battleManager.startLoading(1,1);                           
    }

    showGameResult(isSuccess: boolean) {
        this.gameEnd!.active = true;

        let LabelWin = find("LabelWin", this.gameEnd!);
        let LabelFail = find("LabelFail", this.gameEnd!);
        LabelWin!.active = isSuccess;
        LabelFail!.active = !isSuccess;
    }

    onBtnAgainClicked() {
        this.gameEnd!.active = false;
        GameIns.battleManager.mainReset();
        director.loadScene("Main");
    }

    /**
     * 每帧更新逻辑
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void {
        // 限制 deltaTime 的最大值
        if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667; // 约等于 1/60 秒
        }

        switch (GameIns.battleManager.gameType) {
            case GameEnum.GameType.Common:
                GameIns.battleManager.update(deltaTime);
                break;
        }
    }

    lateUpdate(dt: number): void {
        // GameIns.colliderManager.update();
        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {
            GameIns.fColliderManager.update(dt);
        }
    }
}



{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts"], "names": ["BattleManager", "SingletonBase", "GameConst", "GameEnum", "GameIns", "GameMapRun", "UIMgr", "LoadingUI", "_percent", "gameType", "GameType", "Common", "initBattleEnd", "gameStart", "animSpeed", "_gameTime", "mainStage", "subStage", "_loadFinish", "_loadTotal", "_loadCount", "mainReset", "enemyManager", "boss<PERSON><PERSON><PERSON>", "waveManager", "reset", "mainPlaneManager", "instance", "clear", "bulletManager", "hurtEffectManager", "gameRuleManager", "subReset", "checkLoadFinish", "loadingUI", "get", "updateProgress", "initBattle", "closeUI", "addLoadCount", "count", "startLoading", "gameSortie", "gameResManager", "preload", "preLoad", "initData", "stageManager", "mainPlane", "planeIn", "onPlaneIn", "beginBattle", "begine", "update", "dt", "GameAble", "isGameOver", "planeManager", "enemyTarget", "isInBattle", "isGameWillOver", "updateGameLogic", "battleDie", "gamePause", "battleFail", "gameMainUI", "showGameResult", "hpBar", "active", "endBattle", "battleSucc", "stopFire", "checkStage", "startNextBattle", "gameOver", "removeAll", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "bossWillEnter", "bossFightStart", "fireEnable", "moveAble", "getRatio", "isGameType"], "mappings": ";;;yGASaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARJC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,U;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;+BAEIP,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAyD;AAAA;AAAA;AAAA,eAE5DQ,QAF4D,GAEjD,CAFiD;AAAA,eAG5DC,QAH4D,GAGjD;AAAA;AAAA,oCAASC,QAAT,CAAkBC,MAH+B;AAAA,eAI5DC,aAJ4D,GAI5C,KAJ4C;AAAA,eAK5DC,SAL4D,GAKhD,KALgD;AAAA,eAM5DC,SAN4D,GAMhD,CANgD;AAAA,eAO5DC,SAP4D,GAOhD,CAPgD;AAAA,eAS5DC,SAT4D,GAShD,CATgD;AAAA,eAU5DC,QAV4D,GAUjD,CAViD;AAAA,eAY5DC,WAZ4D,GAY9C,KAZ8C;AAAA,eAa5DC,UAb4D,GAa/C,CAb+C;AAAA,eAc5DC,UAd4D,GAc/C,CAd+C;AAAA;;AAiB5DC,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,YAAR,CAAqBD,SAArB;AACA;AAAA;AAAA,kCAAQE,WAAR,CAAoBF,SAApB;AACA;AAAA;AAAA,kCAAQG,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBL,SAAzB;AACA;AAAA;AAAA,wCAAWM,QAAX,CAAqBF,KAArB;AACA;AAAA;AAAA,wCAAWE,QAAX,CAAqBC,KAArB;AACA;AAAA;AAAA,kCAAQC,aAAR,CAAsBD,KAAtB;AACA;AAAA;AAAA,kCAAQE,iBAAR,CAA0BF,KAA1B;AACA;AAAA;AAAA,kCAAQG,eAAR,CAAwBN,KAAxB;AACH;;AAEDO,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQD,eAAR,CAAwBN,KAAxB;AACA;AAAA;AAAA,kCAAQD,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQH,YAAR,CAAqBU,QAArB;AACA;AAAA;AAAA,kCAAQT,WAAR,CAAoBS,QAApB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAG;AACd,eAAKb,UAAL;AACA,cAAIc,SAAS,GAAG;AAAA;AAAA,8BAAMC,GAAN;AAAA;AAAA,qCAAhB;AACAD,UAAAA,SAAS,CAACE,cAAV,CAAyB,KAAKhB,UAAL,GAAkB,KAAKD,UAAhD;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC,iBAAKkB,UAAL;AACA;AAAA;AAAA,gCAAMC,OAAN;AAAA;AAAA;AACH;AAEJ;;AAEDC,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAKrB,UAAL,IAAmBqB,KAAnB;AACH;;AAEDC,QAAAA,YAAY,CAACzB,SAAD,EAAmBC,QAAe,GAAG,CAArC,EAAwC;AAChD,eAAKD,SAAL,GAAiBA,SAAjB;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AAEA;AAAA;AAAA,kCAAQc,eAAR,CAAwBW,UAAxB;AACA;AAAA;AAAA,kCAAQC,cAAR,CAAuBC,OAAvB;AACA;AAAA;AAAA,kCAAQlB,gBAAR,CAAyBkB,OAAzB;AACA;AAAA;AAAA,kCAAQf,aAAR,CAAsBgB,OAAtB,CAA8B7B,SAA9B,EAPgD,CAOP;;AACzC;AAAA;AAAA,kCAAQc,iBAAR,CAA0Be,OAA1B,GARgD,CAQZ;;AACpC;AAAA;AAAA,wCAAWlB,QAAX,CAAqBmB,QAArB,CAA8B9B,SAA9B,EATgD,CASP;;AACzC;AAAA;AAAA,kCAAQM,YAAR,CAAqBuB,OAArB,CAA6B7B,SAA7B,EAVgD,CAUR;;AACxC;AAAA;AAAA,kCAAQO,WAAR,CAAoBsB,OAApB,GAXgD,CAWlB;AACjC;;AAIDR,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQU,YAAR,CAAqBV,UAArB,CAAgC,KAAKrB,SAArC,EAAgD,KAAKC,QAArD;AACA;AAAA;AAAA,kCAAQK,YAAR,CAAqBe,UAArB,CAAgC,KAAKrB,SAArC,EAAgD,KAAKC,QAArD;AACA;AAAA;AAAA,kCAAQS,gBAAR,CAAyBsB,SAAzB,CAAoCX,UAApC;AACA;AAAA;AAAA,kCAAQX,gBAAR,CAAyBsB,SAAzB,CAAoCC,OAApC;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,eAAKtC,aAAL,GAAqB,IAArB;AACA,eAAKuC,WAAL;AACH;;AAEDA,QAAAA,WAAW,GAAG;AACV,cAAI,KAAKvC,aAAL,IAAsB,CAAC,KAAKC,SAAhC,EAA2C;AACvC,iBAAKA,SAAL,GAAiB,IAAjB;AAEA;AAAA;AAAA,oCAAQkC,YAAR,CAAqBlC,SAArB;AACA;AAAA;AAAA,oCAAQW,WAAR,CAAoBX,SAApB;AACA;AAAA;AAAA,oCAAQkB,eAAR,CAAwBlB,SAAxB;AAEA;AAAA;AAAA,oCAAQa,gBAAR,CAAyBsB,SAAzB,CAAoCI,MAApC,CAA2C,IAA3C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,EAAD,EAAY;AACd,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB;AACrB;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQxB,eAAR,CAAwByB,UAAxB,EAAJ,EAA0C;AACtC,gBAAI;AAAA;AAAA,oCAAQC,YAAZ,EAA0B;AACtB;AAAA;AAAA,sCAAQA,YAAR,CAAqBC,WAArB,GAAmC,IAAnC;AACH;;AACD;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQ3B,eAAR,CAAwB4B,UAAxB,MAAwC;AAAA;AAAA,kCAAQ5B,eAAR,CAAwB6B,cAAxB,EAA5C,EAAsF;AAClF;AAAA;AAAA,oCAAQH,YAAR,CAAqBJ,MAArB,CAA4BC,EAA5B;AACA;AAAA;AAAA,oCAAQ9B,WAAR,CAAoBqC,eAApB,CAAoCP,EAApC;AACA;AAAA;AAAA,oCAAQhC,YAAR,CAAqBuC,eAArB,CAAqCP,EAArC;AACA;AAAA;AAAA,oCAAQ/B,WAAR,CAAoBsC,eAApB,CAAoCP,EAApC;AAEA;AAAA;AAAA,oCAAQvB,eAAR,CAAwB8B,eAAxB,CAAwCP,EAAxC;AACA,iBAAKvC,SAAL,IAAkBuC,EAAlB;AACH,WARD,MAQO,IAAI;AAAA;AAAA,kCAAQG,YAAZ,EAA0B;AAC7B;AAAA;AAAA,oCAAQA,YAAR,CAAqBC,WAArB,GAAmC,IAAnC;AACH;AACJ;AAED;AACJ;AACA;;;AACmB,cAATI,SAAS,GAAG;AACd;AACA;AAAA;AAAA,kCAAQ/B,eAAR,CAAwBgC,SAAxB;AACH,SAjI2D,CAmI5D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQC,UAAR,CAAoBC,cAApB,CAAmC,KAAnC;AACA;AAAA;AAAA,kCAAQxC,gBAAR,CAAyBsB,SAAzB,CAAoCmB,KAApC,CAA2CC,MAA3C,GAAoD,KAApD;AACA,eAAKC,SAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQ5C,gBAAR,CAAyBsB,SAAzB,CAAoCmB,KAApC,CAA2CC,MAA3C,GAAoD,KAApD;AACA;AAAA;AAAA,kCAAQ1C,gBAAR,CAAyBsB,SAAzB,CAAoCuB,QAApC;AACA,eAAKF,SAAL;;AAEA,cAAI;AAAA;AAAA,kCAAQtB,YAAR,CAAqByB,UAArB,CAAgC,KAAKxD,SAArC,EAA+C,KAAKC,QAAL,GAAgB,CAA/D,CAAJ,EAAsE;AAClE,iBAAKwD,eAAL;AACH,WAFD,MAEK;AACD;AAAA;AAAA,oCAAQR,UAAR,CAAoBC,cAApB,CAAmC,IAAnC;AACH;AACJ;AACG;AACR;AACA;;;AACIO,QAAAA,eAAe,GAAG;AACd,eAAKzC,QAAL;AACA,eAAKf,QAAL,IAAiB,CAAjB;AACA,eAAKoB,UAAL;AACH;AAED;AACJ;AACA;;;AACIgC,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQtC,eAAR,CAAwB2C,QAAxB;AACA;AAAA;AAAA,kCAAQ7C,aAAR,CAAsB8C,SAAtB,CAAgC,KAAhC,EAAuC,IAAvC;AAEA,eAAK9D,SAAL,GAAiB,KAAjB;AACA,eAAKD,aAAL,GAAqB,KAArB;AACH;AAGD;AACJ;AACA;AACA;;;AACIgE,QAAAA,gBAAgB,CAACC,QAAD,EAAmB,CAC/B;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDC,QAAAA,aAAa,GAAG,CACZ;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACH;AACD;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb;AAAA;AAAA,kCAAQrD,gBAAR,CAAyBsD,UAAzB,GAAsC,IAAtC;AACA;AAAA;AAAA,kCAAQtD,gBAAR,CAAyBuD,QAAzB,GAAoC,IAApC;AAEA;AAAA;AAAA,kCAAQ1D,WAAR,CAAoBwD,cAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,QAAQ,GAAG;AACP,iBAAO,QAAP,CADO,CACU;AACpB;;AAEDC,QAAAA,UAAU,CAAC1E,QAAD,EAAoB;AAC1B,iBAAO,KAAKA,QAAL,IAAiBA,QAAxB;AACH;;AA7O2D,O", "sourcesContent": ["\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport { UIMgr } from \"../../ui/UIMgr\";\r\nimport { LoadingUI } from \"../../ui/LoadingUI\";\r\n\r\nexport class BattleManager extends SingletonBase<BattleManager> {\r\n\r\n    _percent = 0;\r\n    gameType = GameEnum.GameType.Common;\r\n    initBattleEnd = false;\r\n    gameStart = false;\r\n    animSpeed = 1;\r\n    _gameTime = 0;\r\n\r\n    mainStage = 0;\r\n    subStage = 0;\r\n\r\n    _loadFinish = false;\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n\r\n\r\n    mainReset() {\r\n        GameIns.enemyManager.mainReset();\r\n        GameIns.bossManager.mainReset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.mainPlaneManager.mainReset();\r\n        GameMapRun.instance!.reset();\r\n        GameMapRun.instance!.clear();\r\n        GameIns.bulletManager.clear();\r\n        GameIns.hurtEffectManager.clear();\r\n        GameIns.gameRuleManager.reset();\r\n    }\r\n\r\n    subReset() {\r\n        GameIns.gameRuleManager.reset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.enemyManager.subReset();\r\n        GameIns.bossManager.subReset();\r\n    }\r\n\r\n    /**\r\n     * 检查所有资源是否加载完成\r\n     */\r\n    checkLoadFinish() {\r\n        this._loadCount++;\r\n        let loadingUI = UIMgr.get(LoadingUI)\r\n        loadingUI.updateProgress(this._loadCount / this._loadTotal)\r\n        if (this._loadCount >= this._loadTotal) {\r\n            this.initBattle();\r\n            UIMgr.closeUI(LoadingUI)\r\n        }\r\n\r\n    }\r\n\r\n    addLoadCount(count :number) {\r\n        this._loadTotal += count;\r\n    }\r\n\r\n    startLoading(mainStage:number, subStage:number = 1) {\r\n        this.mainStage = mainStage;\r\n        this.subStage = subStage;\r\n\r\n        GameIns.gameRuleManager.gameSortie();\r\n        GameIns.gameResManager.preload();\r\n        GameIns.mainPlaneManager.preload();\r\n        GameIns.bulletManager.preLoad(mainStage);//子弹资源\r\n        GameIns.hurtEffectManager.preLoad();//伤害特效资源\r\n        GameMapRun.instance!.initData(mainStage);//地图背景初始化\r\n        GameIns.enemyManager.preLoad(mainStage);//敌人资源\r\n        GameIns.bossManager.preLoad();//boss资源\r\n    }\r\n\r\n\r\n\r\n    initBattle() {\r\n        GameIns.stageManager.initBattle(this.mainStage, this.subStage);\r\n        GameIns.enemyManager.initBattle(this.mainStage, this.subStage);\r\n        GameIns.mainPlaneManager.mainPlane!.initBattle();\r\n        GameIns.mainPlaneManager.mainPlane!.planeIn();\r\n    }\r\n\r\n    onPlaneIn() {\r\n        this.initBattleEnd = true;\r\n        this.beginBattle();\r\n    }\r\n\r\n    beginBattle() {\r\n        if (this.initBattleEnd && !this.gameStart) {\r\n            this.gameStart = true;\r\n\r\n            GameIns.stageManager.gameStart();\r\n            GameIns.waveManager.gameStart();\r\n            GameIns.gameRuleManager.gameStart();\r\n\r\n            GameIns.mainPlaneManager.mainPlane!.begine(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    update(dt:number) {\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameRuleManager.isGameOver()) {\r\n            if (GameIns.planeManager) {\r\n                GameIns.planeManager.enemyTarget = null;\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {\r\n            GameIns.planeManager.update(dt);\r\n            GameIns.waveManager.updateGameLogic(dt);\r\n            GameIns.enemyManager.updateGameLogic(dt);\r\n            GameIns.bossManager.updateGameLogic(dt);\r\n\r\n            GameIns.gameRuleManager.updateGameLogic(dt);\r\n            this._gameTime += dt;\r\n        } else if (GameIns.planeManager) {\r\n            GameIns.planeManager.enemyTarget = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 战斗失败逻辑\r\n     */\r\n    async battleDie() {\r\n        // GameFunc.addDialog(ReplayUI.default);\r\n        GameIns.gameRuleManager.gamePause();\r\n    }\r\n\r\n    //     /**\r\n    //      * 战斗复活逻辑\r\n    //      */\r\n    //     relifeBattle() {\r\n    //         GameIns.eventManager.emit(GameEvent.MainRelife);\r\n    //         GameIns.gameRuleManager.gameResume();\r\n    //     }\r\n\r\n    /**\r\n     * 战斗失败结算\r\n     */\r\n    battleFail() {\r\n        GameIns.gameMainUI!.showGameResult(false);\r\n        GameIns.mainPlaneManager.mainPlane!.hpBar!.active = false;\r\n        this.endBattle();\r\n    }\r\n\r\n    /**\r\n     * 战斗胜利逻辑\r\n     */\r\n    battleSucc() {\r\n        GameIns.mainPlaneManager.mainPlane!.hpBar!.active = false;\r\n        GameIns.mainPlaneManager.mainPlane!.stopFire();\r\n        this.endBattle();\r\n\r\n        if (GameIns.stageManager.checkStage(this.mainStage,this.subStage + 1)){\r\n            this.startNextBattle();\r\n        }else{\r\n            GameIns.gameMainUI!.showGameResult(true);\r\n        }\r\n    }\r\n        /**\r\n     * 继续下一场战斗\r\n     */\r\n    startNextBattle() {\r\n        this.subReset();\r\n        this.subStage += 1;\r\n        this.initBattle();\r\n    }\r\n\r\n    /**\r\n     * 结束战斗\r\n     */\r\n    endBattle() {\r\n        GameIns.gameRuleManager.gameOver();\r\n        GameIns.bulletManager.removeAll(false, true);\r\n\r\n        this.gameStart = false;\r\n        this.initBattleEnd = false;\r\n    }\r\n\r\n\r\n    /**\r\n     * Boss切换完成\r\n     * @param {string} bossName Boss名称\r\n     */\r\n    bossChangeFinish(bossName: string) {\r\n        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        // if (bossEnterDialog) {\r\n        //     bossEnterDialog.node.active = true;\r\n        //     GameIns.mainPlaneManager.moveAble = false;\r\n        //     bossEnterDialog.showTips(bossName);\r\n        // }\r\n    }\r\n\r\n    bossWillEnter() {\r\n        //        GameIns.mainPlaneManager.fireEnable = false;\r\n        //        GameIns.mainPlaneManager.moveAble = false;\r\n        //         WinePlaneManager.default.me.pauseBattle();\r\n\r\n        //         const inGameUI = GameIns.uiManager.getDialog(InGameUI.default);\r\n        //         if (inGameUI) {\r\n        //             inGameUI.hideUI();\r\n        //         }\r\n\r\n        //         const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        //         if (bossEnterDialog) {\r\n        //             if (!bossEnterDialog.node.parent) {\r\n        //                 GameIns.uiManager.addDialog(BossEnterDialog.default, bossEnterDialog);\r\n        //             }\r\n        //             bossEnterDialog.node.active = true;\r\n        //             bossEnterDialog.play();\r\n        //         }\r\n\r\n        //         GameIns.audioManager.playbg(\"bg_3\");\r\n    }\r\n    /**\r\n     * 开始Boss战斗\r\n     */\r\n    bossFightStart() {\r\n        GameIns.mainPlaneManager.fireEnable = true;\r\n        GameIns.mainPlaneManager.moveAble = true;\r\n\r\n        GameIns.bossManager.bossFightStart();\r\n    }\r\n\r\n    /**\r\n     * 获取屏幕比例\r\n     * @returns {number} 屏幕比例\r\n     */\r\n    getRatio() {\r\n        return 0.666667; // 固定比例值\r\n    }\r\n\r\n    isGameType(gameType : number) {\r\n        return this.gameType == gameType;\r\n    }\r\n}"]}
{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEquipInfoUI.ts"], "names": ["_decorator", "Label", "DataMgr", "EventMgr", "MyApp", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "OpenEquipInfoUISource", "ccclass", "property", "PlaneEquipInfoUI", "_planeEquipInfo", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "PopUp", "getUIOption", "isClickBgCloseUI", "onLoad", "levelUpEquipBtn", "addClick", "onClickLevelUpEquip", "multiLevelUpEquipBtn", "onClickMultiLevelUpEquip", "unEquipBtn", "onClickUnEquip", "replaceEquipBtn", "onClickReplaceEquip", "onShow", "planeEquipInfo", "source", "tbEquip", "lubanMgr", "table", "TbEquip", "DisPlay", "node", "active", "parent", "slot", "equip", "eqSlots", "getEmptySlotByClass", "get", "item_id", "equipClass", "getComponentInChildren", "string", "lubanTables", "name", "unequip", "guid", "hideUI", "onHide", "args", "onClose", "targetOff", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AAGZC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,qB,iBAAAA,qB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;kCAGjBY,gB,WADZF,OAAO,CAAC,kBAAD,C,UASHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,2BAfb,MACaC,gBADb;AAAA;AAAA,4BAC6C;AAAA;AAAA;AAAA,eAMjCC,eANiC,GAMK,IANL;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AACrB,eAANC,MAAM,GAAW;AAAE,iBAAO,gCAAP;AAA0C;;AACrD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,KAAf;AAAsB;;AACjC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAYSC,QAAAA,MAAM,GAAS;AACrB,eAAKC,eAAL,CAAqBC,QAArB,CAA8B,KAAKC,mBAAnC,EAAwD,IAAxD;AACA,eAAKC,oBAAL,CAA0BF,QAA1B,CAAmC,KAAKG,wBAAxC,EAAkE,IAAlE;AACA,eAAKC,UAAL,CAAgBJ,QAAhB,CAAyB,KAAKK,cAA9B,EAA8C,IAA9C;AACA,eAAKC,eAAL,CAAqBN,QAArB,CAA8B,KAAKO,mBAAnC,EAAwD,IAAxD;AACH;;AAEW,cAANC,MAAM,CAACC,cAAD,EAAqCC,MAArC,EAAmF;AAAA;;AAC3F,gBAAMC,OAAO,GAAG;AAAA;AAAA,8BAAMC,QAAN,CAAeC,KAAf,CAAqBC,OAArC;;AACA,cAAIJ,MAAM,IAAI;AAAA;AAAA,8DAAsBK,OAApC,EAA6C;AACzC,iBAAKT,eAAL,CAAqBU,IAArB,CAA0BC,MAA1B,GAAmC,KAAnC;AACA,iBAAKb,UAAL,CAAgBY,IAAhB,CAAqBE,MAArB,CAA4BD,MAA5B,GAAqC,IAArC;AACH,WAHD,MAGO;AAAA;;AACH,iBAAKX,eAAL,CAAqBU,IAArB,CAA0BC,MAA1B,GAAmC,IAAnC;AACA,iBAAKb,UAAL,CAAgBY,IAAhB,CAAqBE,MAArB,CAA4BD,MAA5B,GAAqC,KAArC;AACA,kBAAME,IAAI,GAAG;AAAA;AAAA,oCAAQC,KAAR,CAAcC,OAAd,CAAsBC,mBAAtB,iBAA0CX,OAAO,CAACY,GAAR,CAAYd,cAAc,CAACe,OAA3B,CAA1C,qBAA0C,aAAqCC,UAA/E,CAAb;;AACA,gBAAI,CAACN,IAAL,EAAW;AACP,mBAAKb,eAAL,CAAqBoB,sBAArB,CAA4C9C,KAA5C,EAAmD+C,MAAnD,GAA4D,IAA5D;AACH,aAFD,MAEO;AACH,mBAAKrB,eAAL,CAAqBoB,sBAArB,CAA4C9C,KAA5C,EAAmD+C,MAAnD,GAA4D,IAA5D;AACH;AACJ;;AACD,eAAKD,sBAAL,CAA4B9C,KAA5B,EAAmC+C,MAAnC,4BAA4C;AAAA;AAAA,8BAAMC,WAAN,CAAkBd,OAAlB,CAA0BS,GAA1B,CAA8Bd,cAAc,CAACe,OAA7C,CAA5C,qBAA4C,sBAAuDK,IAAnG;AACA,eAAKrC,eAAL,GAAuBiB,cAAvB;AACH;;AAEOF,QAAAA,mBAAmB,GAAG;AAC1B;AAAA;AAAA,kCAAQa,KAAR,CAAcC,OAAd,CAAsBD,KAAtB,CAA4B,KAAK5B,eAAjC;AACH;;AAEOa,QAAAA,cAAc,GAAG;AACrB;AAAA;AAAA,kCAAQe,KAAR,CAAcC,OAAd,CAAsBS,OAAtB,CAA8B,KAAKtC,eAAL,CAAqBuC,IAAnD;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAazC,gBAAb;AACH;;AAEOU,QAAAA,mBAAmB,GAAG,CAC1B;AACH;;AACOE,QAAAA,wBAAwB,GAAG,CAC/B;AACH;;AAEW,cAAN8B,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAAG;;AAClC,cAAPC,OAAO,CAAC,GAAGD,IAAJ,EAAgC;AACzC;AAAA;AAAA,oCAASE,SAAT,CAAmB,IAAnB;AACH;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAhEwC,O;;;;;iBASX,I;;;;;;;iBAEL,I;;;;;;;iBAEK,I;;;;;;;iBAEK,I", "sourcesContent": ["\nimport { _decorator, Label } from 'cc';\n\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { DataMgr } from '../../../Data/DataManager';\nimport { EventMgr } from '../../../event/EventManager';\nimport { MyApp } from '../../../MyApp';\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\nimport { BaseUI, UILayer, UIMgr, UIOpt } from '../../UIMgr';\nimport { OpenEquipInfoUISource } from './PlaneTypes';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlaneEquipInfoUI')\nexport class PlaneEquipInfoUI extends BaseUI {\n    public static getUrl(): string { return \"ui/main/plane/PlaneEquipInfoUI\"; }\n    public static getLayer(): UILayer { return UILayer.PopUp }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: true }\n    }\n    private _planeEquipInfo: csproto.cs.ICSItem = null;\n\n    @property(ButtonPlus)\n    replaceEquipBtn: ButtonPlus = null;\n    @property(ButtonPlus)\n    unEquipBtn: ButtonPlus = null;\n    @property(ButtonPlus)\n    levelUpEquipBtn: ButtonPlus = null;\n    @property(ButtonPlus)\n    multiLevelUpEquipBtn: ButtonPlus = null;\n\n    protected onLoad(): void {\n        this.levelUpEquipBtn.addClick(this.onClickLevelUpEquip, this)\n        this.multiLevelUpEquipBtn.addClick(this.onClickMultiLevelUpEquip, this)\n        this.unEquipBtn.addClick(this.onClickUnEquip, this)\n        this.replaceEquipBtn.addClick(this.onClickReplaceEquip, this)\n    }\n\n    async onShow(planeEquipInfo: csproto.cs.ICSItem, source: OpenEquipInfoUISource): Promise<void> {\n        const tbEquip = MyApp.lubanMgr.table.TbEquip\n        if (source == OpenEquipInfoUISource.DisPlay) {\n            this.replaceEquipBtn.node.active = false;\n            this.unEquipBtn.node.parent.active = true\n        } else {\n            this.replaceEquipBtn.node.active = true\n            this.unEquipBtn.node.parent.active = false\n            const slot = DataMgr.equip.eqSlots.getEmptySlotByClass(tbEquip.get(planeEquipInfo.item_id)?.equipClass)\n            if (!slot) {\n                this.replaceEquipBtn.getComponentInChildren(Label).string = \"替换\"\n            } else {\n                this.replaceEquipBtn.getComponentInChildren(Label).string = \"装备\"\n            }\n        }\n        this.getComponentInChildren(Label).string = MyApp.lubanTables.TbEquip.get(planeEquipInfo.item_id)?.name\n        this._planeEquipInfo = planeEquipInfo;\n    }\n\n    private onClickReplaceEquip() {\n        DataMgr.equip.eqSlots.equip(this._planeEquipInfo)\n    }\n\n    private onClickUnEquip() {\n        DataMgr.equip.eqSlots.unequip(this._planeEquipInfo.guid)\n        UIMgr.hideUI(PlaneEquipInfoUI)\n    }\n\n    private onClickLevelUpEquip() {\n        //EventMgr.emit(PlaneUIEvent.LevelUpEquip)\n    }\n    private onClickMultiLevelUpEquip() {\n        //EventMgr.emit(PlaneUIEvent.MultiLevelUpEquip)\n    }\n\n    async onHide(...args: any[]): Promise<void> { }\n    async onClose(...args: any[]): Promise<void> {\n        EventMgr.targetOff(this)\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}
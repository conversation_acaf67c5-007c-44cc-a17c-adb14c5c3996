import { _decorator, Component, Game } from "cc";
import { IMgr, FnOnUpdate, FnOnLateUpdate } from "./IMgr";
import { LubanMgr } from "./Luban/LubanMgr";
import { NetMgr } from "./Network/NetMgr";
import { audioManager } from './ResUpdate/audioManager';
import * as cfg from './AutoGen/Luban/schema';
import { IPlatformSDK, CreateLoginSDK } from "./PlatformSDK/IPlatformSDK";
import { ResManager } from "./core/base/ResManager";
import { GlobalDataManager } from "./Game/manager/GlobalDataManager";

const { ccclass } = _decorator;

@ccclass("MyApp")
export class MyApp extends Component {

    private static _instance: MyApp|null = null;
    public static GetInstance(): MyApp {
        return MyApp._instance!;
    }
    private ManagerPool:IMgr[]= [];
    private _updateContainer:FnOnUpdate[]=[];
    private _lateUpdateContainer:FnOnLateUpdate[]=[];

    private _lubanMgr:LubanMgr|null = null;
    private _netMgr:NetMgr|null = null;
    private _resMgr:ResManager|null = null;
    private _platformSDK:IPlatformSDK|null =null;
    private _globalDataManager:GlobalDataManager|null =null;

    

    onLoad():void {
        MyApp._instance = this; 

        this.ManagerPool.push(audioManager.instance);
        this._lubanMgr = new LubanMgr();
        this.ManagerPool.push(this._lubanMgr);
        this._netMgr = new NetMgr();
        this.ManagerPool.push(this._netMgr);
        this._resMgr = new ResManager();
        this.ManagerPool.push(this._resMgr);
        this._platformSDK = CreateLoginSDK();
        this._globalDataManager = new GlobalDataManager();

        this.ManagerPool.forEach(manager => {
            manager.init();
             this._updateContainer.push(manager.onUpdate.bind(manager));
             this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));
         });
    }
    update (deltaTime: number):void {
        for (let i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
        }
    }

    lateUpdate():void {
        for (let i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i]();
        }
    }

    static get netMgr():NetMgr {
        return MyApp.GetInstance()._netMgr!;
    }
    static get lubanMgr():LubanMgr {
        return MyApp.GetInstance()._lubanMgr!;
    }
    static get lubanTables():cfg.Tables {
        return MyApp.GetInstance()._lubanMgr!.table;
    }
    static get platformSDK():IPlatformSDK {
        return MyApp.GetInstance()._platformSDK!;
    }
    static get resMgr():ResManager {
        return MyApp.GetInstance()._resMgr!;
    }
     static get globalDataManager():GlobalDataManager {
        return MyApp.GetInstance()._globalDataManager!;
    }
}
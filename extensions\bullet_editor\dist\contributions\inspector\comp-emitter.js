'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const fs_1 = require("fs");
const path_1 = require("path");
exports.template = `
<ui-prop type="dump" class="bulletID"></ui-prop>
<ui-prop type="dump" class="emitterData"></ui-prop>
<ui-prop type="dump" class="bulletData"></ui-prop>
<div class="event-group-section">
    <ui-label value="Event Groups"></ui-label>
    <div class="event-group-list"></div>
    <ui-button class="add-event-group">Add Event Group</ui-button>
</div>
`;
exports.$ = {
    bulletID: '.bulletID',
    emitterData: '.emitterData',
    bulletData: '.bulletData',
    eventGroupList: '.event-group-list',
    addEventGroupBtn: '.add-event-group',
};
// Define your bullet data source
const BULLET_DATA = [
    { id: 1, name: "普通子弹", description: "基础攻击子弹" },
    { id: 2, name: "穿透子弹", description: "可穿透敌人的子弹" },
    { id: 3, name: "爆炸子弹", description: "爆炸范围伤害" },
    { id: 4, name: "追踪子弹", description: "自动追踪目标" },
    // Add more bullet types as needed
];
// Helper function to check if EventGroupData file exists
function checkEventGroupFileExists(eventGroupName) {
    if (!eventGroupName)
        return false;
    const projectPath = Editor.Project.path;
    const emitterPath = (0, path_1.join)(projectPath, 'assets', 'resources', 'Game', 'emitter', 'events', 'Emitter', `${eventGroupName}.json`);
    const bulletPath = (0, path_1.join)(projectPath, 'assets', 'resources', 'Game', 'emitter', 'events', 'Bullet', `${eventGroupName}.json`);
    return (0, fs_1.existsSync)(emitterPath) || (0, fs_1.existsSync)(bulletPath);
}
// Helper function to create event group item element
function createEventGroupItem(eventGroupName, index) {
    const container = document.createElement('div');
    container.className = 'event-group-item';
    container.style.cssText = `
        display: flex;
        align-items: center;
        margin: 2px 0;
        padding: 4px;
        border: 1px solid ${checkEventGroupFileExists(eventGroupName) ? '#555' : '#ff4444'};
        border-radius: 3px;
        background: #2a2a2a;
    `;
    // Input field for event group name
    const input = document.createElement('input');
    input.type = 'text';
    input.value = eventGroupName;
    input.style.cssText = `
        flex: 1;
        margin-right: 4px;
        padding: 2px 4px;
        background: #1a1a1a;
        border: 1px solid #555;
        color: #fff;
        border-radius: 2px;
    `;
    input.addEventListener('input', () => {
        const newValue = input.value;
        // Update the dump data
        if (this.dump && this.dump.value.emitterData && this.dump.value.emitterData.value.eventGroupData) {
            this.dump.value.emitterData.value.eventGroupData.value[index] = newValue;
            // Update border color based on file existence
            container.style.borderColor = checkEventGroupFileExists(newValue) ? '#555' : '#ff4444';
            // Commit the change
            this.dump.value.emitterData.value.eventGroupData.dispatch('change');
        }
    });
    // Open button
    const openBtn = document.createElement('button');
    openBtn.textContent = '📂';
    openBtn.title = 'Open in Event Editor';
    openBtn.style.cssText = `
        margin-right: 4px;
        padding: 2px 6px;
        background: #4a90e2;
        border: none;
        color: white;
        border-radius: 2px;
        cursor: pointer;
        font-size: 12px;
    `;
    openBtn.addEventListener('click', () => {
        // Send message to open event editor panel with this event group
        Editor.Message.send('bullet_editor', 'open-event-editor', eventGroupName);
    });
    // Remove button
    const removeBtn = document.createElement('button');
    removeBtn.textContent = '✕';
    removeBtn.title = 'Remove Event Group';
    removeBtn.style.cssText = `
        padding: 2px 6px;
        background: #e74c3c;
        border: none;
        color: white;
        border-radius: 2px;
        cursor: pointer;
        font-size: 12px;
    `;
    removeBtn.addEventListener('click', () => {
        if (this.dump && this.dump.value.emitterData && this.dump.value.emitterData.value.eventGroupData) {
            // Remove from array
            this.dump.value.emitterData.value.eventGroupData.value.splice(index, 1);
            // Commit the change
            this.dump.value.emitterData.value.eventGroupData.dispatch('change');
            // Re-render the list
            renderEventGroupList.call(this);
        }
    });
    container.appendChild(input);
    container.appendChild(openBtn);
    container.appendChild(removeBtn);
    return container;
}
function update(dump) {
    this.dump = dump;
    this.eventGroupElements = [];
    // Render other emitter properties
    this.$.bulletID.render(dump.value.bulletID);
    this.$.emitterData.render(dump.value.emitterData);
    this.$.bulletData.render(dump.value.bulletData);
    // Render event group list
    renderEventGroupList.call(this);
}
function ready() {
    // Setup add event group button
    if (this.$.addEventGroupBtn) {
        this.$.addEventGroupBtn.addEventListener('click', () => {
            if (this.dump && this.dump.value.emitterData && this.dump.value.emitterData.value.eventGroupData) {
                // Add new empty event group
                this.dump.value.emitterData.value.eventGroupData.value.push('');
                // Commit the change
                this.dump.value.emitterData.value.eventGroupData.dispatch('change');
                // Re-render the list
                renderEventGroupList.call(this);
            }
        });
    }
}
// Add method to render event group list
function renderEventGroupList() {
    var _a, _b, _c, _d, _e;
    if (!this.$.eventGroupList)
        return;
    // Clear existing elements
    this.$.eventGroupList.innerHTML = '';
    this.eventGroupElements = [];
    // Get event group data
    const eventGroupData = (_e = (_d = (_c = (_b = (_a = this.dump) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.emitterData) === null || _c === void 0 ? void 0 : _c.value) === null || _d === void 0 ? void 0 : _d.eventGroupData) === null || _e === void 0 ? void 0 : _e.value;
    if (!eventGroupData || !Array.isArray(eventGroupData))
        return;
    // Create elements for each event group
    eventGroupData.forEach((eventGroupName, index) => {
        const element = createEventGroupItem.call(this, eventGroupName, index);
        this.eventGroupElements.push(element);
        this.$.eventGroupList.appendChild(element);
    });
}
//# sourceMappingURL=data:application/json;base64,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
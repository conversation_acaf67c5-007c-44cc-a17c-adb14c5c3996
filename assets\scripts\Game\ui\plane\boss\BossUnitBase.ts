import { _decorator, Component, Node, Vec2, tween, misc, UIOpacity, v2 } from 'cc';
import BossHurt from './BossHurt';
import { GameIns } from '../../../GameIns';
import { GameConst } from '../../../const/GameConst';
import Bullet from '../../bullet/Bullet';
import EnemyEffectLayer from '../../layer/EnemyEffectLayer';
import FCollider from '../../../collider-system/FCollider';

const { ccclass, property } = _decorator;

@ccclass('BossUnitBase')
export default class BossUnitBase extends BossHurt {
    owner: any = null;
    _curHp: number = 0;
    _maxHp: number = 0;
    defence: number = 0;
    blastParam: any[] = [];
    blastShake: any[] = [];
    _whiteNode: Node | null = null;
    _winkCount: number = 0;
    _bWinkWhite: boolean = false;
    _winkAct: any = null;

    initWinkWhite(whiteNode: Node): void {
        this._whiteNode = whiteNode;
        const opacityComp = this._whiteNode.getComponent(UIOpacity);
        if (!opacityComp) {
            this._whiteNode.addComponent(UIOpacity); // 确保组件存在
        }
        this._winkAct = tween()
            .to(0, { opacity: 255 })
            .to(3 * GameConst.ActionFrameTime, { opacity: 0 });
        if (this._whiteNode) {
            this._whiteNode.getComponent(UIOpacity)!.opacity = 0;
        }
    }

    setPropertyRate(rates: number[]): void {
        if (rates.length > 2) {
            this._curHp *= rates[0];
            this._maxHp = this._curHp;
            this.attack *= rates[1];
            this._collideAtk *= rates[2];
        }
    }

    get curHp(): number {
        return this._curHp;
    }

    set curHp(value: number) {
        this._curHp = value;
    }

    get maxHp(): number {
        return this._maxHp;
    }

    set maxHp(value: number) {
        this._maxHp = value;
    }


    getAngleToOwner(): number {
        let angle = 0;
        let parent = this.node.parent;
        while (parent && parent !== this.owner.node) {
            angle += parent.angle;
            parent = parent.parent;
        }
        return angle;
    }

    getScenePos(): Vec2 {
        let pos = new Vec2(this.node.position.x, this.node.position.y).rotate(
            misc.degreesToRadians(this.getAngleToOwner())
        );
        let parent = this.node.parent;
        let scaleX = 1;
        let scaleY = 1;

        while (parent && parent.name !== 'enemyPlane') {
            scaleX *= parent.scale.x;
            scaleY *= parent.scale.y;
            pos.x += parent.position.x;
            pos.y += parent.position.y;
            parent = parent.parent;
        }

        pos.x *= scaleX;
        pos.y *= scaleY;
        return pos;
    }

    updateGameLogic(deltaTime: number): void {
        if (!this.isDead) {
            this.m_comps.forEach((comp) => {
                comp.update(deltaTime);
            });

            if (this._bWinkWhite) {
                this._winkCount++;
                if (this._winkCount > 10) {
                    this._winkCount = 0;
                    this._bWinkWhite = false;
                }
            }
        }
    }

    hurt(damage: number): boolean {
        if (this.isDead || !this.active) {
            return false;
        }
        this.changeHp(-damage);
        this.onHurt();
        return true;
    }

    onHurt(): void {
        this.winkWhite();
    }

    changeHp(amount: number): void {
        let change = amount;
        let newHp = this._curHp + amount;

        if (newHp < 0) {
            change = -this._curHp;
        }

        this._curHp = newHp;
        if (this._curHp < 0) {
            this._curHp = 0;
        }

        this.onHpChange(change);
        this.checkHp();
    }

    onHpChange(change: number): void {
        if (this.owner && this.owner.hpChange) {
            this.owner.hpChange(change);
        }
    }

    checkHp(): boolean {
        if (this.curHp <= 0) {
            this.die();
            return true;
        }
        return false;
    }

    die(): void {
        if (!this.isDead) {
            this.isDead = true;
            this.onDie();
        }
    }

    onDie(): void {
        this.playDieAnim();
    }

    winkWhite(): void {
        if (!this._bWinkWhite) {
            this._bWinkWhite = true;
            if (this._whiteNode && this._winkAct) {
                this._winkAct.clone(this._whiteNode).start();
            }
        }
    }

    playDieAnim(): void {
        this.onDieAnimEnd()
    }

    onDieAnimEnd(): void {}

    getHpPercent(): number {
        return this.curHp / this.maxHp;
    }
}
import { JsonAsset, NodePool, resources, sp, Sprite, SpriteAtlas, Node} from "cc";
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameIns } from "../GameIns";
import { GameConst } from "../const/GameConst";
import BossBase from "../ui/plane/boss/BossBase";
import { Tools } from "../utils/Tools";
import { BossData, BossUnitData, UnitData } from "../data/BossData";
import BattleLayer from "../ui/layer/BattleLayer";
import BossEntity from "../ui/plane/boss/BossEntity";
import { GameFunc } from "../GameFunc";
import { MyApp } from "../../MyApp";
import GameResourceList from "../const/GameResourceList";


export class BossManager extends SingletonBase<BossManager>{
    _bossDatas: Map<number, BossData[]> = new Map();
    _unitDatas: Map<number, UnitData> = new Map();
    _bossUnitDatas: Map<number, BossUnitData> = new Map();
    _bossDataMap: Map<number, any> = new Map();
    bossAtlasMap: Map<string, SpriteAtlas> = new Map();
    unitAtlas: SpriteAtlas | null = null;
    _bossArr: BossBase[] = [];
    fireParticle: Node | null = null;
    skelDataMap: Map<string, sp.SkeletonData> = new Map();
    bossResFinish: boolean = false;
    _fireParticlePool: NodePool = new NodePool();
    _isBossWarning: boolean = false;
    _smokeSkelData: sp.SkeletonData | null = null;
    _mainStage: number = -1;
    _subStage: number = -1;
    m_bossResStage: number = 0;

    constructor() {
        super();
        this.initConfig();
    }

    initConfig(){
        let bossTbDatas = MyApp.lubanTables.TbBoss.getDataList();
        for (let bossTbData of bossTbDatas) {
            const bossData = new BossData();
            bossData.loadJson(bossTbData);
            let bossList = this._bossDatas.get(bossData.id);
            if (!bossList) {
                bossList = [];
                this._bossDatas.set(bossData.id, bossList);
            }
            bossList.push(bossData);
        }

        let unitTbDatas = MyApp.lubanTables.TbUnit.getDataList();
        for (let unitTbData of unitTbDatas) {
            const unitData = new UnitData();
            unitData.loadJson(unitTbData);
            this._unitDatas.set(unitData.id, unitData);
        }
    }


    async preLoad() {
        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.load(GameResourceList.spine_boss_smoke, sp.SkeletonData, (error: Error, data: sp.SkeletonData) => {
            this._smokeSkelData = data;
            GameIns.battleManager.checkLoadFinish();
        });

        GameIns.battleManager.addLoadCount(1);
        this.unitAtlas = await MyApp.resMgr.loadAsync(GameResourceList.atlas_boss_unit,SpriteAtlas);
        GameIns.battleManager.checkLoadFinish();
    }


    mainReset() {
        this.subReset();
        this._mainStage = -1;

        if (!GameConst.Cache) {
            if (this._smokeSkelData) {
                MyApp.resMgr.releaseAssetByForce(this._smokeSkelData);
                this._smokeSkelData = null;
            }
            if (this.unitAtlas) {
                MyApp.resMgr.releaseAssetByForce(this.unitAtlas);
                this.unitAtlas = null;
            }
        }
    }

    /**
     * 重置子关卡
     */
    subReset() {
        for (const boss of this._bossArr) {
            boss.willDestroy();
            boss.node.parent = null;
            setTimeout(() => {
                boss.node.destroy();
            }, 1000);
        }
        this._bossArr = [];

        // 清理骨骼动画资源
        this.skelDataMap.forEach((data, key) => {
            MyApp.resMgr.releaseAssetByForce(data);
        });
        this.skelDataMap.clear();

        // 清理图集资源
        this.bossAtlasMap.forEach((atlas, key) => {
            MyApp.resMgr.releaseAssetByForce(atlas);
        });
        this.bossAtlasMap.clear();
    }


    /**
     * 加载 Boss 资源
     * @param bossType Boss 类型
     * @param bossId Boss ID
     * @param callback 回调函数
     * @param isPreload 是否为预加载
     */
    async loadBossRes(bossType: number, bossId: number, callback: Function | null = null, isPreload: boolean = false): Promise<boolean> {
        try {
            Tools.warn("Loading Boss Resources", bossType, bossId);

            if (!isPreload) {
                this.bossResFinish = false;
                // GameFunc.showUILoadingForDelay(5);
            }

            switch (bossType) {
                case 100:
                    // 加载普通 Boss
                    const bossDatas = this.getBossDatas(bossId);
                    for (const bossData of bossDatas!) {
                        for (const unitId of bossData.units) {
                            const unitData = this.getUnitData(unitId)!;
                            if (unitData.anim) {
                                const skelData = await MyApp.resMgr.loadAsync("Game/spine/"+unitData.anim,sp.SkeletonData);
                                if (!skelData) {
                                    if (!isPreload) {
                                        // GameFunc.showUILoadErr(() => {
                                            this.loadBossRes(bossType, bossId, callback);
                                        // });
                                    }
                                    return false;
                                }
                                if (!isPreload) {
                                    this.skelDataMap.set(unitData.anim, skelData);
                                }
                            }
                        }
                    }
                    break;

                default:
                    Tools.error("Unknown Boss Type:", bossType);
                    return false;
            }

            if (!isPreload) {
                this.bossResFinish = true;
                // GameFunc.hideUILoading();
            }

            if (callback) {
                callback();
            }

            return true;
        } catch (error) {
            Tools.error("Error loading Boss resources:", error);
            if (!isPreload) {
                // GameFunc.showUILoadErr(() => {
                    this.loadBossRes(bossType, bossId, callback);
                // });
            }
            return false;
        }
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        for (let i = 0; i < this._bossArr.length; i++) {
            const boss = this._bossArr[i];
            if (boss.removeAble) {
                this.removeBoss(boss);
                i--;
            } else {
                boss.updateGameLogic(deltaTime);
            }
        }
    }

    /**
     * 开始 Boss 战斗
     */
    bossFightStart() {
        for (const boss of this._bossArr) {
            if (!boss.isDead) {
                boss.startBattle();
                break;
            }
        }
    }

    /**
     * 添加 Boss
     * @param bossType Boss 类型
     * @param bossId Boss ID
     */
    addBoss(bossType: number, bossId: number): BossBase | null {
        switch (bossType) {
            case 100:
                const bossDatas = this.getBossDatas(bossId)!;
                return this._createBoss(bossDatas);

            default:
                Tools.error("Unknown Boss Type:", bossType);
                return null;
        }
    }

    /**
     * 移除 Boss
     * @param boss 要移除的 Boss
     */
    removeBoss(boss: BossBase) {
        boss.node.y = 1000;
        Tools.arrRemove(this._bossArr, boss);
        boss.node.parent = null;
        boss.node.destroy();
    }

    /**
     * 获取 Boss 图集
     * @param atlasName 图集名称
     */
    async getBossAtlas(atlasName: string): Promise<SpriteAtlas | null> {
        let atlas = this.bossAtlasMap.get(atlasName);
        if (!atlas) {
            atlas = await MyApp.resMgr.loadAsync(atlasName, SpriteAtlas);
            if (atlas) {
                this.bossAtlasMap.set(atlasName, atlas);
            }
        }
        return atlas;
    }

    /**
     * 设置 Boss 的精灵帧
     * @param sprite 精灵组件
     * @param frameName 精灵帧名称
     */
    setBossFrame(sprite: Sprite, frameName: string) {
        if (sprite && this.unitAtlas) {
            sprite.spriteFrame = this.unitAtlas.getSpriteFrame(frameName);
        }
    }

    /**
     * 显示 Boss 警告
     */
    showBossWarning() {
        this._isBossWarning = true;
    }

    /**
     * 获取所有 Boss
     */
    get bosses(): BossBase[] {
        return this._bossArr;
    }

    /**
     * 检查是否所有 Boss 已结束
     */
    isBossOver(): boolean {
        return this._bossArr.length === 0;
    }

    /**
     * 检查是否所有 Boss 已死亡
     */
    isBossDead(): boolean {
        for (const boss of this._bossArr) {
            if (!boss.isDead) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取烟雾骨骼数据
     */
    get smokeSkelData(): sp.SkeletonData | null {
        return this._smokeSkelData;
    }


    /**
     * 获取单位数据
     * @param unitId 单位 ID
     */
    getUnitData(unitId: number): UnitData | undefined {
        return this._unitDatas.get(unitId);
    }

    /**
     * 获取 Boss 数据
     * @param bossId Boss ID
     */
    getBossDatas(bossId: number): BossData[] | undefined {
        return this._bossDatas.get(bossId);
    }

    /**
     * 根据 ID 创建 Boss
     * @param bossId Boss ID
     */
    createBossById(bossId: number): BossEntity | null {
        const bossDatas = this.getBossDatas(bossId);
        if (bossDatas) {
            return this._createBoss(bossDatas);
        }
        return null;
    }

    /**
     * 创建普通 Boss
     * @param bossDatas Boss 数据
     */
    _createBoss(bossDatas: BossData[]): BossEntity {
        const node = new Node("boss");
        BattleLayer.me.addEnemy(node);

        const boss = node.addComponent(BossEntity);
        boss.init(bossDatas);
        boss.new_uuid = GameFunc.uuid;
        this._bossArr.push(boss);

        return boss;
    }

    /**
     * 暂停所有 Boss
     */
    pauseBoss() {
        for (const boss of this._bossArr) {
            if (!boss.isDead && boss.pause) {
                boss.pause();
            }
        }
    }

    /**
     * 恢复所有 Boss
     */
    resumeBoss() {
        for (const boss of this._bossArr) {
            if (!boss.isDead && boss.resume) {
                boss.resume();
            }
        }
    }
}
import { _decorator, Component, instantiate, Node, Prefab, UITransform, view } from "cc";
import { LevelData, LevelDataLayer } from "../../../leveldata/leveldata";
import { LevelLayerUI } from "./LevelLayerUI";
import { MyApp } from "../../../MyApp";

const { ccclass, property } = _decorator;

const BackgroundsNodeName = "backgrounds";

@ccclass('LevelLayer')
class LevelLayer {
    public node: Node | null = null;
    public speed: number = 0;
}

@ccclass('LevelBackgroundLayer')
class LevelBackgroundLayer extends LevelLayer {
    public backgrounds: Prefab[] = [];
    public backgroundsNode: Node|null = null;
}

@ccclass('LevelBaseUI')
export class LevelBaseUI extends Component {
    private _curLevelIndex: number = 0; // 当前关卡索引
    private _totalTime: number = 10; // 当前关卡的时长
    private _preLevelHeight: number = 0; // 上一关的关卡高度
    private _preLevelOffsetY: number = 0; // 上一关的关卡偏移量
    
    private _backgroundLayerNode:Node|null = null;
    private _floorLayersNode:Node|null = null;
    private _skyLayersNode:Node|null = null;

    private _backgroundLayer: LevelBackgroundLayer | null = null;
    private _floorLayers: LevelLayer[] = [];
    private _skyLayers: LevelLayer[] = [];

    private _lastLevelUpdate: (() => void) | null = null; 

    public get floorLayers(): LevelLayer[] {
        return this._floorLayers;
    }
    public get skyLayers(): LevelLayer[] {
        return this._skyLayers;
    }
    public get backgroundLayer(): LevelBackgroundLayer | null {
        if (!this._backgroundLayer) {
            throw new Error("backgroundLayer is not initialized");
        }
        return this._backgroundLayer;
    }

    public get TotalTime(): number {
        return this._totalTime;
    }

    public getLevelTotalHeightByIndex(index: number): number {
        var totalHeight = 0;
        if (this._backgroundLayerNode) {
            const levelNode = this._backgroundLayerNode.getChildByName(`level_${index}`);
            if (levelNode) {
                var preBgNode = levelNode.getChildByName(`layer_${index}`);
                if (preBgNode) {
                    const backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);
                    if (backgroundsNode) {
                        backgroundsNode.children.forEach((bg) => {
                            var height = bg.getComponent(UITransform)!.contentSize.height;
                            totalHeight += height;
                        });
                    }
                }
            }
        }

        return totalHeight;
    }

    protected onLoad(): void {
    }

    private _getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }

    public async levelPrefab(levelData: LevelData, levelInfo:{ levelID: number, levelCount: number, levelIndex: number }, bFristLevel: boolean = false):Promise<void> {
        this._backgroundLayerNode = this._getOrAddNode(this.node, "BackgroundLayer");
        this._floorLayersNode = this._getOrAddNode(this.node, "FloorLayers");
        this._skyLayersNode = this._getOrAddNode(this.node, "SkyLayers");

        const floorIndex = this.node.children.indexOf(this._floorLayersNode);
        const battleNode = this.node.getChildByName("battle");
        if (battleNode) {
            battleNode.setSiblingIndex(floorIndex + 1);
        }

        if (bFristLevel) {
            await this._initByLevelData(levelData,levelInfo);
        } else {
            this._initByLevelData(levelData,levelInfo);
        }

        // 如果是最后一关，设置无限循环滚动逻辑
        if (levelInfo.levelIndex + 1 >= levelInfo.levelCount) {
            this._setupInfiniteScroll();
            this._setupLastLevelUpdate(); 
        }
    }

    public switchLevel(speed: number, time: number, levelIndex: number): void {
        this.backgroundLayer!.speed = speed;
        this._totalTime = time;
        this._curLevelIndex = levelIndex;

        if (levelIndex > 0) {
            // 释放上一关资源
            this._removeNode(this._backgroundLayerNode!, `level_${levelIndex - 1}`);
            this._removeNode(this._floorLayersNode!, `level_${levelIndex - 1}`);
            this._removeNode(this._skyLayersNode!, `level_${levelIndex - 1}`);
        }
    }

    private _removeNode(parentNode: Node, name: string): void {
        var node = parentNode.getChildByName(name);
        if (node) {
            node.removeFromParent();
        }
    }

    public async _initByLevelData(data: LevelData, levelInfo:{ levelID: number, levelCount: number, levelIndex: number }):Promise<void> {
        const levelBackground = this._getOrAddNode(this._backgroundLayerNode!, `level_${levelInfo.levelIndex}`);
        const levelFloor = this._getOrAddNode(this._floorLayersNode!, `level_${levelInfo.levelIndex}`);
        const levelSky = this._getOrAddNode(this._skyLayersNode!, `level_${levelInfo.levelIndex}`);
 
        await this._initBackgroundLayer(levelBackground, data, levelInfo);
        this._initLayers(levelFloor, this.floorLayers, data.floorLayers);
        this._initLayers(levelSky, this.skyLayers, data.skyLayers);
    }

    private _initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]): void {
        dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.node = this._addLayer(parentNode, `layer_${i}`).node;
            levelLayer!.node!.getComponent<LevelLayerUI>(LevelLayerUI)!.initByLevelData(layer,this._preLevelOffsetY);
            layers.push(levelLayer);
        });
    }

    private async _initBackgroundLayer(parentNode: Node, data: LevelData, levelInfo:{ levelID: number,levelCount: number, levelIndex: number }): Promise<void> {
        if (data.backgroundLayer.backgrounds.length > 0) { 
            if (this._backgroundLayer === null) {
                this._backgroundLayer = new LevelBackgroundLayer();
                this._backgroundLayer.backgrounds = [];
            }
            this._backgroundLayer.speed = data.backgroundLayer.speed;
            var bgCount = Math.ceil(data.totalTime * this._backgroundLayer.speed / 1280);
            if (levelInfo.levelIndex + 1 >= levelInfo.levelCount) {
                bgCount += 1; // 最后一关多一个背景层，防止最后一关背景层消失
            }
            const loadPromises = data.backgroundLayer.backgrounds.map((backgroundLayer) => {
                return new Promise<void>((resolve, reject) => {
                    const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, backgroundLayer);
                    MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                        if (err) {
                            console.error('LevelBaseUI', "initByLevelData load background prefab err", err);
                            reject(err);
                            return;
                        }
                        this._backgroundLayer!.backgrounds.push(prefab);
                        resolve();
                    });
                });
            });

            await Promise.all(loadPromises);
            // 节点设置偏移
            var offsetY = 0;
            this._preLevelHeight = 0;
            if (this.backgroundLayer) {
                const preLevel = this._backgroundLayerNode!.getChildByName(`level_${levelInfo.levelIndex - 1}`);
                if (preLevel) {
                    const preBgNode = preLevel.getChildByName(`layer_${levelInfo.levelIndex - 1}`);
                    if (preBgNode) {
                        offsetY = preBgNode.getPosition().y;
                        const backgroundsNode = preBgNode.getChildByName(BackgroundsNodeName);
                        if (backgroundsNode) {
                            backgroundsNode.children.forEach((bg) => {
                                const height = bg.getComponent(UITransform)!.contentSize.height;
                                this._preLevelHeight += height;
                            });
                        }
                    }
                }
            }

            this._preLevelOffsetY = this._preLevelHeight + offsetY;
            console.log('LevelBaseUI', "_initBackgroundLayer _preLevelHeight", this._preLevelHeight, "offsetY", offsetY);

            this.backgroundLayer!.node = this._addLayer(parentNode, `layer_${levelInfo.levelIndex}`).node;
            this.backgroundLayer!.backgroundsNode = this._getOrAddNode(this.backgroundLayer!.node, BackgroundsNodeName);
            this.backgroundLayer!.node.getComponent<LevelLayerUI>(LevelLayerUI)!.initByLevelData(data.backgroundLayer, this._preLevelOffsetY);
            this.backgroundLayer!.backgroundsNode.setSiblingIndex(0);

            var pos = 0;
            while (this._backgroundLayer.backgrounds.length > 0 && bgCount > this._backgroundLayer.backgroundsNode!.children.length) {
                var bg = instantiate(this._backgroundLayer.backgrounds[this._backgroundLayer.backgroundsNode!.children.length % this._backgroundLayer.backgrounds.length]);
                const height = bg.getComponent(UITransform)!.contentSize.height;
                
                bg.setPosition(0, pos, 0);
                pos += height;
                this._backgroundLayer.backgroundsNode!.addChild(bg);
            }
        }
    }

    private _addLayer(parentNode: Node, name: string): LevelLayerUI {
        var layerNode = new Node(name);
        var layerCom = layerNode.addComponent<LevelLayerUI>(LevelLayerUI);
        parentNode.addChild(layerNode);
        return layerCom;
    }

    public tick(deltaTime: number): void {
        this._backgroundLayerNode!.children.forEach((node) => {
            node.children.forEach((child) => {
                const layerUI = child.getComponent<LevelLayerUI>(LevelLayerUI);
                if (layerUI) {
                    layerUI.tick(deltaTime, this.backgroundLayer!.speed);
                }
            });
        });

        this.floorLayers.forEach((layer) => {
            const layerUI = layer.node?.getComponent<LevelLayerUI>(LevelLayerUI);
            if (layerUI) {
                if (layerUI.TrackBackground) {
                    layerUI.tick(deltaTime, this.backgroundLayer!.speed);
                } else {
                    layerUI.tick(deltaTime, layer.speed);
                }
            }
        });
        this.skyLayers.forEach((layer) => {
            const layerUI = layer.node?.getComponent<LevelLayerUI>(LevelLayerUI);
                if (layerUI) {
                if (layerUI.TrackBackground) {
                    layerUI.tick(deltaTime, this.backgroundLayer!.speed);
                } else {
                    layerUI.tick(deltaTime, layer.speed);
                }
            }
        });
    }

    private _setupInfiniteScroll(): void {
         if (!this._backgroundLayerNode) return;

        this.schedule(() => {
            const lastLevelNode = this._backgroundLayerNode!.getChildByName(`level_${this._curLevelIndex}`);
            if (!lastLevelNode) return;

            const lastBgNode = lastLevelNode.getChildByName(`layer_${this._curLevelIndex}`);
            if (!lastBgNode) return;

            const bgPosY = lastBgNode.getPosition().y;
            const screenHeight = view.getVisibleSize().height;

            // 当原始节点完全滚出屏幕时，重置其位置到副本下方，并移除副本
            if (bgPosY < -screenHeight * 2) {
                lastBgNode.setPosition(0, bgPosY + screenHeight * 2, 0);
            }
        }, 0.1);
    }

    private _setupLastLevelUpdate(): void {
        this._lastLevelUpdate = () => {
            this.tick(0.016); 
        };

        this.schedule(this._lastLevelUpdate, 0.016);
    }

    protected onDestroy(): void {
        this.unschedule(this._setupInfiniteScroll);
        if (this._lastLevelUpdate) {
            this.unschedule(this._lastLevelUpdate);
        }
    }
}
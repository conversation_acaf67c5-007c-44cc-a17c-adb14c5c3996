import { _decorator, instantiate } from "cc";
import Entity from "../../base/Entity";


const { ccclass, property } = _decorator;

@ccclass('EnemyEntity')
export default class EnemyEntity extends Entity {

    _type = 0; // 敌人类型
    _isDead = false; // 是否死亡
    _active = false; // 是否激活
    _removeAble = false; // 是否可移除
    _attack = 0; // 攻击力
    _collideAtk = 0; // 碰撞攻击力
    _sceneLayer = -1; // 场景层


    /**
     * 重置敌人状态
     */
    reset() {
        this._isDead = false;
        this._active = false;
        this._removeAble = false;
        this._sceneLayer = -1;
    }

    /**
     * 获取敌人类型
     * @returns {number} 敌人类型
     */
    get type() {
        return this._type;
    }

    /**
     * 设置敌人类型
     * @param {number} value 敌人类型
     */
    set type(value) {
        this._type = value;
    }

    /**
     * 获取攻击力
     * @returns {number} 攻击力
     */
    get attack() {
        return this._attack;
    }

    /**
     * 设置攻击力
     * @param {number} value 攻击力
     */
    set attack(value) {
        this._attack = value;
    }

    /**
     * 设置碰撞攻击力
     * @param {number} value 碰撞攻击力
     */
    setCollideAtk(value: number) {
        this._collideAtk = value;
    }

    /**
     * 获取碰撞攻击力
     * @returns {number} 碰撞攻击力
     */
    getColliderAtk() {
        return this._collideAtk;
    }

    /**
     * 获取碰撞攻击力
     * @returns {number} 碰撞攻击力
     */
    get collideAtk() {
        return this._collideAtk;
    }

    /**
     * 设置碰撞攻击力
     * @param {number} value 碰撞攻击力
     */
    set collideAtk(value) {
        this._collideAtk = value;
    }

    /**
     * 获取是否死亡
     * @returns {boolean} 是否死亡
     */
    get isDead() {
        return this._isDead;
    }

    /**
     * 设置是否死亡
     * @param {boolean} value 是否死亡
     */
    set isDead(value) {
        this._isDead = value;
    }

    /**
     * 获取是否可移除
     * @returns {boolean} 是否可移除
     */
    get removeAble() {
        return this._removeAble;
    }

    /**
     * 设置是否可移除
     * @param {boolean} value 是否可移除
     */
    set removeAble(value) {
        this._removeAble = value;
    }

    /**
     * 获取是否激活
     * @returns {boolean} 是否激活
     */
    get active() {
        return this._active;
    }

    /**
     * 设置是否激活
     * @param {boolean} value 是否激活
     */
    set active(value) {
        this._active = value;
    }

    /**
     * 获取场景层
     * @returns {number} 场景层
     */
    get sceneLayer() {
        return this._sceneLayer;
    }

    /**
     * 设置场景层
     * @param {number} value 场景层
     */
    set sceneLayer(value) {
        this._sceneLayer = value;
    }

    /**
     * 处理敌人受到的伤害
     * @param {number} damage 伤害值
     */
    hurt(damage: number) {}

    /**
     * 敌人即将销毁时的回调
     */
    willDestroy() {}

}
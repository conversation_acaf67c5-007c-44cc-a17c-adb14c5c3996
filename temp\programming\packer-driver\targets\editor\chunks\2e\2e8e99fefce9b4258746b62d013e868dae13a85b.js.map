{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/mail/MailCellUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "Component", "Label", "resources", "Sprite", "SpriteFrame", "MyApp", "UIMgr", "PopupUI", "<PERSON>", "ccclass", "property", "MailCellUI", "itemID", "start", "id", "fromString", "lte", "update", "deltaTime", "onButtonClick", "openUI", "setData", "item", "lubanTables", "TbItem", "get", "mailTitle", "string", "name", "mailContent", "load", "err", "spriteFrame", "mailIcon"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAqBC,MAAAA,W,OAAAA,W;;AAC5EC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,I;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;4BAGjBa,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACP,MAAD,C,UAGRO,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ,CAACX,MAAD,C,2BAZb,MACaY,UADb,SACgCX,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eActCY,MAdsC,GAcd,IAdc;AAAA;;AAgBtCC,QAAAA,KAAK,GAAG;AACJ,gBAAMC,EAAE,GAAG;AAAA;AAAA,4BAAKC,UAAL,CAAgB,YAAhB,CAAX,CADI,CACsC;;AAC1C,cAAID,EAAE,CAACE,GAAH,CAAO,CAAP,CAAJ,EAAe,CAEd,CAFD,MAEO,CAEN;AACJ;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AACDC,QAAAA,aAAa,GAAG;AACZ;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,kCAAsB,UAAU,KAAKR,MAArC;AACH;;AACMS,QAAAA,OAAO,CAACT,MAAD,EAAuB;AACjC,eAAKA,MAAL,GAAcA,MAAd;AACA,cAAIU,IAAyB,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,GAAzB,CAA6Bb,MAA7B,CAAhC;AACA,eAAKc,SAAL,CAAgBC,MAAhB,GAAyB,CAAAL,IAAI,QAAJ,YAAAA,IAAI,CAAEM,IAAN,KAAc,EAAvC;AACA,eAAKC,WAAL,CAAkBF,MAAlB,GAA2B,CAAAL,IAAI,QAAJ,YAAAA,IAAI,CAAEM,IAAN,KAAc,EAAzC;AACA1B,UAAAA,SAAS,CAAC4B,IAAV,CAAgB,iCAAhB,EAAkD1B,WAAlD,EAA+D,CAAC2B,GAAD,EAAMC,WAAN,KAAsB;AACjF,iBAAKC,QAAL,CAAeD,WAAf,GAA6BA,WAA7B;AACH,WAFD;AAIH;;AAxCqC,O;;;;;iBAGZ,I;;;;;;;iBAGA,I;;;;;;;iBAGE,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Button, Component, Label, Node, resources, Sprite, SpriteAtlas, SpriteFrame } from 'cc';\r\nimport { MyApp } from '../../../MyApp';\r\nimport { ResItem } from '../../../AutoGen/Luban/schema';\r\nimport { UIMgr } from '../../UIMgr';\r\nimport { PopupUI } from '../PopupUI';\r\nimport Long from 'long';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MailCellUI')\r\nexport class MailCellUI extends Component {\r\n\r\n    @property(Sprite)\r\n    mailIcon: Sprite | null = null;\r\n\r\n    @property(Label)\r\n    mailTitle: Label | null = null;\r\n\r\n    @property(Label)\r\n    mailContent: Label | null = null;\r\n\r\n    @property(Button)\r\n    btnClick: Button | null = null;\r\n\r\n    itemID: number | null = null;\r\n\r\n    start() {\r\n        const id = Long.fromString(\"1234567890\"); // 从字符串创建\r\n        if (id.lte(0)) {\r\n\r\n        } else {\r\n\r\n        }\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n    onButtonClick() {\r\n        UIMgr.openUI(PopupUI, '物品ID：' + this.itemID);\r\n    }\r\n    public setData(itemID: number): void {\r\n        this.itemID = itemID;\r\n        let item: ResItem | undefined = MyApp.lubanTables.TbItem.get(itemID);\r\n        this.mailTitle!.string = item?.name || \"\";\r\n        this.mailContent!.string = item?.name || \"\";\r\n        resources.load(`Game/texture/common/common/ag_1`, SpriteFrame, (err, spriteFrame) => {\r\n            this.mailIcon!.spriteFrame = spriteFrame;\r\n        });\r\n\r\n    }\r\n}\r\n\r\n\r\n"]}
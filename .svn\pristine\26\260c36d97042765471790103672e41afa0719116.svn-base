import { _decorator, Sprite, game, size, UITransform, UIOpacity, Vec2 } from 'cc';
import { GameIns } from '../../GameIns';
import { Tools } from '../../utils/Tools';
import Entity from '../base/Entity';
import AngleComp from '../base/AngleComp';
import BulletFly from './BulletFly';
import { GameFunc } from '../../GameFunc';
import { MainPlane } from '../plane/mainPlane/MainPlane';
import { GameConst } from '../../const/GameConst';
import CircleZoomFly from './CircleZoomFly';
import FBoxCollider from '../../collider-system/FBoxCollider';
import FCollider, { ColliderGroupType } from '../../collider-system/FCollider';
import { Bullet as BulletConfig} from 'db://assets/scripts/AutoGen/Luban/schema';


const { ccclass, property } = _decorator;

export interface IBulletState {
    attack: number;
    through: boolean;
    cirt: number[];
    atkChallenge: number;
}

@ccclass('Bullet')
export default class Bullet extends Entity {
    static PrefabName = "Bullet";

    @property(Sprite)
    skinImg: Sprite | null = null;


    enemy = false;
    bulletID = 1;
    m_collideComp:FBoxCollider | null = null;
    isCirt = false;
    m_createTime = 0;
    m_lifeTime = 0;
    aliveTime = 0;
    m_fireTween = null;
    _catapultCount = 0;
    _catapultAtkRatio = 1;
    _collideEntity = null;
    _catapultTargets: Entity[] = [];
    m_throughArr = [];
    m_config: BulletConfig | null = null;
    m_mainEntity: any;
    bulletState: IBulletState = {
        attack: 0,
        through: false,
        cirt: [],
        atkChallenge: 0
    };

    /**
     * 初始化子弹
     * @param {number} bulletID 子弹的唯一标识符
     */
    create(bulletID:number) {
        this.bulletID = bulletID;
        this.m_config = GameIns.bulletManager.getConfig(this.bulletID) as BulletConfig;
        this.removeAllComp();

        // 添加子弹飞行组件（如旋转角度）
        if (this.m_config.angleSpeed && this.m_config.angleSpeed !== 0) {
            this.addComp('AngleComp', new AngleComp(this.m_config ,this.m_config.bustyle));
        }
        switch (this.m_config.bustyle) {
            case 25:
                this.addComp('CircleZoomFly', new CircleZoomFly(this.m_config));
                break;

            default:
                this.addComp('BulletFly', new BulletFly(this.m_config));
        }

        // 添加碰撞组件
        this.m_collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.m_collideComp!.init(this,size(40,40)); // 初始化碰撞组件
        this.m_collideComp!.groupType = this.enemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;
        this.m_collideComp!.isEnable = true;

        // 设置子弹皮肤
        this.setSkin();

        // 设置子弹缩放
        this.node.setScale(this.m_config.scale, this.m_config.scale);
    }
    /**
 * 设置子弹的皮肤
 */
    async setSkin() {
        // 移除旧的子弹特效节点
        Tools.removeChildByName(this.skinImg!.node!.parent!, 'fist');
        Tools.removeChildByName(this.skinImg!.node!.parent!, 'gatlin');
        Tools.removeChildByName(this.skinImg!.node!.parent!, 'tail');

        // 重置子弹透明度
        
        var color = this.skinImg!.node!.getComponent(Sprite)!.color;
        this.skinImg!.node!.getComponent(Sprite)!.color.set(color.r, color.g, color.b, 255);

        // 设置子弹的默认皮肤
        this.skinImg!.spriteFrame = null;

        if (this.bulletID > 1000) {
            // 敌方子弹
            GameFunc.setImage(this.skinImg!, this.m_config!.image, GameIns.bulletManager.enemyBulletAtlas!);
        } else {
            // 主角子弹
            GameFunc.setImage(this.skinImg!, this.m_config!.image, GameIns.bulletManager.mainBulletAtlas!);
        }

        // 检查图片是否加载成功
        if (!this.skinImg!.spriteFrame) {
            Tools.error('Bullet image error:', this.m_config!.image);
        }
    }
    //     /**
    //  * 初始化跟踪子弹
    //  * @param {boolean} isEnemy 是否为敌方子弹
    //  * @param {Object} position 子弹的初始位置
    //  * @param {number} attack 子弹的攻击力
    //  */
    //     initFollow(isEnemy, position, attack) {
    //         this.init(isEnemy, position, {
    //             attack: attack,
    //             through: false,
    //         });
    //     }

    /**
     * 初始化子弹
     * @param {boolean} isEnemy 是否为敌方子弹
     * @param {Object} position 子弹的初始位置
     * @param {Object} state 子弹的状态
     * @param {Entity} mainEntity 子弹的发射实体
     */
    init(isEnemy:boolean, position: Vec2, state: IBulletState, mainEntity = null) {

        this.m_mainEntity = mainEntity;
        this.node.setPosition(position.x, position.y);
        this.skinImg!.node.angle = 0;
        this.m_lifeTime = this.m_config!.retrieve;

        if (this.m_lifeTime > 0) {
            this.m_createTime = game.totalTime;
        }

        this.node.angle = isEnemy ? (180 - Number(position.angle)) : -position.angle;
        this.enemy = isEnemy;

        if (state) {
            state.through = this.m_config!.disappear === 1;
        }

        this.bulletState = state;


        // 初始化子弹的所有组件
        this.m_comps.forEach((comp) => {
            comp.init(this);
        });

        // 设置子弹飞行组件
        const bulletFlyComp = this.getComp('BulletFly');
        if (bulletFlyComp) {
            bulletFlyComp.setData(-position.angle, this.m_mainEntity, this.enemy);
        }

        this.m_collideComp!.init(this,size(this.m_config!.body, this.m_config!.body));
    }
    /**
     * 获取子弹的基础攻击力
     * @returns {number} 子弹的攻击力
     */
    _getAttack() {
        const randomValue = Math.random();
        const critChance = this.bulletState.cirt ? this.bulletState.cirt[0] : 0;
        const atkChallenge = this.bulletState.atkChallenge || 0;
        let attack = this.bulletState.attack;

        // 特殊处理主机类型子弹
        if (this.m_mainEntity instanceof MainPlane) {
            const config = this.m_mainEntity.m_config;
            if (config && config.type === 702) {
                if ([27, 28, 29, 37].indexOf(this.getType()) != -1) {
                    attack /= 5;
                }
            }
        }

        // 判断是否暴击
        if (randomValue <= critChance) {
            const critMultiplier = this.bulletState.cirt ? this.bulletState.cirt[1] : 1;
            this.isCirt = true;
            return (attack + atkChallenge) * critMultiplier;
        } else {
            this.isCirt = false;
            return attack + atkChallenge;
        }
    }

    /**
     * 获取子弹对目标的实际攻击力
     * @param {Entity} target 目标实体
     * @returns {number} 实际攻击力
     */
    getAttack(target: Entity) {
        let attack = this._getAttack();
        return attack;
    }

    /**
     * 获取子弹的类型
     * @returns {number} 子弹的类型
     */
    getType() {
        return this.m_config!.bustyle;
    }
    /**
     * 播放子弹命中音效
     */
    playHurtAudio() {
        // if (this.m_config.hit.length > 0) {
        //     Bullet.playAudio('hit2');
        // }
    }

    onCollide(collider:FCollider) {
        // if (this.getComp(ResistBulletComp)) {
        //     this.getComp(ResistBulletComp).onCollide(target);
        // }

        // if (this.getComp(OnceCollideComp)) {
        //     if (this.getComp(OnceCollideComp).onCollide(target)) {
        //         this.m_throughArr.push(target);
        //     }
        // } else 

        this.remove(true);
        if (this.m_config!.exstyle1) {
            let worldPos = collider.entity!.node!.parent!.getComponent(UITransform)!.convertToWorldSpaceAR(collider!.entity!.node.position);
            GameIns.hurtEffectManager.createHurtEffect(worldPos, this.m_config!.exstyle1, parseFloat(this.m_config!.exstyle2));
        }
    }

    /**
     * 子弹超出屏幕处理
     */
    onOutScreen() {
        if (this.m_lifeTime > 0) {
            const currentTime = game.totalTime;
            this.aliveTime = (currentTime - this.m_createTime) / 1000;
        }

        if (this.aliveTime >= this.m_lifeTime) {
            this.remove();
        }
    }

    /**
     * 移除子弹
     * @param {boolean} force 是否强制移除
     */
    remove(force = false) {
        this.willRemove();
        GameIns.bulletManager.removeBullet(this);
        this.m_mainEntity = null;
    }

    /**
     * 子弹死亡移除
     */
    dieRemove() {
        this.willRemove();

        try {
            GameIns.hurtEffectManager.playBulletDieAnim(this.node.position);
        } catch (error) {
            console.error('Error during dieRemove:', error);
        }

        GameIns.bulletManager.removeBullet(this, false);
        this.m_mainEntity = null;
    }

    /**
     * 子弹移除前的清理操作
     */
    willRemove() {
        if (this.m_collideComp) {
            this.m_collideComp.isEnable = false;
        }

        if (this.skinImg) {
            this.skinImg.spriteFrame = null;
        }

        this.m_throughArr.splice(0);
    }

    /**
     * 更新子弹逻辑
     * @param {number} deltaTime 帧间隔时间
     */
    update(deltaTime: number) {
        if (!GameConst.GameAble) {
            return;
        }

        this.m_comps.forEach((comp) => {
            comp.update(deltaTime);
        });

        if (this.m_config!.bustyle === 55 && this.node.y < -2000) {
            this.remove();
        }
    }

    /**
     * 更新子弹的组件逻辑
     * @param {number} deltaTime 帧间隔时间
     */
    updateComp(deltaTime:number) {
        this.m_comps.forEach((comp) => {
            if (comp.updateComp) {
                comp.updateComp(deltaTime);
            }
        });
    }

    /**
     * 刷新子弹状态
     */
    refresh() {
        this._catapultCount = 0;
        this._catapultAtkRatio = 1;
        this._collideEntity = null;
        this._catapultTargets.splice(0);
    }

    /**
     * 检查子弹是否可以与目标碰撞
     * @param {Entity} target 碰撞目标
     * @returns {boolean} 是否可以碰撞
     */
    canCollideEntity(target:Entity) {
        return (
            this._catapultTargets.length === 0 ||
            this._catapultTargets.indexOf(target) < 0
        );
    }


    /**
     * 子弹音效的最大播放数量
     */
    static audioMaxNum = {
        hit1: 5,
        hit2: 3,
        hit3: 2,
        hit4: 5,
        hit5: 5,
        hit6: 5,
        swordHit: 5,
    };

    //     /**
    //      * 子弹音效的播放状态
    //      */
    //     static audios = new Map();

    //     /**
    //      * 播放子弹音效
    //      * @param {string} audioName 音效名称
    //      */
    //     static playAudio(audioName) {
    //         const maxNum = Bullet.audioMaxNum[audioName];
    //         let currentNum = Bullet.audios.get(audioName) || 0;

    //         if (currentNum < maxNum) {
    //             Bullet.audios.set(audioName, ++currentNum);
    //             // const audioId = GameIns.audioManager.playEffectSync(audioName);
    //             // audioEngine.setFinishCallback(audioId, () => {
    //             //     Bullet.onAudioFinish(audioName);
    //             // });
    //         }
    //     }

    //     /**
    //      * 子弹音效播放完成的回调
    //      * @param {string} audioName 音效名称
    //      */
    //     static onAudioFinish(audioName) {
    //         const currentNum = Bullet.audios.get(audioName);
    //         Bullet.audios.set(audioName, Math.max(0, currentNum - 1));
    //     }
}
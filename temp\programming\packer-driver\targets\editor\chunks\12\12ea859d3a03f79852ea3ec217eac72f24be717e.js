System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, AudioSource, clamp01, IMgr, MyApp, audioManager, _crd, ccclass, property;

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../MyApp", _context.meta, extras);
  }

  _export("audioManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      AudioSource = _cc.AudioSource;
      clamp01 = _cc.clamp01;
    }, function (_unresolved_2) {
      IMgr = _unresolved_2.IMgr;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4ac214oaE9NeL4vcrQpH6oB", "audioManager", undefined);

      __checkObsolete__(['_decorator', 'AudioClip', 'AudioSource', 'Component', 'assert', 'warn', 'clamp01', 'resources']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("audioManager", audioManager = class audioManager extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        static get instance() {
          if (this._instance) {
            return this._instance;
          }

          this._instance = new audioManager();
          return this._instance;
        }
        /**管理器初始化*/


        init() {
          var audioSource = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).GetInstance().node.getComponent(AudioSource);
          audioManager._audioSource = audioSource;
        }
        /**
        * 播放音乐
        * @param {Boolean} loop 是否循环播放
        */


        playMusic(loop) {
          const audioSource = audioManager._audioSource;

          if (!audioSource) {
            return;
          }

          audioSource.loop = loop;

          if (!audioSource.playing) {
            audioSource.play();
          }
        }
        /**
        * 播放音效
        * @param {audioClip} audioClip 音效名称
        * @param {Number} volumeScale 播放音量倍数
        */


        playSound(audioClip, volumeScale = 1) {
          const audioSource = audioManager._audioSource;

          if (!audioSource) {
            return;
          } // 注意：第二个参数 “volumeScale” 是指播放音量的倍数，最终播放的音量为 “audioSource.volume * volumeScale”


          audioSource.playOneShot(audioClip, volumeScale);
        } // 设置音乐音量


        setMusicVolume(flag) {
          const audioSource = audioManager._audioSource;

          if (!audioSource) {
            return;
          }

          flag = clamp01(flag);
          audioSource.volume = flag;
        }

      });

      audioManager._instance = void 0;
      audioManager._audioSource = void 0;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=12ea859d3a03f79852ea3ec217eac72f24be717e.js.map
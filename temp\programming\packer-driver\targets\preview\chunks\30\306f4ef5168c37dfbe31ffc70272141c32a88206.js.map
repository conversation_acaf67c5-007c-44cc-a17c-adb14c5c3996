{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts"], "names": ["WaveManager", "warn", "SingletonBase", "GameConst", "EnemyWave", "GameIns", "Tools", "BossBase", "MyApp", "enemyCreateAble", "_bEnemyCreateAble", "value", "constructor", "_waveNorDatasMap", "Map", "_enemyOver", "_bEnemyNorCreateAble", "_enemyActions", "_enemyActionIndex", "_enemyCreateTime", "_curEnemyAction", "_waveCreateTime", "_waveIndexOver", "_waveIndex", "_waveArr", "_waveNumArr", "_waveTimeArr", "_waveActionArr", "_boss<PERSON><PERSON><PERSON><PERSON><PERSON>", "_bossCreateTime", "_bossToAddArr", "_bShowBossWarning", "initConfig", "waveDatas", "lubanTables", "TbWave", "getDataList", "waveData", "wave", "loadJson", "group", "get", "enemyGroupID", "push", "set", "reset", "splice", "setEnemyActions", "actions", "gameStart", "getNorWaveDatas", "groupID", "addWaveByLevel", "posX", "posY", "enemyWave", "fromLevelWave", "updateGameLogic", "deltaTime", "_updateCurAction", "_updateEnemy", "_updateBoss", "length", "action", "type", "enemyNorInterval", "enemyManager", "getNormalPlaneCount", "console", "enemyNorIDs", "boss<PERSON><PERSON><PERSON>", "loadBossRes", "_updateEnemyCreate", "_updateNorEnemys", "i", "currentEnemyCount", "EnemyPos", "x", "y", "bSetStartPos", "startPosX", "startPosY", "expPerEnemy", "Math", "floor", "exp", "enemyNum", "j", "enemyInterval", "enemy", "enemyPosX", "posDX", "enemyPosY", "posDY", "addPlane", "enemyID", "firstShootDelay", "setFirstShoot<PERSON>elay", "setStandByTime", "setExp", "initPropertyRate", "enemyNorRate", "initTrack", "trackGroups", "liveParam", "rotateSpeed", "waveID", "log", "arrC<PERSON>ain", "groupInterval", "isEnemyOver", "battleManager", "bossWillEnter", "boss<PERSON><PERSON><PERSON><PERSON>sh", "bossData", "boss", "addBoss", "setPropertyRate", "setTip", "stageManager", "getBossTips"], "mappings": ";;;2JAaqBA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAbbC,MAAAA,I,OAAAA,I;;AACCC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;;;;;;;;yBAMYR,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAqD;AA2B7C,YAAfS,eAAe,GAAY;AAC3B,iBAAO,KAAKC,iBAAZ;AACH;;AAEkB,YAAfD,eAAe,CAACE,KAAD,EAAiB;AAChC,eAAKD,iBAAL,GAAyBC,KAAzB;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV,kBADU,CAEV;;AAFU,eAlCNC,gBAkCM,GAlCuC,IAAIC,GAAJ,EAkCvC;AAlCiD;AAkCjD,eAjCNC,UAiCM,GAjCgB,KAiChB;AAjCsB;AAiCtB,eA/BNL,iBA+BM,GA/BuB,KA+BvB;AA/B6B;AA+B7B,eA9BNM,oBA8BM,GA9B0B,KA8B1B;AA9BgC;AA8BhC,eA5BNC,aA4BM,GA5B8B,IA4B9B;AA5BmC;AA4BnC,eA3BNC,iBA2BM,GA3BsB,CA2BtB;AA3BwB;AA2BxB,eA1BNC,gBA0BM,GA1BqB,CA0BrB;AA1BuB;AA0BvB,eAzBNC,eAyBM,GAzB8B,IAyB9B;AAzBmC;AAyBnC,eAxBNC,eAwBM,GAxBoB,CAwBpB;AAAA,eAvBNC,cAuBM,GAvBqB,EAuBrB;AAAA,eArBNC,UAqBM,GArBe,CAqBf;AArBiB;AAqBjB,eApBNC,QAoBM,GApBkB,EAoBlB;AApBqB;AAoBrB,eAnBNC,WAmBM,GAnBkB,EAmBlB;AAnBqB;AAmBrB,eAlBNC,YAkBM,GAlBmB,EAkBnB;AAlBsB;AAkBtB,eAjBNC,cAiBM,GAjBkB,EAiBlB;AAfd;AAec,eAdNC,gBAcM,GAdqB,CAcrB;AAAA,eAbNC,eAaM,GAboB,CAapB;AAAA,eAZNC,aAYM,GAZiB,EAYjB;AAAA,eAXNC,iBAWM,GAXuB,KAWvB;AAGb;;AAEDC,QAAAA,UAAU,GAAG;AACT,cAAIC,SAAS,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,WAAzB,EAAhB;;AACA,eAAK,IAAIC,QAAT,IAAqBJ,SAArB,EAAgC;AAC5B,gBAAMK,IAAI,GAAG;AAAA;AAAA,yCAAb;AACAA,YAAAA,IAAI,CAACC,QAAL,CAAcF,QAAd;AACA,gBAAMG,KAAK,GAAG,KAAK3B,gBAAL,CAAsB4B,GAAtB,CAA0BH,IAAI,CAACI,YAA/B,KAAgD,EAA9D;AACAF,YAAAA,KAAK,CAACG,IAAN,CAAWL,IAAX;;AACA,iBAAKzB,gBAAL,CAAsB+B,GAAtB,CAA0BN,IAAI,CAACI,YAA/B,EAA6CF,KAA7C;AACH;AACJ;;AAEDK,QAAAA,KAAK,GAAS;AACV,eAAK9B,UAAL,GAAkB,KAAlB;AACA,eAAKE,aAAL,GAAqB,EAArB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKT,iBAAL,GAAyB,KAAzB;AACA,eAAKM,oBAAL,GAA4B,KAA5B;AACA,eAAKO,UAAL,GAAkB,CAAlB;AACA,eAAKF,eAAL,GAAuB,CAAvB;;AACA,eAAKC,cAAL,CAAoBwB,MAApB,CAA2B,CAA3B;;AACA,eAAK1B,eAAL,GAAuB,IAAvB;AACA,eAAKI,QAAL,GAAgB,EAAhB;AACA,eAAKG,cAAL,GAAsB,EAAtB;AACA,eAAKF,WAAL,GAAmB,EAAnB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKK,iBAAL,GAAyB,KAAzB;AACA,eAAKF,eAAL,GAAuB,CAAvB;AACH;;AAEDkB,QAAAA,eAAe,CAACC,OAAD,EAA6B;AACxC,eAAK/B,aAAL,GAAqB+B,OAArB;AACH;;AAEDC,QAAAA,SAAS,GAAS;AACd,eAAKvC,iBAAL,GAAyB,IAAzB;AACA,eAAKM,oBAAL,GAA4B,IAA5B;AACA,eAAKQ,QAAL,GAAgB,EAAhB;AACA,eAAKG,cAAL,GAAsB,EAAtB;AACA,eAAKF,WAAL,GAAmB,EAAnB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACH;;AAEDwB,QAAAA,eAAe,CAACC,OAAD,EAA2C;AACtD,iBAAO,KAAKtC,gBAAL,CAAsB4B,GAAtB,CAA0BU,OAA1B,CAAP;AACH;;AAGDC,QAAAA,cAAc,CAACd,IAAD,EAAae,IAAb,EAA2BC,IAA3B,EAA8C;AACxD,cAAMC,SAAS,GAAG;AAAA;AAAA,sCAAUC,aAAV,CAAwBlB,IAAxB,EAA8Be,IAA9B,EAAoCC,IAApC,CAAlB;;AACA,eAAK9B,QAAL,CAAcmB,IAAd,CAAmBY,SAAnB;;AACA,eAAK9B,WAAL,CAAiBkB,IAAjB,CAAsB,CAAtB;;AACA,eAAKjB,YAAL,CAAkBiB,IAAlB,CAAuB,CAAvB;;AACA,eAAKhB,cAAL,CAAoBgB,IAApB,CAAyB,KAAKvB,eAA9B;;AAEA,cAAMoB,KAAK,GAAG,KAAK3B,gBAAL,CAAsB4B,GAAtB,CAA0Bc,SAAS,CAACb,YAApC,CAAd;;AACA,cAAIF,KAAK,IAAI,IAAb,EAAmB;AACf,iBAAK3B,gBAAL,CAAsB+B,GAAtB,CAA0BW,SAAS,CAACb,YAApC,EAAkD,CAACa,SAAD,CAAlD;AACH,WAFD,MAEO;AACHf,YAAAA,KAAK,CAACG,IAAN,CAAWY,SAAX;AACH;AACJ;;AAGKE,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAAA;;AAAA;AACrC,YAAA,KAAI,CAACC,gBAAL,CAAsBD,SAAtB;;AACA,kBAAM,KAAI,CAACE,YAAL,CAAkBF,SAAlB,CAAN;;AACA,YAAA,KAAI,CAACG,WAAL,CAAiBH,SAAjB;AAHqC;AAIxC;AAED;AACJ;AACA;AACA;;;AACYC,QAAAA,gBAAgB,CAACD,SAAD,EAA0B;AAC9C,cAAI,CAAC,KAAK3C,UAAV,EAAsB;AAAA;;AAClB,gBAAI,KAAKG,iBAAL,KAA2B,6BAAKD,aAAL,yCAAoB6C,MAApB,KAA8B,CAAzD,CAAJ,EAAiE;AAC7D,mBAAK/C,UAAL,GAAkB,IAAlB;AACAd,cAAAA,IAAI,CAAC,YAAD,CAAJ;AACH,aAHD,MAGO,IAAI,KAAKQ,eAAL,IAAwB,CAAC,KAAKW,eAAlC,EAAmD;AACtD,kBAAM2C,MAAM,GAAG,KAAK9C,aAAL,CAAoB,KAAKC,iBAAzB,CAAf;;AACA,sBAAQ6C,MAAM,CAACC,IAAf;AACI,qBAAK,CAAL;AACI,uBAAK7C,gBAAL,IAAyBuC,SAAzB;;AACA,sBACI,KAAKvC,gBAAL,IAAyB4C,MAAM,CAACE,gBAAhC,IACC,KAAKzC,QAAL,CAAcsC,MAAd,KAAyB,CAAzB,IAA8B;AAAA;AAAA,0CAAQI,YAAR,CAAqBC,mBAArB,OAA+C,CAFlF,EAGE;AACE,yBAAK/C,eAAL,GAAuB2C,MAAvB;AACH;;AACD;;AACJ;AACI,sBAAIA,MAAM,CAACC,IAAP,IAAe,GAAnB,EAAwB;AACpBI,oBAAAA,OAAO,CAACnE,IAAR,CAAa,YAAb,EAA2B8D,MAAM,CAACC,IAAlC,EAAwCD,MAAM,CAACM,WAAP,CAAmB,CAAnB,CAAxC;AACA,yBAAKzC,gBAAL,GAAwBmC,MAAM,CAACE,gBAA/B;AACA;AAAA;AAAA,4CAAQK,WAAR,CAAoBC,WAApB,CAAgCR,MAAM,CAACC,IAAvC,EAA6CD,MAAM,CAACM,WAAP,CAAmB,CAAnB,CAA7C;;AACA,yBAAKvC,aAAL,CAAmBa,IAAnB,CAAwBoB,MAAxB;;AACA,yBAAK7C,iBAAL;AACH;;AAjBT;AAmBH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACkB0C,QAAAA,YAAY,CAACF,SAAD,EAAoB;AAAA;;AAAA;AAC1C,kBAAM,MAAI,CAACc,kBAAL,CAAwBd,SAAxB,CAAN;;AAEA,gBAAI,MAAI,CAACtC,eAAT,EAA0B;AACtB,kBAAI,CAAC,MAAI,CAACqD,gBAAL,CAAsBf,SAAtB,CAAL,EAAuC;AACnC,gBAAA,MAAI,CAACtC,eAAL,GAAuB,IAAvB;AACA,gBAAA,MAAI,CAACF,iBAAL;AACA,gBAAA,MAAI,CAACC,gBAAL,GAAwB,CAAxB;AACH;AACJ;AATyC;AAU7C;AAGD;AACJ;AACA;AACA;;;AACkBqD,QAAAA,kBAAkB,CAACd,SAAD,EAAmC;AAAA;;AAAA;AAC/D,iBAAK,IAAIgB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,MAAI,CAAClD,QAAL,CAAcsC,MAAlC,EAA0CY,CAAC,EAA3C,EAA+C;AAC3C,kBAAMpC,IAAI,GAAG,MAAI,CAACd,QAAL,CAAckD,CAAd,CAAb;AACA,cAAA,MAAI,CAAChD,YAAL,CAAkBgD,CAAlB,KAAwBhB,SAAxB;AACA,kBAAMiB,iBAAiB,GAAG,MAAI,CAAClD,WAAL,CAAiBiD,CAAjB,CAA1B;AACA,kBAAIrB,IAAI,GAAG;AAAA;AAAA,0CAAUuB,QAAV,CAAmBC,CAA9B;AACA,kBAAIvB,IAAI,GAAG;AAAA;AAAA,0CAAUsB,QAAV,CAAmBE,CAA9B;;AAEA,kBAAIxC,IAAI,CAACyC,YAAT,EAAuB;AACnB1B,gBAAAA,IAAI,IAAIf,IAAI,CAAC0C,SAAb;AACA1B,gBAAAA,IAAI,IAAIhB,IAAI,CAAC2C,SAAb;AACH;;AAED,kBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAW9C,IAAI,CAAC+C,GAAL,GAAW/C,IAAI,CAACgD,QAA3B,CAApB;;AAEA,mBAAK,IAAIC,CAAC,GAAGZ,iBAAb,EAAgCY,CAAC,GAAGjD,IAAI,CAACgD,QAAzC,EAAmDC,CAAC,EAApD,EAAwD;AACpD,oBAAIjD,IAAI,CAACkD,aAAL,IAAsBD,CAAC,GAAG,CAA1B,IAA+B,MAAI,CAAC7D,YAAL,CAAkBgD,CAAlB,CAAnC,EAAyD;AACrD,kBAAA,MAAI,CAACjD,WAAL,CAAiBiD,CAAjB;AACA,sBAAIe,KAAqB,SAAzB;AACA,sBAAMC,SAAS,GAAGrC,IAAI,GAAGf,IAAI,CAACqD,KAAL,IAAcJ,CAAC,GAAG,CAAlB,CAAzB;AACA,sBAAMK,SAAS,GAAGtC,IAAI,GAAGhB,IAAI,CAACuD,KAAL,IAAcN,CAAC,GAAG,CAAlB,CAAzB;;AAEA,0BAAQjD,IAAI,CAAC0B,IAAb;AACI,yBAAK,CAAL;AACIyB,sBAAAA,KAAK,SAAS;AAAA;AAAA,8CAAQvB,YAAR,CAAqB4B,QAArB,CAA8BxD,IAAI,CAACyD,OAAnC,CAAd;;AACA,0BAAIN,KAAJ,EAAW;AACP,4BAAIF,CAAC,GAAGjD,IAAI,CAAC0D,eAAL,CAAqBlC,MAA7B,EAAqC;AACjC2B,0BAAAA,KAAK,CAACQ,kBAAN,CAAyB3D,IAAI,CAAC0D,eAAL,CAAqBT,CAArB,CAAzB;AACH;;AACDE,wBAAAA,KAAK,CAACS,cAAN,CAAqB,CAArB;AACAT,wBAAAA,KAAK,CAACU,MAAN,CAAajB,WAAb;AACAO,wBAAAA,KAAK,CAACW,gBAAN,CAAuB,MAAI,CAACzE,cAAL,CAAoB+C,CAApB,EAAuB2B,YAA9C;AACAZ,wBAAAA,KAAK,CAACa,SAAN,CACIhE,IAAI,CAACiE,WADT,EAEIjE,IAAI,CAACkE,SAFT,EAGId,SAHJ,EAIIE,SAJJ,EAKItD,IAAI,CAACmE,WALT,EAPO,CAcP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AACD;AA1BR;AA4BH;AACJ;;AAED,kBAAInE,IAAI,CAACgD,QAAL,IAAiB,MAAI,CAAC7D,WAAL,CAAiBiD,CAAjB,CAArB,EAA0C;AACtC,gBAAA,MAAI,CAAClD,QAAL,CAAcsB,MAAd,CAAqB4B,CAArB,EAAwB,CAAxB;;AACA,gBAAA,MAAI,CAACjD,WAAL,CAAiBqB,MAAjB,CAAwB4B,CAAxB,EAA2B,CAA3B;;AACA,gBAAA,MAAI,CAAChD,YAAL,CAAkBoB,MAAlB,CAAyB4B,CAAzB,EAA4B,CAA5B;;AACA,gBAAA,MAAI,CAAC/C,cAAL,CAAoBmB,MAApB,CAA2B4B,CAA3B,EAA8B,CAA9B;;AACAA,gBAAAA,CAAC;AACJ;AACJ;AA5D8D;AA6DlE;AAED;AACJ;AACA;AACA;;;AACYD,QAAAA,gBAAgB,CAACf,SAAD,EAA6B;AACjD,cAAI,KAAK1C,oBAAT,EAA+B;AAC3B,gBAAI,KAAKO,UAAL,IAAmB,KAAKH,eAAL,CAAsBiD,WAAtB,CAAkCP,MAAzD,EAAiE;AAC7D,mBAAKvC,UAAL,GAAkB,CAAlB;AACA,qBAAO,KAAP;AACH;;AAED,gBAAMmF,MAAM,GAAG,KAAKtF,eAAL,CAAsBiD,WAAtB,CAAkC,KAAK9C,UAAvC,CAAf;AACA,iBAAKF,eAAL,IAAwBqC,SAAxB;AAEA,gBAAMzB,SAAS,GAAG,KAAKiB,eAAL,CAAqBwD,MAArB,CAAlB;;AACA,gBAAI,CAACzE,SAAL,EAAgB;AACZ,qBAAO,KAAP;AACH;;AACDmC,YAAAA,OAAO,CAACuC,GAAR,kBAA2BD,MAA3B,0BAAsDzE,SAAS,CAAC6B,MAAhE;;AACA,iBAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzC,SAAS,CAAE6B,MAA/B,EAAuCY,CAAC,EAAxC,EAA4C;AACxC,kBAAMpC,IAAI,GAAGL,SAAS,CAAEyC,CAAF,CAAtB;;AACA,kBACI,CAAC;AAAA;AAAA,kCAAMkC,UAAN,CAAiB,KAAKtF,cAAtB,EAAsCoD,CAAtC,CAAD,IACA,KAAKrD,eAAL,IAAwBiB,IAAI,CAACuE,aAFjC,EAGE;AACE,qBAAKrF,QAAL,CAAcmB,IAAd,CAAmBL,IAAnB;;AACA,qBAAKb,WAAL,CAAiBkB,IAAjB,CAAsB,CAAtB;;AACA,qBAAKjB,YAAL,CAAkBiB,IAAlB,CAAuB,CAAvB;;AACA,qBAAKhB,cAAL,CAAoBgB,IAApB,CAAyB,KAAKvB,eAA9B;;AACA,qBAAKE,cAAL,CAAoBqB,IAApB,CAAyB+B,CAAzB;AACH;AACJ;;AAED,gBAAI,KAAKpD,cAAL,CAAoBwC,MAApB,IAA8B7B,SAAS,CAAE6B,MAA7C,EAAqD;AACjD,mBAAKxC,cAAL,CAAoBwB,MAApB,CAA2B,CAA3B;;AACA,mBAAKzB,eAAL,GAAuB,CAAvB;AACA,mBAAKE,UAAL;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACYsC,QAAAA,WAAW,CAACH,SAAD,EAA0B;AACzC,cACI,KAAK5B,aAAL,CAAmBgC,MAAnB,GAA4B,CAA5B,IACA;AAAA;AAAA,kCAAQI,YAAR,CAAqB4C,WAArB,EADA,KAEC,KAAK/E,iBAAL,KACI,KAAKA,iBAAL,GAAyB,IAAzB,EAA+B;AAAA;AAAA,kCAAQgF,aAAR,CAAsBC,aAAtB,EADnC,CAFD,CADJ,EAKE;AACE,iBAAKnF,eAAL,IAAwB6B,SAAxB;;AACA,gBACI,KAAK7B,eAAL,GAAuB,KAAKD,gBAA5B,IACA;AAAA;AAAA,oCAAQ0C,WAAR,CAAoB2C,aAFxB,EAGE;AACE,kBAAMC,QAAQ,GAAG,KAAKpF,aAAL,CAAmB,CAAnB,CAAjB;AACA,kBAAMqF,IAAI,GAAG;AAAA;AAAA,sCAAQ7C,WAAR,CAAoB8C,OAApB,CACTF,QAAQ,CAAClD,IADA,EAETkD,QAAQ,CAAC7C,WAAT,CAAqB,CAArB,CAFS,CAAb;;AAIA,kBAAI8C,IAAI;AAAA;AAAA,uCAAR,EAA8B;AAC1B;AACA;AACA;AACAA,gBAAAA,IAAI,CAACE,eAAL,CAAqBH,QAAQ,CAACb,YAA9B,EAJ0B,CAK1B;;AACAc,gBAAAA,IAAI,CAACG,MAAL,CAAY;AAAA;AAAA,wCAAQC,YAAR,CAAqBC,WAArB,EAAZ;AACH;;AACD,mBAAK1F,aAAL,CAAmBgB,MAAnB,CAA0B,CAA1B,EAA6B,CAA7B;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIgE,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAK/F,UAAL,IAAmB,KAAKS,QAAL,CAAcsC,MAAd,KAAyB,CAA5C,IAAiD,KAAKhC,aAAL,CAAmBgC,MAAnB,KAA8B,CAAtF;AACH;;AAtT+D,O", "sourcesContent": ["import {warn } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport { EnemyWave } from \"../data/EnemyWave\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport BossBase from \"../ui/plane/boss/BossBase\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport { StageData } from \"../data/StageData\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport { Wave } from \"../wave/Wave\";\r\n\r\n\r\nexport default class WaveManager extends SingletonBase<WaveManager> {\r\n    private _waveNorDatasMap: Map<number, EnemyWave[]> = new Map();// 波次配表数据\r\n    private _enemyOver: boolean = false;//是否加载所有敌机\r\n\r\n    private _bEnemyCreateAble: boolean = false;//是否可以创建敌机\r\n    private _bEnemyNorCreateAble: boolean = false;//是否可以创建普通敌机\r\n\r\n    private _enemyActions: StageData[] | null = null;//小阶段所有数据列表\r\n    private _enemyActionIndex: number = 0;//当前小阶段索引\r\n    private _enemyCreateTime: number = 0;//当前小阶段时间\r\n    private _curEnemyAction: StageData | null = null;//当前波次数据\r\n    private _waveCreateTime: number = 0;\r\n    private _waveIndexOver: number[] = [];\r\n\r\n    private _waveIndex: number = 0;//当前波次的索引\r\n    private _waveArr: EnemyWave[] = [];//当前波次的所有敌机数据\r\n    private _waveNumArr: number[] = [];//当前波次已创建的敌机数量\r\n    private _waveTimeArr: number[] = [];//当前波次计时\r\n    private _waveActionArr: any[] = [];\r\n\r\n    //boss\r\n    private _bossCreateDelay: number = 0;\r\n    private _bossCreateTime: number = 0;\r\n    private _bossToAddArr: any[] = [];\r\n    private _bShowBossWarning: boolean = false;\r\n\r\n\r\n    get enemyCreateAble(): boolean {\r\n        return this._bEnemyCreateAble;\r\n    }\r\n\r\n    set enemyCreateAble(value: boolean) {\r\n        this._bEnemyCreateAble = value;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n        // this.initConfig();\r\n    }\r\n\r\n    initConfig() {\r\n        let waveDatas = MyApp.lubanTables.TbWave.getDataList();\r\n        for (let waveData of waveDatas) {\r\n            const wave = new EnemyWave();\r\n            wave.loadJson(waveData);\r\n            const group = this._waveNorDatasMap.get(wave.enemyGroupID) || [];\r\n            group.push(wave);\r\n            this._waveNorDatasMap.set(wave.enemyGroupID, group);\r\n        }\r\n    }\r\n\r\n    reset(): void {\r\n        this._enemyOver = false;\r\n        this._enemyActions = [];\r\n        this._enemyActionIndex = 0;\r\n        this._enemyCreateTime = 0;\r\n        this._bEnemyCreateAble = false;\r\n        this._bEnemyNorCreateAble = false;\r\n        this._waveIndex = 0;\r\n        this._waveCreateTime = 0;\r\n        this._waveIndexOver.splice(0);\r\n        this._curEnemyAction = null;\r\n        this._waveArr = [];\r\n        this._waveActionArr = [];\r\n        this._waveNumArr = [];\r\n        this._waveTimeArr = [];\r\n        this._bShowBossWarning = false;\r\n        this._bossCreateTime = 0;\r\n    }\r\n\r\n    setEnemyActions(actions: StageData[]): void {\r\n        this._enemyActions = actions;\r\n    }\r\n\r\n    gameStart(): void {\r\n        this._bEnemyCreateAble = true;\r\n        this._bEnemyNorCreateAble = true;\r\n        this._waveArr = [];\r\n        this._waveActionArr = [];\r\n        this._waveNumArr = [];\r\n        this._waveTimeArr = [];\r\n    }\r\n\r\n    getNorWaveDatas(groupID: number): EnemyWave[] | undefined {\r\n        return this._waveNorDatasMap.get(groupID);\r\n    }\r\n\r\n    \r\n    addWaveByLevel(wave: Wave, posX: number, posY: number):void {\r\n        const enemyWave = EnemyWave.fromLevelWave(wave, posX, posY);\r\n        this._waveArr.push(enemyWave)\r\n        this._waveNumArr.push(0)\r\n        this._waveTimeArr.push(0)\r\n        this._waveActionArr.push(this._curEnemyAction)\r\n        \r\n        const group = this._waveNorDatasMap.get(enemyWave.enemyGroupID)\r\n        if (group == null) {\r\n            this._waveNorDatasMap.set(enemyWave.enemyGroupID, [enemyWave]);\r\n        } else {\r\n            group.push(enemyWave);\r\n        }\r\n    }\r\n\r\n\r\n    async updateGameLogic(deltaTime: number) {\r\n        this._updateCurAction(deltaTime);\r\n        await this._updateEnemy(deltaTime);\r\n        this._updateBoss(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 更新当前敌人行为\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateCurAction(deltaTime: number): void {\r\n        if (!this._enemyOver) {\r\n            if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {\r\n                this._enemyOver = true;\r\n                warn(\"enemy over\");\r\n            } else if (this.enemyCreateAble && !this._curEnemyAction) {\r\n                const action = this._enemyActions![this._enemyActionIndex];\r\n                switch (action.type) {\r\n                    case 0:\r\n                        this._enemyCreateTime += deltaTime;\r\n                        if (\r\n                            this._enemyCreateTime >= action.enemyNorInterval ||\r\n                            (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)\r\n                        ) {\r\n                            this._curEnemyAction = action;\r\n                        }\r\n                        break;\r\n                    default:\r\n                        if (action.type >= 100) {\r\n                            console.warn(\"Boss stage\", action.type, action.enemyNorIDs[0]);\r\n                            this._bossCreateDelay = action.enemyNorInterval;\r\n                            GameIns.bossManager.loadBossRes(action.type, action.enemyNorIDs[0]);\r\n                            this._bossToAddArr.push(action);\r\n                            this._enemyActionIndex++;\r\n                        }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新敌人逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private async _updateEnemy(deltaTime: number) {\r\n        await this._updateEnemyCreate(deltaTime);\r\n\r\n        if (this._curEnemyAction) {\r\n            if (!this._updateNorEnemys(deltaTime)) {\r\n                this._curEnemyAction = null;\r\n                this._enemyActionIndex++;\r\n                this._enemyCreateTime = 0;\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 更新敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private async _updateEnemyCreate(deltaTime: number): Promise<void> {\r\n        for (let i = 0; i < this._waveArr.length; i++) {\r\n            const wave = this._waveArr[i];\r\n            this._waveTimeArr[i] += deltaTime;\r\n            const currentEnemyCount = this._waveNumArr[i];\r\n            let posX = GameConst.EnemyPos.x;\r\n            let posY = GameConst.EnemyPos.y;\r\n\r\n            if (wave.bSetStartPos) {\r\n                posX += wave.startPosX;\r\n                posY += wave.startPosY;\r\n            }\r\n\r\n            const expPerEnemy = Math.floor(wave.exp / wave.enemyNum);\r\n\r\n            for (let j = currentEnemyCount; j < wave.enemyNum; j++) {\r\n                if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {\r\n                    this._waveNumArr[i]++;\r\n                    let enemy:EnemyPlane|null;\r\n                    const enemyPosX = posX + wave.posDX * (j + 1);\r\n                    const enemyPosY = posY + wave.posDY * (j + 1);\r\n\r\n                    switch (wave.type) {\r\n                        case 0:\r\n                            enemy = await GameIns.enemyManager.addPlane(wave.enemyID);\r\n                            if (enemy) {\r\n                                if (j < wave.firstShootDelay.length) {\r\n                                    enemy.setFirstShootDelay(wave.firstShootDelay[j]);\r\n                                }\r\n                                enemy.setStandByTime(0);\r\n                                enemy.setExp(expPerEnemy);\r\n                                enemy.initPropertyRate(this._waveActionArr[i].enemyNorRate);\r\n                                enemy.initTrack(\r\n                                    wave.trackGroups,\r\n                                    wave.liveParam,\r\n                                    enemyPosX,\r\n                                    enemyPosY,\r\n                                    wave.rotateSpeed\r\n                                );\r\n                                // if (\r\n                                //     wave.normalLoot &&\r\n                                //     Tools.arrContain(wave.normalLoot.enemys, j + 1)\r\n                                // ) {\r\n                                //     enemy.addLoot(\r\n                                //         GameIns.lootManager.getLootData(wave.normalLoot.lootId)\r\n                                //     );\r\n                                // }\r\n                            }\r\n                            break;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (wave.enemyNum <= this._waveNumArr[i]) {\r\n                this._waveArr.splice(i, 1);\r\n                this._waveNumArr.splice(i, 1);\r\n                this._waveTimeArr.splice(i, 1);\r\n                this._waveActionArr.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新普通敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateNorEnemys(deltaTime: number): boolean {\r\n        if (this._bEnemyNorCreateAble) {\r\n            if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {\r\n                this._waveIndex = 0;\r\n                return false;\r\n            }\r\n\r\n            const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];\r\n            this._waveCreateTime += deltaTime;\r\n\r\n            const waveDatas = this.getNorWaveDatas(waveID);\r\n            if (!waveDatas) {\r\n                return false;\r\n            }\r\n            console.log(`ybgg waveID:${waveID} waveDatas length:${waveDatas.length}`);\r\n            for (let i = 0; i < waveDatas!.length; i++) {\r\n                const wave = waveDatas![i];\r\n                if (\r\n                    !Tools.arrContain(this._waveIndexOver, i) &&\r\n                    this._waveCreateTime >= wave.groupInterval\r\n                ) {\r\n                    this._waveArr.push(wave);\r\n                    this._waveNumArr.push(0);\r\n                    this._waveTimeArr.push(0);\r\n                    this._waveActionArr.push(this._curEnemyAction);\r\n                    this._waveIndexOver.push(i);\r\n                }\r\n            }\r\n\r\n            if (this._waveIndexOver.length >= waveDatas!.length) {\r\n                this._waveIndexOver.splice(0);\r\n                this._waveCreateTime = 0;\r\n                this._waveIndex++;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 更新 Boss 生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateBoss(deltaTime: number): void {\r\n        if (\r\n            this._bossToAddArr.length > 0 &&\r\n            GameIns.enemyManager.isEnemyOver() &&\r\n            (this._bShowBossWarning ||\r\n                (this._bShowBossWarning = true, GameIns.battleManager.bossWillEnter()))\r\n        ) {\r\n            this._bossCreateTime += deltaTime;\r\n            if (\r\n                this._bossCreateTime > this._bossCreateDelay &&\r\n                GameIns.bossManager.bossResFinish\r\n            ) {\r\n                const bossData = this._bossToAddArr[0];\r\n                const boss = GameIns.bossManager.addBoss(\r\n                    bossData.type,\r\n                    bossData.enemyNorIDs[0]\r\n                );\r\n                if (boss instanceof BossBase) {\r\n                    // if (GameIns.battleManager.isGameType(GameEnum.GameType.Boss)) {\r\n                    //     boss.setPropertyRate(BossBattleManager.getPropertyRate());\r\n                    // } else {\r\n                    boss.setPropertyRate(bossData.enemyNorRate);\r\n                    // }\r\n                    boss.setTip(GameIns.stageManager.getBossTips());\r\n                }\r\n                this._bossToAddArr.splice(0, 1);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 检查敌人是否全部结束\r\n     * @returns 是否所有敌人都已结束\r\n     */\r\n    isEnemyOver(): boolean {\r\n        return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;\r\n    }\r\n}"]}
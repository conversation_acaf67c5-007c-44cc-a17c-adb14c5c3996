{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MyApp.ts"], "names": ["_decorator", "Component", "LubanMgr", "NetMgr", "audioManager", "CreateLoginSDK", "ResManager", "GlobalDataManager", "ccclass", "MyApp", "ManagerPool", "_updateContainer", "_lateUpdateC<PERSON>r", "_lubanMgr", "_netMgr", "_resMgr", "_platformSDK", "_globalDataManager", "GetInstance", "_instance", "onLoad", "push", "instance", "for<PERSON>ach", "manager", "init", "onUpdate", "bind", "onLateUpdate", "update", "deltaTime", "i", "length", "lateUpdate", "netMgr", "lubanMgr", "lubanTables", "table", "platformSDK", "resMgr", "globalDataManager"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AAEZC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEcC,MAAAA,c,iBAAAA,c;;AACdC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcR,U;;uBAGPS,K,WADZD,OAAO,CAAC,OAAD,C,2BAAR,MACaC,KADb,SAC2BR,SAD3B,CACqC;AAAA;AAAA;AAAA,eAMzBS,WANyB,GAML,EANK;AAAA,eAOzBC,gBAPyB,GAOK,EAPL;AAAA,eAQzBC,oBARyB,GAQa,EARb;AAAA,eAUzBC,SAVyB,GAUC,IAVD;AAAA,eAWzBC,OAXyB,GAWH,IAXG;AAAA,eAYzBC,OAZyB,GAYC,IAZD;AAAA,eAazBC,YAbyB,GAaO,IAbP;AAAA,eAczBC,kBAdyB,GAckB,IAdlB;AAAA;;AAGR,eAAXC,WAAW,GAAU;AAC/B,iBAAOT,KAAK,CAACU,SAAb;AACH;;AAaDC,QAAAA,MAAM,GAAQ;AACVX,UAAAA,KAAK,CAACU,SAAN,GAAkB,IAAlB;AAEA,eAAKT,WAAL,CAAiBW,IAAjB,CAAsB;AAAA;AAAA,4CAAaC,QAAnC;AACA,eAAKT,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKH,WAAL,CAAiBW,IAAjB,CAAsB,KAAKR,SAA3B;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,iCAAf;AACA,eAAKJ,WAAL,CAAiBW,IAAjB,CAAsB,KAAKP,OAA3B;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,yCAAf;AACA,eAAKL,WAAL,CAAiBW,IAAjB,CAAsB,KAAKN,OAA3B;AACA,eAAKC,YAAL,GAAoB;AAAA;AAAA,iDAApB;AACA,eAAKC,kBAAL,GAA0B;AAAA;AAAA,uDAA1B;AAEA,eAAKP,WAAL,CAAiBa,OAAjB,CAAyBC,OAAO,IAAI;AAChCA,YAAAA,OAAO,CAACC,IAAR;;AACC,iBAAKd,gBAAL,CAAsBU,IAAtB,CAA2BG,OAAO,CAACE,QAAR,CAAiBC,IAAjB,CAAsBH,OAAtB,CAA3B;;AACA,iBAAKZ,oBAAL,CAA0BS,IAA1B,CAA+BG,OAAO,CAACI,YAAR,CAAqBD,IAArB,CAA0BH,OAA1B,CAA/B;AACH,WAJF;AAKH;;AACDK,QAAAA,MAAM,CAAEC,SAAF,EAA0B;AAC5B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpB,gBAAL,CAAsBqB,MAA1C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,iBAAKpB,gBAAL,CAAsBoB,CAAtB,EAAyBD,SAAzB;AACH;AACJ;;AAEDG,QAAAA,UAAU,GAAQ;AACd,eAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnB,oBAAL,CAA0BoB,MAA9C,EAAsDD,CAAC,EAAvD,EAA2D;AACvD,iBAAKnB,oBAAL,CAA0BmB,CAA1B;AACH;AACJ;;AAEgB,mBAANG,MAAM,GAAU;AACvB,iBAAOzB,KAAK,CAACS,WAAN,GAAoBJ,OAA3B;AACH;;AACkB,mBAARqB,QAAQ,GAAY;AAC3B,iBAAO1B,KAAK,CAACS,WAAN,GAAoBL,SAA3B;AACH;;AACqB,mBAAXuB,WAAW,GAAc;AAChC,iBAAO3B,KAAK,CAACS,WAAN,GAAoBL,SAApB,CAA+BwB,KAAtC;AACH;;AACqB,mBAAXC,WAAW,GAAgB;AAClC,iBAAO7B,KAAK,CAACS,WAAN,GAAoBF,YAA3B;AACH;;AACgB,mBAANuB,MAAM,GAAc;AAC3B,iBAAO9B,KAAK,CAACS,WAAN,GAAoBH,OAA3B;AACH;;AAC4B,mBAAjByB,iBAAiB,GAAqB;AAC9C,iBAAO/B,KAAK,CAACS,WAAN,GAAoBD,kBAA3B;AACH;;AAlEgC,O,UAElBE,S,GAAwB,I", "sourcesContent": ["import { _decorator, Component, Game } from \"cc\";\nimport { IMgr, FnOnUpdate, FnOnLateUpdate } from \"./IMgr\";\nimport { LubanMgr } from \"./Luban/LubanMgr\";\nimport { NetMgr } from \"./Network/NetMgr\";\nimport { audioManager } from './ResUpdate/audioManager';\nimport * as cfg from './AutoGen/Luban/schema';\nimport { IPlatformSDK, CreateLoginSDK } from \"./PlatformSDK/IPlatformSDK\";\nimport { ResManager } from \"./core/base/ResManager\";\nimport { GlobalDataManager } from \"./Game/manager/GlobalDataManager\";\n\nconst { ccclass } = _decorator;\n\n@ccclass(\"MyApp\")\nexport class MyApp extends Component {\n\n    private static _instance: MyApp|null = null;\n    public static GetInstance(): MyApp {\n        return MyApp._instance!;\n    }\n    private ManagerPool:IMgr[]= [];\n    private _updateContainer:FnOnUpdate[]=[];\n    private _lateUpdateContainer:FnOnLateUpdate[]=[];\n\n    private _lubanMgr:LubanMgr|null = null;\n    private _netMgr:NetMgr|null = null;\n    private _resMgr:ResManager|null = null;\n    private _platformSDK:IPlatformSDK|null =null;\n    private _globalDataManager:GlobalDataManager|null =null;\n\n    \n\n    onLoad():void {\n        MyApp._instance = this; \n\n        this.ManagerPool.push(audioManager.instance);\n        this._lubanMgr = new LubanMgr();\n        this.ManagerPool.push(this._lubanMgr);\n        this._netMgr = new NetMgr();\n        this.ManagerPool.push(this._netMgr);\n        this._resMgr = new ResManager();\n        this.ManagerPool.push(this._resMgr);\n        this._platformSDK = CreateLoginSDK();\n        this._globalDataManager = new GlobalDataManager();\n\n        this.ManagerPool.forEach(manager => {\n            manager.init();\n             this._updateContainer.push(manager.onUpdate.bind(manager));\n             this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));\n         });\n    }\n    update (deltaTime: number):void {\n        for (let i = 0; i < this._updateContainer.length; i++) {\n            this._updateContainer[i](deltaTime);\n        }\n    }\n\n    lateUpdate():void {\n        for (let i = 0; i < this._lateUpdateContainer.length; i++) {\n            this._lateUpdateContainer[i]();\n        }\n    }\n\n    static get netMgr():NetMgr {\n        return MyApp.GetInstance()._netMgr!;\n    }\n    static get lubanMgr():LubanMgr {\n        return MyApp.GetInstance()._lubanMgr!;\n    }\n    static get lubanTables():cfg.Tables {\n        return MyApp.GetInstance()._lubanMgr!.table;\n    }\n    static get platformSDK():IPlatformSDK {\n        return MyApp.GetInstance()._platformSDK!;\n    }\n    static get resMgr():ResManager {\n        return MyApp.GetInstance()._resMgr!;\n    }\n     static get globalDataManager():GlobalDataManager {\n        return MyApp.GetInstance()._globalDataManager!;\n    }\n}"]}
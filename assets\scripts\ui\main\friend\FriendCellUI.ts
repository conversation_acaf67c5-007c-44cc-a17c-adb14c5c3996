import { _decorator, Component, Label, Node, RichText } from "cc";
import { EventMgr } from "../../../event/EventManager";
import { ButtonPlus } from "../../common/components/button/ButtonPlus";
import { UIMgr } from "../../UIMgr";
import { MainEvent } from "../MainEvent";
import { PopupUI } from "../PopupUI";

const { ccclass, property } = _decorator;

//参考 BagItem
@ccclass('FriendCellUI')
export class FriendCellUI extends Component {

    @property(Label)
    txtName: Label | null = null;
    @property(RichText)
    txtPower: RichText | null = null;
    @property(Label)
    txtOnline: Label | null = null;

    @property(Node)
    node1: Node | null = null;
    @property(ButtonPlus)
    btnGet: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnSend: ButtonPlus | null = null;

    @property(Node)
    node2: Node | null = null;
    @property(ButtonPlus)
    btnIgnore: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnAgree: ButtonPlus | null = null;

    @property(Node)
    node3: Node | null = null;
    @property(ButtonPlus)
    btnApply: ButtonPlus | null = null;

    protected onLoad(): void {
        this.getComponent(ButtonPlus)!.addClick(this.onClick, this)
        this.setPowerText(4567500);
        this.btnGet!.addClick(this.onGet, this)
        this.btnSend!.addClick(this.onSend, this)
        this.btnIgnore!.addClick(this.onIgnore, this)
        this.btnAgree!.addClick(this.onAgree, this)
        this.btnApply!.addClick(this.onApply, this)

    }
    public setPowerText(powerValue: number): void {
        const currentText = this.txtPower!.string;
        const prefixColorRegex = /<color=([^>]+)>战力：<\/color>/;
        const prefixMatch = currentText.match(prefixColorRegex);
        let prefixColorTag = "<color=#00ff00>"; // 默认颜色
        const valueColorRegex = /<color=([^>]+)>\d+<\/color>/;
        const valueMatch = currentText.match(valueColorRegex);
        let valueColorTag = "<color=#0fffff>"; // 默认颜色
        if (prefixMatch && prefixMatch[1]) {
            prefixColorTag = `<color=${prefixMatch[1]}>`;
        }
        if (valueMatch && valueMatch[1]) {
            valueColorTag = `<color=${valueMatch[1]}>`;
        }
        let formattedValue: string;
        if (powerValue >= 1000) {
            formattedValue = (powerValue / 1000).toFixed(1) + "K";
        } else {
            formattedValue = powerValue.toString();
        }
        this.txtPower!.string = `${prefixColorTag}战力：</color>${valueColorTag}${formattedValue}</color>`;
    }

    public setLastOnlineTime(lastOnlineTimestamp: number): void {
        if (lastOnlineTimestamp === 0) {
            this.txtOnline!.string = "在线";
            return;
        }

        const currentTimestamp = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
        const diffSeconds = currentTimestamp - lastOnlineTimestamp;

        if (diffSeconds < 0) {
            // 时间戳异常（未来时间）
            this.txtOnline!.string = "";
            return;
        }

        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffSeconds / 3600);

        if (diffMinutes < 1) {
            this.txtOnline!.string = "刚刚";
        } else if (diffHours < 1) {
            this.txtOnline!.string = `${diffMinutes}分钟前`;
        } else if (diffHours < 24) {
            this.txtOnline!.string = `${diffHours}小时前`;
        } else {
            var diffDays = Math.floor(diffSeconds / 86400);
            if (diffDays > 7) diffDays = 7;
            this.txtOnline!.string = `${diffDays}天前`;
        }
    }

    protected onDestroy(): void {
    }

    private onClick() {
        EventMgr.emit(MainEvent.BattleItemClick, this.getComponentInChildren(Label)!.string);
    }
    private onGet() {
        UIMgr.openUI(PopupUI, "收了");
    }
    private onSend() {
        UIMgr.openUI(PopupUI, "送了");
    }
    private onIgnore() {
        UIMgr.openUI(PopupUI, "忽略了");
    }
    private onAgree() {
        UIMgr.openUI(PopupUI, "同意了");
    }
    private onApply() {
        UIMgr.openUI(PopupUI, "申请了");
    }
    //cell的容器去手动调用
    onListRender(listItem: Node, row: number) {
        listItem.name = `${row}`
    }

    public setType(type: number) {
        if (type === 1) {
            this.node1!.active = true;
            this.node2!.active = false;
            this.node3!.active = false;
        } else if (type === 2) {
            this.node1!.active = false;
            this.node2!.active = true;
            this.node3!.active = false;
        } else if (type === 3) {
            this.node1!.active = false;
            this.node2!.active = false;
            this.node3!.active = true;
        }
    }
}
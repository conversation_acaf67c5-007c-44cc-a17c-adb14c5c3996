'use strict';

import path from "path";
import { methods } from "../../main";

type Selector<$> = { $: Record<keyof $, any | null> }

export const template = `
<ui-prop type="dump" class="progress"></ui-prop>
<ui-prop type="dump" class="levelPrefab"></ui-prop>
<ui-prop type="dump" class="save"></ui-prop>
<ui-prop type="dump" class="play"></ui-prop>
<ui-prop type="dump" class="levelPrefabUUID"></ui-prop>
<ui-prop >
    <ui-button class="btn-save">保存(ctrl+t)</ui-button>
    <ui-button class="btn-new">新建关卡</ui-button>
    <ui-button class="btn-play">播放</ui-button>
</ui-prop>
`;


export const $ = {
    progress: '.progress',
    levelPrefab: '.levelPrefab',
    save: '.save',
    levelPrefabUUID: '.levelPrefabUUID',
    btnSave: '.btn-save',
    btnNew: '.btn-new',
    btnPlay: '.btn-play',
    play: '.play',
};

export function update(this: Selector<typeof $> & typeof methods, dump: any) {
    // 使用 ui-porp 自动渲染，设置 prop 的 type 为 dump
    // render 传入一个 dump 数据，能够自动渲染出对应的界面
    // 自动渲染的界面修改后，能够自动提交数据
    this.dump = dump
    this.$.progress.render(dump.value.progress);
    this.$.levelPrefab.render(dump.value.levelPrefab);     // 这个是 levelPrefab 的 uuid，需要转成 JsonAsset 才能用
    this.$.save.dump = dump.value.save;
    this.$.play.dump = dump.value.play;
    this.$.levelPrefabUUID.dump = dump.value.levelPrefabUUID;
    // this.$.save.render(dump.value.save);
}

export function ready(this: Selector<typeof $> & typeof methods) {
    this.$.btnSave.addEventListener('confirm', ()=>{
        console.log("panel save level")
        // @ts-ignore
        this.dump.value.save.value = true;
        this.$.save.dispatch('change-dump')
    })
    this.$.btnNew.addEventListener('confirm', async ()=>{
        console.log("panel new level:", Editor.Project.path)
        // Editor.Panel.open('level-editor.newlevel')
        const dirPath = path.join(Editor.Project.path, "assets", "resources", "Game", "level")
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [ // 过滤器，用于指定可以选择的文件类型。
                { name: 'Level', extensions: ['json'] },
            ],
        })
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path.relative(dirPath, retData.filePath)
        console.log("panel new level name:", name)
        const filePath = `db://assets/resources/Game/level/${name}`;
        var createRsp = await Editor.Message.request('asset-db', 'create-asset', filePath, "{}");
        console.log("panel new level create asset rsp:", createRsp)
        // @ts-ignore
        this.dump.value.levelPrefabUUID.value = createRsp?.uuid;
        this.$.levelPrefabUUID.dispatch('change-dump')
        // Editor.Message.send('level-editor', 'new-level', name)
    })
    this.$.btnPlay.addEventListener('confirm', ()=>{
        console.log("panel play level")
        // @ts-ignore
        this.dump.value.play.value = true;
        this.$.play.dispatch('change-dump')
    })
}

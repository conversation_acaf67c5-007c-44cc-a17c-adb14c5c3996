
import { GameConst } from "../../const/GameConst";
import { Bullet as BulletConfig } from "db://assets/scripts/AutoGen/Luban/schema";
import BaseComp from "./BaseComp";
import Bullet from "../bullet/Bullet";


export default class AngleComp extends BaseComp {
    m_initFinish = false; // 初始化完成标志
    m_bstyle: number = 0; // 子弹样式
    props: BulletConfig | null = null; // 配置参数
    m_entity: Bullet | null = null; 

    constructor(props: BulletConfig, bstyle:number) {
        super();
        this.m_initFinish = false; // 初始化完成标志
        this.m_bstyle = bstyle;   // 子弹样式
        this.props = props;       // 配置参数
    }

    /**
     * 初始化组件
     */
    onInit() {
        this.m_initFinish = true;
    }

    /**
     * 更新组件逻辑
     * @param {number} deltaTime 帧间隔时间
     */
    update(deltaTime: number) {
        if (!GameConst.GameAble || !this.m_initFinish) {
            return;
        }

        // 限制最大帧间隔时间
        if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667;
        }

        // 根据子弹样式更新角度
        if ([38, 33, 44, 22].indexOf(this.m_bstyle) != -1) {
            
            this.m_entity!.skinImg!.node.angle += this.props!.angleSpeed * deltaTime;
        } else {
            this.m_entity!.node!.angle += this.props!.angleSpeed * deltaTime;
        }
    }
}
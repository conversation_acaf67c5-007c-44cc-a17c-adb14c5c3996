{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts"], "names": ["BulletManager", "<PERSON><PERSON><PERSON><PERSON>", "log", "Prefab", "SpriteAtlas", "SingletonBase", "GameIns", "GameFunc", "Bullet", "EnemyBase", "BossBase", "GameResourceList", "MyApp", "_preloadFinish", "_mainStage", "mainBulletAtlas", "enemyBulletAtlas", "enemyComAtlas", "m_unUseBullets", "Map", "selfBullets", "enemyBullets", "m_nodeCatch", "_bulletCount", "_testIds", "preLoad", "stage", "battleManager", "addLoadCount", "spriteAtlases", "atlas_enemyBullet", "atlas_mainBullet", "resMgr", "load", "error", "atlas", "checkLoadFinish", "prefabs", "atlas_enemyBullet1", "clear", "releaseAssetByForce", "battleInit", "get", "bullets", "i", "bullet", "createNewBullet", "node", "push", "set", "removeAll", "length", "prefabManager", "createFrame", "getConfig", "bulletID", "includes", "console", "lubanTables", "TbBullet", "getBullet", "isEnemy", "config", "saveType", "getSaveType", "unusedBullets", "pop", "create", "enemy", "bustyle", "new_uuid", "uuid", "has", "refresh", "setPosition", "removeBullet", "removeFromEntity", "bulletList", "index", "indexOf", "splice", "remove", "m_mainEntity", "forceDestroy", "removeAllComp", "getType", "parent", "setScale", "getScale", "x", "destroy", "removeEnemyBullets", "dieRemove", "for<PERSON>ach", "count", "destroyAll", "bulletType", "createComponent", "id"], "mappings": ";;;0MAWaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXJC,MAAAA,O,OAAAA,O;AAASC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACtBC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,M;;AACAC,MAAAA,S;;AACAC,MAAAA,Q;;AACAC,MAAAA,gB;;AACEC,MAAAA,K,iBAAAA,K;;;;;;;;;+BAGIZ,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAwD;AAAA;AAAA;AAAA,eAE3Da,cAF2D,GAE1C,KAF0C;AAAA,eAG3DC,UAH2D,GAG9C,CAH8C;AAAA,eAI3DC,eAJ2D,GAIrB,IAJqB;AAAA,eAK3DC,gBAL2D,GAKpB,IALoB;AAAA,eAM3DC,aAN2D,GAMvB,IANuB;AAAA,eAO3DC,cAP2D,GAO1C,IAAIC,GAAJ,EAP0C;AAAA,eAQ3DC,WAR2D,GAQ7C,IAAID,GAAJ,EAR6C;AAAA,eAS3DE,YAT2D,GAS5C,IAAIF,GAAJ,EAT4C;AAAA,eAU3DG,WAV2D,GAUpC,EAVoC;AAAA,eAW3DC,YAX2D,GAW5C,GAX4C;AAAA,eAY3DC,QAZ2D,GAYtC,EAZsC;AAAA;;AAc3DC,QAAAA,OAAO,CAACC,KAAK,GAAG,CAAT,EAAY;AACf;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AAAsC;AACtC,gBAAMC,aAAa,GAAG,CAAC;AAAA;AAAA,oDAAiBC,iBAAlB,EAAqC;AAAA;AAAA,oDAAiBC,gBAAtD,CAAtB;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkBJ,aAAlB,EAAiCzB,WAAjC,EAA8C,CAAC8B,KAAD,EAAcC,KAAd,KAAsC;AAChF,iBAAKlB,aAAL,GAAqBkB,KAAK,CAAC,CAAD,CAA1B;AACA,iBAAKpB,eAAL,GAAuBoB,KAAK,CAAC,CAAD,CAA5B;AACA;AAAA;AAAA,oCAAQR,aAAR,CAAsBS,eAAtB;AAAwC;AAC3C,WAJD;AAMA;AAAA;AAAA,kCAAQT,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA,gBAAMS,OAAO,GAAG,CACZ;AAAA;AAAA,oDAAiB7B,MADL,CAAhB;AAIA;AAAA;AAAA,8BAAMwB,MAAN,CAAaC,IAAb,CAAkBI,OAAlB,EAA2BlC,MAA3B,EAAmC,MAAM;AACrC;AAAA;AAAA,oCAAQwB,aAAR,CAAsBS,eAAtB;AAAwC;AAC3C,WAFD;;AAIA,cAAIV,KAAK,GAAG,CAAZ,EAAe;AACX;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AAAsC;AACtC;AAAA;AAAA,gCAAMI,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiBK,kBAAnC,EAAuDlC,WAAvD,EAAmE,CAAC8B,KAAD,EAAeC,KAAf,KAAsC;AACrG,mBAAKnB,gBAAL,GAAwBmB,KAAxB;AACA;AAAA;AAAA,sCAAQR,aAAR,CAAsBS,eAAtB;AAAwC;AAC3C,aAHD;AAIH;AACJ;;AAEDG,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKvB,gBAAT,EAA2B;AACvB;AAAA;AAAA,gCAAMgB,MAAN,CAAaQ,mBAAb,CAAiC,KAAKxB,gBAAtC;AACA,iBAAKA,gBAAL,GAAwB,IAAxB;AACH;AACJ;;AAEe,cAAVyB,UAAU,GAAG;AACf,cAAI,CAAC,KAAKvB,cAAL,CAAoBwB,GAApB,CAAwB,IAAxB,CAAL,EAAoC;AAChC,kBAAMC,OAAO,GAAG,EAAhB;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,GAApB,EAAyBA,CAAC,EAA1B,EAA8B;AAC1B,oBAAMC,MAAM,GAAI,MAAM,KAAKC,eAAL,CAAqB,CAArB,EAAwB,IAAxB,CAAtB;;AACA,kBAAID,MAAM,CAACE,IAAP,IAAe9C,OAAO,CAAC4C,MAAD,CAA1B,EAAoC;AAChCF,gBAAAA,OAAO,CAACK,IAAR,CAAaH,MAAb;AACH;AACJ;;AACD,iBAAK3B,cAAL,CAAoB+B,GAApB,CAAwB,IAAxB,EAA8BN,OAA9B;AACH;;AACD,eAAKO,SAAL;;AACA,cAAI,KAAK5B,WAAL,CAAiB6B,MAAjB,GAA0B,KAAK5B,YAAnC,EAAiD;AAC7C,kBAAM;AAAA;AAAA,oCAAQ6B,aAAR,CAAsBC,WAAtB;AAAA;AAAA,kCAEF,KAAK9B,YAAL,GAAoB,KAAKD,WAAL,CAAiB6B,MAFnC,EAGF,KAAK7B,WAHH,EAIF,EAJE,CAAN;AAMH;AACJ;;AAEDgC,QAAAA,SAAS,CAACC,QAAD,EAAkB;AACvB,cAAI,CAAC,KAAK/B,QAAL,CAAcgC,QAAd,CAAuBD,QAAvB,CAAL,EAAuC;AACnCE,YAAAA,OAAO,CAACvD,GAAR,CAAY,WAAZ,EAAyBqD,QAAzB;;AACA,iBAAK/B,QAAL,CAAcwB,IAAd,CAAmBO,QAAnB;AACH;;AACD,iBAAO;AAAA;AAAA,8BAAMG,WAAN,CAAkBC,QAAlB,CAA2BjB,GAA3B,CAA+Ba,QAA/B,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACmB,cAATK,SAAS,CAACL,QAAD,EAAmBM,OAAnB,EAAqC;AAChD,cAAI;AACA,kBAAMC,MAAM,GAAG,KAAKR,SAAL,CAAeC,QAAf,CAAf;AACA,gBAAI,CAACO,MAAL,EAAa,OAAO,IAAP;AAEb,kBAAMC,QAAQ,GAAG,KAAKC,WAAL,CAAiBF,MAAjB,EAAyBD,OAAzB,CAAjB;AACA,gBAAII,aAAa,GAAG,KAAK/C,cAAL,CAAoBwB,GAApB,CAAwBqB,QAAxB,CAApB;AACA,gBAAIlB,MAAJ;;AAEA,gBAAIoB,aAAa,IAAIA,aAAa,CAACd,MAAd,GAAuB,CAA5C,EAA+C;AAC3CN,cAAAA,MAAM,GAAGoB,aAAa,CAACC,GAAd,EAAT;;AACA,kBAAIjE,OAAO,CAAC4C,MAAD,CAAP,IAAmB5C,OAAO,CAAC4C,MAAM,CAACE,IAAR,CAA9B,EAA6C;AACzCF,gBAAAA,MAAM,CAACsB,MAAP,CAAcZ,QAAd;AACAV,gBAAAA,MAAM,CAACuB,KAAP,GAAeP,OAAf;AACH,eAHD,MAGO;AACH3D,gBAAAA,GAAG,CAAC,kBAAD,EAAqBqD,QAArB,EAA+BQ,QAA/B,CAAH;AACAlB,gBAAAA,MAAM,GAAG,MAAM,KAAKC,eAAL,CAAqBgB,MAAM,CAACO,OAA5B,EAAqCR,OAArC,CAAf;AACAhB,gBAAAA,MAAM,CAACU,QAAP,GAAkBA,QAAlB;AACAV,gBAAAA,MAAM,CAACuB,KAAP,GAAeP,OAAf;AACAhB,gBAAAA,MAAM,CAACsB,MAAP,CAAcZ,QAAd;AACH;AACJ,aAZD,MAYO;AACHV,cAAAA,MAAM,GAAG,MAAM,KAAKC,eAAL,CAAqBgB,MAAM,CAACO,OAA5B,EAAqCR,OAArC,CAAf;AACAhB,cAAAA,MAAM,CAACU,QAAP,GAAkBA,QAAlB;AACAV,cAAAA,MAAM,CAACuB,KAAP,GAAeP,OAAf;AACAhB,cAAAA,MAAM,CAACsB,MAAP,CAAcZ,QAAd;AACH;;AAEDV,YAAAA,MAAM,CAACyB,QAAP,GAAkB;AAAA;AAAA,sCAASC,IAA3B;;AAEA,gBAAI1B,MAAM,CAACuB,KAAX,EAAkB;AACd,kBAAI,KAAK/C,YAAL,CAAkBmD,GAAlB,CAAsB3B,MAAM,CAACU,QAA7B,CAAJ,EAA4C;AACxC,qBAAKlC,YAAL,CAAkBqB,GAAlB,CAAsBG,MAAM,CAACU,QAA7B,EAAuCP,IAAvC,CAA4CH,MAA5C;AACH,eAFD,MAEO;AACH,qBAAKxB,YAAL,CAAkB4B,GAAlB,CAAsBJ,MAAM,CAACU,QAA7B,EAAuC,CAACV,MAAD,CAAvC;AACH;AACJ,aAND,MAMO;AACH,kBAAI,KAAKzB,WAAL,CAAiBoD,GAAjB,CAAqB3B,MAAM,CAACU,QAA5B,CAAJ,EAA2C;AACvC,qBAAKnC,WAAL,CAAiBsB,GAAjB,CAAqBG,MAAM,CAACU,QAA5B,EAAsCP,IAAtC,CAA2CH,MAA3C;AACH,eAFD,MAEO;AACH,qBAAKzB,WAAL,CAAiB6B,GAAjB,CAAqBJ,MAAM,CAACU,QAA5B,EAAsC,CAACV,MAAD,CAAtC;AACH;AACJ;;AAEDA,YAAAA,MAAM,CAAC4B,OAAP;AACA5B,YAAAA,MAAM,CAACE,IAAP,CAAY2B,WAAZ,CAAwB,CAAxB,EAA2B,CAA3B;AAEA,mBAAO7B,MAAP;AACH,WA/CD,CA+CE,OAAOX,KAAP,EAAc;AACZ,kBAAM4B,MAAM,GAAG,KAAKR,SAAL,CAAeC,QAAf,CAAf;AACA,kBAAMQ,QAAQ,GAAG,KAAKC,WAAL,CAAiBF,MAAjB,EAAyBD,OAAzB,CAAjB;AACA3D,YAAAA,GAAG,CAAC,iBAAD,EAAoB,CAApB,EAAuBqD,QAAvB,EAAiCM,OAAjC,EAA0CC,MAAM,GAAGA,MAAM,CAACO,OAAV,GAAoB,CAApE,EAAuEN,QAAvE,CAAH;AACA,mBAAO,IAAP;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIY,QAAAA,YAAY,CAAC9B,MAAD,EAAiB+B,gBAAyB,GAAG,IAA7C,EAAmD;AAC3D,cAAIC,UAAJ;AACA,cAAIC,KAAJ;;AAEA,cAAIjC,MAAM,CAACuB,KAAX,EAAkB;AACdS,YAAAA,UAAU,GAAG,KAAKxD,YAAL,CAAkBqB,GAAlB,CAAsBG,MAAM,CAACU,QAA7B,CAAb;;AACA,gBAAIsB,UAAJ,EAAgB;AACZC,cAAAA,KAAK,GAAGD,UAAU,CAACE,OAAX,CAAmBlC,MAAnB,CAAR;;AACA,kBAAIiC,KAAK,IAAI,CAAb,EAAgB;AACZD,gBAAAA,UAAU,CAACG,MAAX,CAAkBF,KAAlB,EAAyB,CAAzB;AACA,qBAAKG,MAAL,CAAYpC,MAAZ;AACH;AACJ;;AACD,gBAAI+B,gBAAgB,IAAI/B,MAAM,CAACqC,YAA/B,EAA6C;AACzC,kBAAIrC,MAAM,CAACqC,YAAP;AAAA;AAAA,6CAA4CrC,MAAM,CAACqC,YAAP;AAAA;AAAA,uCAAhD,EAAyF;AACrFrC,gBAAAA,MAAM,CAACqC,YAAP,CAAoBP,YAApB,CAAiC9B,MAAjC;AACH;AACJ;AACJ,WAdD,MAcO;AACHgC,YAAAA,UAAU,GAAG,KAAKzD,WAAL,CAAiBsB,GAAjB,CAAqBG,MAAM,CAACU,QAA5B,CAAb;;AACA,gBAAIsB,UAAJ,EAAgB;AACZC,cAAAA,KAAK,GAAGD,UAAU,CAACE,OAAX,CAAmBlC,MAAnB,CAAR;;AACA,kBAAIiC,KAAK,IAAI,CAAb,EAAgB;AACZD,gBAAAA,UAAU,CAACG,MAAX,CAAkBF,KAAlB,EAAyB,CAAzB;AACA,qBAAKG,MAAL,CAAYpC,MAAZ;AACH,eAHD,MAGO;AACH3C,gBAAAA,GAAG,CAAC,WAAD,CAAH;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACI+E,QAAAA,MAAM,CAACpC,MAAD,EAAgBsC,YAAY,GAAG,KAA/B,EAAsC;AACxCtC,UAAAA,MAAM,CAACuC,aAAP;;AACA,cAAIvC,MAAM,CAACE,IAAX,EAAiB;AACb,gBAAIF,MAAM,CAACwC,OAAP,OAAqB,EAAzB,EAA6B;AACzBxC,cAAAA,MAAM,CAACE,IAAP,CAAYuC,MAAZ,GAAqB,IAArB;AACAzC,cAAAA,MAAM,CAACE,IAAP,CAAYwC,QAAZ,CAAqB1C,MAAM,CAACE,IAAP,CAAYyC,QAAZ,GAAuBC,CAA5C,EAA8C,CAA9C;AACH;;AACD,kBAAM3B,MAAM,GAAG,KAAKR,SAAL,CAAeT,MAAM,CAACU,QAAtB,CAAf;AACA,kBAAMQ,QAAQ,GAAG,KAAKC,WAAL,CAAiBF,MAAjB,EAAyBjB,MAAM,CAACuB,KAAhC,CAAjB;;AAEA,gBAAI,KAAKlD,cAAL,CAAoBsD,GAApB,CAAwBT,QAAxB,CAAJ,EAAuC;AACnC,kBAAI9D,OAAO,CAAC4C,MAAD,CAAX,EAAqB;AACjB,qBAAK3B,cAAL,CAAoBwB,GAApB,CAAwBqB,QAAxB,EAAkCf,IAAlC,CAAuCH,MAAvC;AACH;AACJ,aAJD,MAIO;AACH,mBAAK3B,cAAL,CAAoB+B,GAApB,CAAwBc,QAAxB,EAAkC,CAAClB,MAAD,CAAlC;AACH;AACJ,WAfD,MAeO,IAAI5C,OAAO,CAAC4C,MAAD,CAAX,EAAqB;AACxBA,YAAAA,MAAM,CAAC6C,OAAP;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AAEIC,QAAAA,kBAAkB,CAACC,SAAS,GAAG,KAAb,EAAoB;AAClC,cAAIA,SAAJ,EAAe;AACX,iBAAKvE,YAAL,CAAkBwE,OAAlB,CAA2BhB,UAAD,IAAgB;AACtC,kBAAIiB,KAAK,GAAG,CAAZ;;AACA,qBAAOjB,UAAU,CAAC1B,MAAX,GAAoB,CAApB,IAAyB2C,KAAK,GAAG,IAAxC,EAA8C;AAC1CjB,gBAAAA,UAAU,CAAC,CAAD,CAAV,CAAce,SAAd;AACAE,gBAAAA,KAAK;AACR;AACJ,aAND;AAOH,WARD,MAQO;AACH,iBAAKzE,YAAL,CAAkBwE,OAAlB,CAA2BhB,UAAD,IAAgB;AACtCA,cAAAA,UAAU,CAACgB,OAAX,CAAoBhD,MAAD,IAAmB;AAClC,qBAAKoC,MAAL,CAAYpC,MAAZ,EAAoB,IAApB;AACH,eAFD;AAGH,aAJD;AAKH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,SAAS,CAAC6C,UAAU,GAAG,KAAd,EAAqBZ,YAAY,GAAG,KAApC,EAA2C;AAChD,eAAKQ,kBAAL;AAEA,eAAKvE,WAAL,CAAiByE,OAAjB,CAA0BhB,UAAD,IAAgB;AACrCA,YAAAA,UAAU,CAACgB,OAAX,CAAoBhD,MAAD,IAAoB;AACnC,mBAAKoC,MAAL,CAAYpC,MAAZ,EAAoB,IAApB;AACH,aAFD;AAGH,WAJD;AAMA,eAAK3B,cAAL,CAAoB2E,OAApB,CAA6BhB,UAAD,IAAgB;AACxCA,YAAAA,UAAU,CAACgB,OAAX,CAAoBhD,MAAD,IAAoB;AACnC,kBAAI;AACA,oBAAI,CAACkD,UAAD,IAAe,KAAKzE,WAAL,CAAiB6B,MAAjB,GAA0B,KAAK5B,YAAlD,EAAgE;AAC5D,sBAAItB,OAAO,CAAC4C,MAAD,CAAX,EAAqB;AACjBA,oBAAAA,MAAM,CAACE,IAAP,CAAYuC,MAAZ,GAAqB,IAArB;AACA,yBAAKhE,WAAL,CAAiB0B,IAAjB,CAAsBH,MAAtB;AACH;AACJ,iBALD,MAKO;AACHA,kBAAAA,MAAM,CAACE,IAAP,CAAY2C,OAAZ;AACH;AACJ,eATD,CASE,OAAOxD,KAAP,EAAc;AACZhC,gBAAAA,GAAG,CAAC,wBAAD,CAAH;AACH;AACJ,aAbD;AAcH,WAfD;;AAiBA,cAAI6F,UAAJ,EAAgB;AACZ,iBAAKzE,WAAL,CAAiBuE,OAAjB,CAA0B9C,IAAD,IAAU;AAC/B,kBAAIA,IAAI,CAACA,IAAT,EAAeA,IAAI,CAACA,IAAL,CAAU2C,OAAV;AAClB,aAFD;AAGA,iBAAKpE,WAAL,GAAmB,EAAnB;AACH;;AAED,eAAKJ,cAAL,CAAoBqB,KAApB;AACA,eAAKlB,YAAL,CAAkBkB,KAAlB;AACA,eAAKnB,WAAL,CAAiBmB,KAAjB,GAnCgD,CAoChD;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACyB,cAAfO,eAAe,CAACkD,UAAD,EAAoBnC,OAApB,EAAqD;AACtE,cAAIhB,MAAJ;;AAEA,kBAAQmD,UAAR;AACI;AAAS;AACL,kBAAI,KAAK1E,WAAL,CAAiB6B,MAAjB,GAA0B,CAA9B,EAAiC;AAC7BN,gBAAAA,MAAM,GAAG,KAAKvB,WAAL,CAAiB4C,GAAjB,EAAT;;AACA,oBAAI,CAACjE,OAAO,CAAC4C,MAAD,CAAZ,EAAsB;AAClBA,kBAAAA,MAAM,GAAG,MAAM;AAAA;AAAA,0CAAQO,aAAR,CAAsB6C,eAAtB;AAAA;AAAA,uCAAf;AACH;AACJ,eALD,MAKO;AACHpD,gBAAAA,MAAM,GAAG,MAAM;AAAA;AAAA,wCAAQO,aAAR,CAAsB6C,eAAtB;AAAA;AAAA,qCAAf;AACH;;AATT;;AAYA,iBAAOpD,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACImB,QAAAA,WAAW,CAACF,MAAD,EAAuBD,OAAvB,EAAyC;AAChD,cAAIA,OAAJ,EAAa;AACT,gBAAIC,MAAM,CAACO,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAIP,MAAM,CAACO,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAIP,MAAM,CAACO,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,mBAAO,IAAP;AACH,WALD,MAKO;AACH,gBAAIP,MAAM,CAACO,OAAP,KAAmB,EAAnB,IAAyBP,MAAM,CAACO,OAAP,KAAmB,EAAhD,EAAoD,OAAO,KAAP;AACpD,gBAAIP,MAAM,CAACO,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAIP,MAAM,CAACO,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAIP,MAAM,CAACO,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,mBAAQ,IAAGP,MAAM,CAACoC,EAAG,EAArB;AACH;AACJ;;AAzT0D,O", "sourcesContent": ["import { isValid, log, Prefab, SpriteAtlas } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { GameFunc } from \"../GameFunc\";\r\nimport Bullet from \"../ui/bullet/Bullet\";\r\nimport EnemyBase from \"../ui/plane/enemy/EnemyBase\";\r\nimport BossBase from \"../ui/plane/boss/BossBase\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport { Bullet as BulletConfig } from \"db://assets/scripts/AutoGen/Luban/schema\"\r\n\r\nexport class BulletManager extends SingletonBase<BulletManager>{\r\n\r\n    _preloadFinish = false;\r\n    _mainStage = 0;\r\n    mainBulletAtlas: SpriteAtlas | null = null;\r\n    enemyBulletAtlas: SpriteAtlas | null = null;\r\n    enemyComAtlas: SpriteAtlas | null = null;\r\n    m_unUseBullets = new Map();\r\n    selfBullets = new Map();\r\n    enemyBullets = new Map();\r\n    m_nodeCatch:Bullet[] = [];\r\n    _bulletCount = 120;\r\n    _testIds: number[] = [];\r\n\r\n    preLoad(stage = 0) {\r\n        GameIns.battleManager.addLoadCount(1);;\r\n        const spriteAtlases = [GameResourceList.atlas_enemyBullet, GameResourceList.atlas_mainBullet];\r\n        MyApp.resMgr.load(spriteAtlases, SpriteAtlas, (error: Error,atlas:SpriteAtlas[]) => {\r\n            this.enemyComAtlas = atlas[0];\r\n            this.mainBulletAtlas = atlas[1];\r\n            GameIns.battleManager.checkLoadFinish();;\r\n        });\r\n\r\n        GameIns.battleManager.addLoadCount(1);\r\n        const prefabs = [\r\n            GameResourceList.Bullet\r\n        ];\r\n\r\n        MyApp.resMgr.load(prefabs, Prefab, () => {\r\n            GameIns.battleManager.checkLoadFinish();;\r\n        });\r\n\r\n        if (stage > 0) {\r\n            GameIns.battleManager.addLoadCount(1);;\r\n            MyApp.resMgr.load(GameResourceList.atlas_enemyBullet1, SpriteAtlas,(error: Error, atlas: SpriteAtlas) => {\r\n                this.enemyBulletAtlas = atlas;\r\n                GameIns.battleManager.checkLoadFinish();;\r\n            });\r\n        }\r\n    }\r\n\r\n    clear() {\r\n        if (this.enemyBulletAtlas) {\r\n            MyApp.resMgr.releaseAssetByForce(this.enemyBulletAtlas);\r\n            this.enemyBulletAtlas = null;\r\n        }\r\n    }\r\n\r\n    async battleInit() {\r\n        if (!this.m_unUseBullets.get(\"e1\")) {\r\n            const bullets = [];\r\n            for (let i = 0; i < 150; i++) {\r\n                const bullet =  await this.createNewBullet(1, true);\r\n                if (bullet.node && isValid(bullet)) {\r\n                    bullets.push(bullet);\r\n                }\r\n            }\r\n            this.m_unUseBullets.set(\"e1\", bullets);\r\n        }\r\n        this.removeAll();\r\n        if (this.m_nodeCatch.length < this._bulletCount) {\r\n            await GameIns.prefabManager.createFrame(\r\n                Bullet,\r\n                this._bulletCount - this.m_nodeCatch.length,\r\n                this.m_nodeCatch,\r\n                10\r\n            );\r\n        }\r\n    }\r\n\r\n    getConfig(bulletID:number) {\r\n        if (!this._testIds.includes(bulletID)) {\r\n            console.log(\"getBullet\", bulletID);\r\n            this._testIds.push(bulletID);\r\n        }\r\n        return MyApp.lubanTables.TbBullet.get(bulletID);\r\n    }\r\n\r\n    /**\r\n     * 获取子弹实例\r\n     * @param {number} bulletID 子弹ID\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @returns {Bullet} 子弹实例\r\n     */\r\n    async getBullet(bulletID: number, isEnemy: boolean) {\r\n        try {\r\n            const config = this.getConfig(bulletID);\r\n            if (!config) return null;\r\n\r\n            const saveType = this.getSaveType(config, isEnemy);\r\n            let unusedBullets = this.m_unUseBullets.get(saveType);\r\n            let bullet : Bullet;\r\n\r\n            if (unusedBullets && unusedBullets.length > 0) {\r\n                bullet = unusedBullets.pop();\r\n                if (isValid(bullet) && isValid(bullet.node)) {\r\n                    bullet.create(bulletID);\r\n                    bullet.enemy = isEnemy;\r\n                } else {\r\n                    log(\"bullet not valid\", bulletID, saveType);\r\n                    bullet = await this.createNewBullet(config.bustyle, isEnemy);\r\n                    bullet.bulletID = bulletID;\r\n                    bullet.enemy = isEnemy;\r\n                    bullet.create(bulletID);\r\n                }\r\n            } else {\r\n                bullet = await this.createNewBullet(config.bustyle, isEnemy);\r\n                bullet.bulletID = bulletID;\r\n                bullet.enemy = isEnemy;\r\n                bullet.create(bulletID);\r\n            }\r\n\r\n            bullet.new_uuid = GameFunc.uuid;\r\n\r\n            if (bullet.enemy) {\r\n                if (this.enemyBullets.has(bullet.bulletID)) {\r\n                    this.enemyBullets.get(bullet.bulletID).push(bullet);\r\n                } else {\r\n                    this.enemyBullets.set(bullet.bulletID, [bullet]);\r\n                }\r\n            } else {\r\n                if (this.selfBullets.has(bullet.bulletID)) {\r\n                    this.selfBullets.get(bullet.bulletID).push(bullet);\r\n                } else {\r\n                    this.selfBullets.set(bullet.bulletID, [bullet]);\r\n                }\r\n            }\r\n\r\n            bullet.refresh();\r\n            bullet.node.setPosition(0, 0);\r\n\r\n            return bullet;\r\n        } catch (error) {\r\n            const config = this.getConfig(bulletID) as BulletConfig;\r\n            const saveType = this.getSaveType(config, isEnemy);\r\n            log(\"getBullet error\", 0, bulletID, isEnemy, config ? config.bustyle : 0, saveType);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     * @param {Bullet} bullet 子弹实例\r\n     * @param {boolean} removeFromEntity 是否从实体中移除\r\n     */\r\n    removeBullet(bullet: Bullet, removeFromEntity: boolean = true) {\r\n        let bulletList;\r\n        let index;\r\n\r\n        if (bullet.enemy) {\r\n            bulletList = this.enemyBullets.get(bullet.bulletID);\r\n            if (bulletList) {\r\n                index = bulletList.indexOf(bullet);\r\n                if (index >= 0) {\r\n                    bulletList.splice(index, 1);\r\n                    this.remove(bullet);\r\n                }\r\n            }\r\n            if (removeFromEntity && bullet.m_mainEntity) {\r\n                if (bullet.m_mainEntity instanceof EnemyBase || bullet.m_mainEntity instanceof BossBase) {\r\n                    bullet.m_mainEntity.removeBullet(bullet);\r\n                }\r\n            }\r\n        } else {\r\n            bulletList = this.selfBullets.get(bullet.bulletID);\r\n            if (bulletList) {\r\n                index = bulletList.indexOf(bullet);\r\n                if (index >= 0) {\r\n                    bulletList.splice(index, 1);\r\n                    this.remove(bullet);\r\n                } else {\r\n                    log(\"b11 11111\");\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹并回收\r\n     * @param {Bullet} bullet 子弹实例\r\n     * @param {boolean} forceDestroy 是否强制销毁\r\n     */\r\n    remove(bullet:Bullet, forceDestroy = false) {\r\n        bullet.removeAllComp();\r\n        if (bullet.node) {\r\n            if (bullet.getType() !== 41) {\r\n                bullet.node.parent = null;\r\n                bullet.node.setScale(bullet.node.getScale().x,1);\r\n            }\r\n            const config = this.getConfig(bullet.bulletID) as BulletConfig;\r\n            const saveType = this.getSaveType(config, bullet.enemy);\r\n\r\n            if (this.m_unUseBullets.has(saveType)) {\r\n                if (isValid(bullet)) {\r\n                    this.m_unUseBullets.get(saveType).push(bullet);\r\n                }\r\n            } else {\r\n                this.m_unUseBullets.set(saveType, [bullet]);\r\n            }\r\n        } else if (isValid(bullet)) {\r\n            bullet.destroy();\r\n        }\r\n    }\r\n    /**\r\n\r\n    /**\r\n     * 移除所有敌方子弹\r\n     * @param {boolean} dieRemove 是否调用子弹的死亡移除逻辑\r\n     */\r\n    removeEnemyBullets(dieRemove = false) {\r\n        if (dieRemove) {\r\n            this.enemyBullets.forEach((bulletList) => {\r\n                let count = 0;\r\n                while (bulletList.length > 0 && count < 9999) {\r\n                    bulletList[0].dieRemove();\r\n                    count++;\r\n                }\r\n            });\r\n        } else {\r\n            this.enemyBullets.forEach((bulletList) => {\r\n                bulletList.forEach((bullet:Bullet) => {\r\n                    this.remove(bullet, true);\r\n                });\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除所有子弹\r\n     * @param {boolean} destroyAll 是否销毁所有子弹\r\n     * @param {boolean} forceDestroy 是否强制销毁\r\n     */\r\n    removeAll(destroyAll = false, forceDestroy = false) {\r\n        this.removeEnemyBullets();\r\n\r\n        this.selfBullets.forEach((bulletList) => {\r\n            bulletList.forEach((bullet: Bullet) => {\r\n                this.remove(bullet, true);\r\n            });\r\n        });\r\n\r\n        this.m_unUseBullets.forEach((bulletList) => {\r\n            bulletList.forEach((bullet: Bullet) => {\r\n                try {\r\n                    if (!destroyAll && this.m_nodeCatch.length < this._bulletCount) {\r\n                        if (isValid(bullet)) {\r\n                            bullet.node.parent = null;\r\n                            this.m_nodeCatch.push(bullet);\r\n                        }\r\n                    } else {\r\n                        bullet.node.destroy();\r\n                    }\r\n                } catch (error) {\r\n                    log(\"bullet removeAll error\");\r\n                }\r\n            });\r\n        });\r\n\r\n        if (destroyAll) {\r\n            this.m_nodeCatch.forEach((node) => {\r\n                if (node.node) node.node.destroy();\r\n            });\r\n            this.m_nodeCatch = [];\r\n        }\r\n\r\n        this.m_unUseBullets.clear();\r\n        this.enemyBullets.clear();\r\n        this.selfBullets.clear();\r\n        // this.putAllStreak();\r\n    }\r\n    /**\r\n     * 创建新的子弹实例\r\n     * @param {number} bulletType 子弹类型\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @returns {Bullet} 子弹实例\r\n     */\r\n    async createNewBullet(bulletType:number, isEnemy:boolean):Promise<Bullet> {\r\n        let bullet: Bullet;\r\n\r\n        switch (bulletType) {\r\n            default: // 默认子弹\r\n                if (this.m_nodeCatch.length > 0) {\r\n                    bullet = this.m_nodeCatch.pop()!;\r\n                    if (!isValid(bullet)) {\r\n                        bullet = await GameIns.prefabManager.createComponent(Bullet) as Bullet;\r\n                    }\r\n                } else {\r\n                    bullet = await GameIns.prefabManager.createComponent(Bullet) as Bullet;\r\n                }\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    /**\r\n     * 获取子弹的保存类型\r\n     * @param {BulletConfig} config 子弹配置\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @returns {string} 保存类型\r\n     */\r\n    getSaveType(config: BulletConfig, isEnemy: boolean) {\r\n        if (isEnemy) {\r\n            if (config.bustyle === 23) return \"e23\";\r\n            if (config.bustyle === 26) return \"e26\";\r\n            if (config.bustyle === 39) return \"e39\";\r\n            return \"e1\";\r\n        } else {\r\n            if (config.bustyle === 27 || config.bustyle === 28) return \"f27\";\r\n            if (config.bustyle === 23) return \"f23\";\r\n            if (config.bustyle === 24) return \"f24\";\r\n            if (config.bustyle === 41) return \"f41\";\r\n            return `f${config.id}`;\r\n        }\r\n    }\r\n}"]}
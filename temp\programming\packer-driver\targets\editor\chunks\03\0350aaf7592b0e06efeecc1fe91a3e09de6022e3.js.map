{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameResManager.ts"], "names": ["_decorator", "Prefab", "GameIns", "SingletonBase", "MyApp", "GameResourceList", "ccclass", "GameResManager", "frameAnim", "preload", "battleManager", "addLoadCount", "resMgr", "loadAsync", "FrameAnim", "checkLoadFinish"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAmCC,MAAAA,M,OAAAA,M;;AACnCC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,gB;;;;;;;;;OAED;AAAEC,QAAAA;AAAF,O,GAAcN,U;;yBAGCO,c,WADpBD,OAAO,CAAC,gBAAD,C,gBAAR,MACqBC,cADrB;AAAA;AAAA,0CAC0E;AAAA;AAAA;AAAA,eACtEC,SADsE,GAClD,IADkD;AAAA;;AAGzD,cAAPC,OAAO,GAAE;AACX,cAAI,CAAC,KAAKD,SAAV,EAAoB;AAChB;AAAA;AAAA,oCAAQE,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA,iBAAKH,SAAL,GAAiB,MAAM;AAAA;AAAA,gCAAMI,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,sDAAiBC,SAAxC,EAAkDb,MAAlD,CAAvB;AACA;AAAA;AAAA,oCAAQS,aAAR,CAAsBK,eAAtB;AACH;AACJ;;AATqE,O", "sourcesContent": ["import { _decorator, NodePool, instantiate, Prefab, Node} from 'cc';\r\nimport { GameIns } from '../GameIns';\r\nimport { SingletonBase } from '../../core/base/SingletonBase';\r\nimport { MyApp } from '../../MyApp';\r\nimport GameResourceList from '../const/GameResourceList';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass('GameResManager')\r\nexport default class GameResManager extends SingletonBase<GameResManager> {\r\n    frameAnim: Prefab = null;\r\n\r\n    async preload(){\r\n        if (!this.frameAnim){\r\n            GameIns.battleManager.addLoadCount(1);\r\n            this.frameAnim = await MyApp.resMgr.loadAsync(GameResourceList.FrameAnim,Prefab);\r\n            GameIns.battleManager.checkLoadFinish();\r\n        }\r\n    }\r\n}"]}
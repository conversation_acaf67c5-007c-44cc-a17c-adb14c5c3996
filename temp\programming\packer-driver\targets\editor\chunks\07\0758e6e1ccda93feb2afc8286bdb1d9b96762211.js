System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Node, csproto, DataMgr, EventMgr, MyApp, logError, ButtonPlus, TopBlockInputUI, UIMgr, PlaneCombineResultUI, PlaneUIEvent, TabStatus, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, CombineDisplay;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/scripts/Data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/scripts/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopBlockInputUI(extras) {
    _reporterNs.report("TopBlockInputUI", "../../../../common/TopBlockInputUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneCombineResultUI(extras) {
    _reporterNs.report("PlaneCombineResultUI", "../../PlaneCombineResultUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../PlaneEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      DataMgr = _unresolved_3.DataMgr;
    }, function (_unresolved_4) {
      EventMgr = _unresolved_4.EventMgr;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      logError = _unresolved_6.logError;
    }, function (_unresolved_7) {
      ButtonPlus = _unresolved_7.ButtonPlus;
    }, function (_unresolved_8) {
      TopBlockInputUI = _unresolved_8.TopBlockInputUI;
    }, function (_unresolved_9) {
      UIMgr = _unresolved_9.UIMgr;
    }, function (_unresolved_10) {
      PlaneCombineResultUI = _unresolved_10.PlaneCombineResultUI;
    }, function (_unresolved_11) {
      PlaneUIEvent = _unresolved_11.PlaneUIEvent;
    }, function (_unresolved_12) {
      TabStatus = _unresolved_12.TabStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3468c0U+mpO2JMS8dUZFGdC", "CombineDisplay", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("CombineDisplay", CombineDisplay = (_dec = ccclass('CombineDisplay'), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Label), _dec5 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec6 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class CombineDisplay extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "topGrid", _descriptor, this);

          _initializerDefineProperty(this, "bottomGrid", _descriptor2, this);

          _initializerDefineProperty(this, "tip", _descriptor3, this);

          _initializerDefineProperty(this, "combineOnceBtn", _descriptor4, this);

          _initializerDefineProperty(this, "combineAllBtn", _descriptor5, this);

          this._tips = ["选择你想合成的装备", "还需要2件相同装备", "还需要1件相同装备", "一切准备就绪!"];
        }

        onLoad() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).TabChange, this.onTabChange, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).BagItemClick, this.onBagItemClick, this, 1);
          this.bottomGrid.getComponentsInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).forEach(btn => {
            btn.addClick(this.onBottomBtnClick, this);
          });
          this.combineOnceBtn.addClick(this.onCombineOnceClick, this);
          this.combineAllBtn.addClick(this.onCombineAllClick, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this);
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.unregisterHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this);
        }

        onTabChange(tabStatus) {
          if (tabStatus == (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Bag) {
            this.node.active = false;
            return;
          }

          this.node.active = true;
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.clear();
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.prepareCombineAll();
          this.refreshDisplay();
        }

        refreshDisplay() {
          const mergeLen = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.size();
          this.bottomGrid.getComponentsInChildren(Label).forEach((label, index) => {
            const info = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.getByPos(index);

            if (info) {
              var _lubanTables$TbEquip$;

              label.string = (_lubanTables$TbEquip$ = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).lubanTables.TbEquip.get(info.item.item_id)) == null ? void 0 : _lubanTables$TbEquip$.name;
            } else {
              label.string = "合成材料";
            }
          });
          this.tip.string = this._tips[mergeLen];

          if ((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.isFull()) {
            const nextLev = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.getCombineResult();

            if (nextLev) {
              this.topGrid.getComponentInChildren(Label).string = nextLev.name;
            } else {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("PlaneUI", `cant get merge result no pos1 equip info`);
            }
          } else {
            this.topGrid.getComponentInChildren(Label).string = "合成结果";
          }

          this.combineOnceBtn.node.active = mergeLen == 3;
          this.combineAllBtn.node.active = mergeLen != 3;
        }

        onBagItemClick(item) {
          if (!this.node.active) return;
          const info = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.getByGuid(item.guid);

          if (info) {
            (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.deleteByGuid(item.guid);
          } else {
            if (!(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.add(item)) return;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).UpdateBagGrids);
          this.refreshDisplay();
        }

        onBottomBtnClick(event) {
          const nd = event.target;
          const pos = parseInt(nd.name);
          if (!(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.getByPos(pos)) return;
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.deleteByPos(pos);
          this.refreshDisplay();
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).UpdateBagGrids);
        }

        onCombineResultMsg(msg) {
          var _msg$body;

          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).bag.refreshItems();
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.clear();
          this.refreshDisplay();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PlaneCombineResultUI === void 0 ? (_reportPossibleCrUseOfPlaneCombineResultUI({
            error: Error()
          }), PlaneCombineResultUI) : PlaneCombineResultUI, (_msg$body = msg.body) == null || (_msg$body = _msg$body.equip_combine) == null ? void 0 : _msg$body.results);
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).hideUI(_crd && TopBlockInputUI === void 0 ? (_reportPossibleCrUseOfTopBlockInputUI({
            error: Error()
          }), TopBlockInputUI) : TopBlockInputUI);
        }

        onCombineOnceClick() {
          if (!(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.isFull()) return;
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TopBlockInputUI === void 0 ? (_reportPossibleCrUseOfTopBlockInputUI({
            error: Error()
          }), TopBlockInputUI) : TopBlockInputUI);
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.combine();
        }

        onCombineAllClick() {
          if ((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.isFull()) return;
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TopBlockInputUI === void 0 ? (_reportPossibleCrUseOfTopBlockInputUI({
            error: Error()
          }), TopBlockInputUI) : TopBlockInputUI);
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.combineAll();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "topGrid", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "bottomGrid", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "tip", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "combineOnceBtn", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "combineAllBtn", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0758e6e1ccda93feb2afc8286bdb1d9b96762211.js.map
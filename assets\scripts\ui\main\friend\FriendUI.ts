import { _decorator, instantiate, Label, Node, Prefab, resources } from 'cc';
import { EventMgr } from '../../../event/EventManager';
import { ButtonPlus } from '../../common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr } from '../../UIMgr';
import { BattleUI } from '../BattleUI';
import { Tabs } from '../plane/components/back_pack/Tabs';
import { PlaneUIEvent } from '../plane/PlaneEvent';
import { TabStatus } from '../plane/PlaneTypes';
import { PopupUI } from '../PopupUI';

const { ccclass, property } = _decorator;

@ccclass('FriendUI')
export class FriendUI extends BaseUI {

    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;

    @property(Tabs)
    tabs: Tabs | null = null;

    @property(Node)
    panel1: Node | null = null;
    @property(Node)
    panel2: Node | null = null;
    @property(Node)
    panel3: Node | null = null;

    @property(Node)
    node1: Node | null = null;
    @property(ButtonPlus)
    btnGet: ButtonPlus | null = null;
    @property(Label)
    LabelTimes: Label | null = null;
    @property(Label)
    LabelUpdate: Label | null = null;

    @property(Node)
    node2: Node | null = null;
    @property(ButtonPlus)
    btnIgnoreAll: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnAgreeAll: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnRefreshAll: ButtonPlus | null = null;

    public static getUrl(): string { return "ui/main/friend/FriendUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    protected onLoad(): void {
        this.tabs!.init();
        this.updateLabelAfterColon(this.LabelTimes!, "50", "100");
        this.updateLabelAfterColon(this.tabs!.tabBagBtn!.getComponentInChildren(Label)!, "30", "100");
        this.updateLabelAfterColon(this.LabelUpdate!,
            ((timestamp: number) => {
                const date = new Date(timestamp);
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                return `${hours}时${minutes}分`;
            })(Date.now() + 3 * 60 * 60 * 1000)
        );
        this.btnClose!.addClick(this.closeUI, this);
        this.btnGet!.addClick(this.onPower, this);
        this.btnIgnoreAll!.addClick(this.onIgnore, this);
        this.btnAgreeAll!.addClick(this.onAgree, this);
        this.btnRefreshAll!.addClick(this.onRefresh, this);
        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this);

        resources.load("ui/main/friend/FriendListUI", Prefab, (err, asset) => {
            if (err) {
                console.error("资源加载失败:", err);
                return;
            }

            const node = instantiate(asset);
            this.panel1!.addChild(node);

            console.log("资源加载并添加到 panel 完成");
        });

        resources.load("ui/main/friend/FriendAddUI", Prefab, (err, asset) => {
            if (err) {
                console.error("资源加载失败:", err);
                return;
            }
            const node = instantiate(asset);
            this.panel2!.addChild(node);
            console.log("资源加载并添加到 panel 完成");
        });

        resources.load("ui/main/friend/FriendStrangerUI", Prefab, (err, asset) => {
            if (err) {
                console.error("资源加载失败:", err);
                return;
            }
            const node = instantiate(asset);
            this.panel3!.addChild(node);
            console.log("资源加载并添加到 panel 完成");
        });
        this.panel2!.active = false;
        this.panel3!.active = false;
        this.node2!.active = false;
    }
    private onTabChange(tabStatus: TabStatus) {
        if (tabStatus == TabStatus.Bag) {
            this.node1!.active = true;
            this.node2!.active = false;
            this.panel1!.active = true;
            this.panel2!.active = false;
            this.panel3!.active = false;
        } else {
            this.node1!.active = false;
            this.node2!.active = true;
            this.panel1!.active = false;
            this.panel2!.active = true;
            this.panel3!.active = true;
        }
    }
    public updateLabelAfterColon(label: Label, ...args: string[]): void {
        const originalText = label.string;
        let colonIndex = originalText.indexOf(":");
        if (colonIndex === -1) {
            colonIndex = originalText.indexOf("："); // 中文冒号
        }
        let formattedValue: string;
        if (args.length === 1) {
            formattedValue = args[0];
        } else if (args.length === 2) {
            formattedValue = `${args[0]}/${args[1]}`;
        } else if (args.length > 2) {
            formattedValue = args.join(",");
        } else {
            formattedValue = "";
        }
        if (colonIndex === -1) {
            label.string = `${originalText}:${formattedValue}`;
            return;
        }
        const prefix = originalText.substring(0, colonIndex + 1); // 包含冒号
        label.string = `${prefix}${formattedValue}`;
    }

    async closeUI() {
        UIMgr.closeUI(FriendUI);
        await UIMgr.openUI(BattleUI)
    }
    private onPower() {
        UIMgr.openUI(PopupUI, "一键收赠");
    }
    private onIgnore() {
        UIMgr.openUI(PopupUI, "全部忽略");
    }
    private onAgree() {
        UIMgr.openUI(PopupUI, "全部同意");
    }
    private onRefresh() {
        UIMgr.openUI(PopupUI, "刷新陌生人");
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this);
    }
}
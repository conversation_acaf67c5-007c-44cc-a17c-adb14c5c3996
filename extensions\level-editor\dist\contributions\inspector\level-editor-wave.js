'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
exports.template = `
<ui-prop type="dump" class="wavePrefab"></ui-prop>
<ui-prop type="dump" class="planeID"></ui-prop>
<ui-prop type="dump" class="params"></ui-prop>
<ui-prop type="dump" class="conditions"></ui-prop>
<ui-section>
    <ui-prop slot="header">
        <ui-label slot="label">Conditions</ui-label>
        <ui-num-input slot="content" class="conditionCount" min="0" max="10" step="1"> </ui-num-input>
    </ui-prop>
</ui-section>
`;
exports.$ = {
    wavePrefab: '.wavePrefab',
    planeID: '.planeID',
    params: '.params',
    conditions: '.conditions',
    conditionCount: '.conditionCount'
};
// @ts-ignore
function update(dump) {
    // 使用 ui-porp 自动渲染，设置 prop 的 type 为 dump
    // render 传入一个 dump 数据，能够自动渲染出对应的界面
    // 自动渲染的界面修改后，能够自动提交数据
    this.dump = dump;
    this.$.wavePrefab.render(dump.value.wavePrefab);
    this.$.planeID.render(dump.value.planeID);
    this.$.params.render(dump.value.params);
    this.$.conditions.render(dump.value.conditions);
}
// @ts-ignore
function ready() {
}
//# sourceMappingURL=data:application/json;base64,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
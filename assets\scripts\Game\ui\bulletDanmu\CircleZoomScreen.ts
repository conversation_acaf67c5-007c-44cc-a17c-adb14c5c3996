import { _decorator } from "cc";
import BaseScreen from "./BaseScreen";
import CircleZoomFly from "../bullet/CircleZoomFly";

const { ccclass, property } = _decorator;

@ccclass("CircleZoomScreen")
export class CircleZoomScreen extends BaseScreen {
    private props: {
        r: number;
        difV: number;
        maxR: number;
        XV: number;
        YV: number;
        counts: number;
        face: number;
        nums: number;
        posOffset: any;
    };

    constructor(configID: number, mainEntity: any) {
        super();
        this.setData(configID, mainEntity);

        const para = this.m_config.para;
        const offset = this.m_config.offset;

        this.props = {
            r: para[0],
            difV: para[1],
            maxR: para[2],
            XV: para[3],
            YV: para[4],
            counts: para[5],
            face: para[6],
            nums: para[7],
            posOffset: offset
        };
    }

    async fire() {
        const angleStep = 360 / this.props.nums;

        if (this.props.counts === 1) {
            for (let i = 0; i < this.props.nums; i++) {
                const attackPoint = this.getAttackPoint();
                let x = attackPoint.x;
                let y = attackPoint.y;
                const bullet = await this.createBullet();
                let angle = i * angleStep;

                x += Math.cos((angle / 180) * Math.PI) * this.props.r;
                y += Math.sin((angle / 180) * Math.PI) * this.props.r;

                if (this.props.face === 1) {
                    angle -= 180;
                }

                if (bullet) {
                    bullet.init(this.m_enemy, { x, y, angle }, this.m_bulletState, this.m_mainEntity);
                    bullet.getComp('CircleZoomFly').setData(angle, this.m_mainEntity, this.props.r, this.props.maxR, this.props.r, attackPoint);
                }
            }
        } else {
            for (let i = 0; i < this.props.nums; i++) {
                for (let j = 0; j < this.props.counts; j++) {
                    const attackPoint = this.getAttackPoint();
                    let x = attackPoint.x;
                    let y = attackPoint.y;
                    const bullet = await this.createBullet();
                    const bulletRadius = bullet.skinImg.node.width / 2;
                    let angle = i * angleStep;

                    x += Math.cos((angle / 180) * Math.PI) * (this.props.r + bulletRadius * j);
                    y += Math.sin((angle / 180) * Math.PI) * (this.props.r + bulletRadius * j);

                    if (this.props.face === 1) {
                        angle -= 180;
                    }

                    if (bullet) {
                        bullet.init(this.m_enemy, { x, y, angle }, this.m_bulletState, this.m_mainEntity);
                        bullet.getComp('CircleZoomFly').setData(angle, this.m_mainEntity, this.props.r + bulletRadius * j, this.props.maxR, this.props.r, attackPoint);
                    }
                }
            }
        }
    }

    onInit() {
        // Initialization logic can be added here
    }

    update(deltaTime: number) {
        // Update logic can be added here
    }
}
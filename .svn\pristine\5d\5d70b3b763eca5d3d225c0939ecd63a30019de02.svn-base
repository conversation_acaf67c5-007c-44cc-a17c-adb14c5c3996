import { error, v2, Vec2 } from "cc";
import { Tools } from "../utils/Tools";
import { EnemyUI } from "../../AutoGen/Luban/schema";

export class EnemyAttackPointData {
    x: number = 0;
    y: number = 0;
    shootInterval: number = 0;
    bulletID: number = 0;
    bulletNum: number = 0;
    bulletInterval: number = 0;
    bulletAttackRate: number = 0;
    soundId: number = 0;
    soundDelay: number = 0;

    /**
     * 从 JSON 数据加载攻击点数据
     * @param positionData 位置数据
     * @param attackData 攻击数据
     */
    loadJson(positionData: string, attackData: string): void {
        try {
            const position = Tools.stringToNumber(positionData, ",");
            this.x = position[0];
            this.y = position[1];

            const attack = Tools.stringToNumber(attackData, ",");
            this.shootInterval = attack[0];
            this.bulletID = attack[1];
            this.bulletNum = attack[2];
            this.bulletInterval = attack[3];
            this.bulletAttackRate = attack[4] / 100;

            if (attack.length > 5) this.soundId = attack[5];
            if (attack.length > 6) this.soundDelay = attack[6];
        } catch (error) {
            // Log.e("EnemyAttackPointData error:", positionData, attackData);
        }
    }
}

export class EnemyAttrData {

    type = 0; // 属性类型
    param = ""; // 属性参数


    /**
     * 从 JSON 数据加载敌人属性数据
     * @param {Object} data 属性数据
     */
    loadJson(data: any) {
        if (data.hasOwnProperty("type")) {
            this.type = parseInt(data.type);
        }
        if (data.hasOwnProperty("param")) {
            this.param = data.param;
        }
    }
}

export class EnemyCollider {

    type = 0;
    x = 0;
    y = 0;
    width = 0;
    height = 0;

    /**
     * 从 JSON 数据加载碰撞器数据
     * @param {string} data 碰撞器数据
     */
    loadJson(data: string) {
        try {
            const values = Tools.stringToNumber(data, ',');
            this.type = values[0];
            this.x = values[1];
            this.y = values[2];
            this.width = values[3];
            this.height = values[4];
        } catch (error) {
            Tools.error('EnemyCollider error:', data);
        }
    }
}
/**
 * 敌机数据类
 */
export class EnemyPlaneData {
    id: number = 0;
    uiId: number = 0;
    hp: number = 0;
    dieBullet: boolean = false;
    attack: number = 0;
    defence: number = 0;
    collideLevel: number = 0;
    bTurnDir: boolean = false;
    collideAttack: number = 0;
    bCollideDead: boolean = false;
    bMoveAttack: boolean = false;
    bStayAttack: boolean = false;
    attackInterval: number = 0;
    attackNum: number = 0;
    dieShoot: any[] = [];
    attr: string = "";
    param: string = "";
    bAttackAbles: boolean[] = [];
    attackArrNums: number[] = [];
    attackPointArr: any[] = [];

    /**
     * 从 JSON 数据加载敌机数据
     * @param data JSON 数据
     */
    loadJson(data: any): void {
        this.id = data.id;
        this.uiId = data.uiId;
        this.attack = data.atk;
        this.hp = data.hp;
        this.collideLevel = data.collideLevel;
        this.bTurnDir = data.turn;
        this.collideAttack = data.collideAttack;
        this.bCollideDead = data.bCollideDead;
        this.bMoveAttack = data.bMoveAttack;
        this.bStayAttack = data.bStayAttack;
        this.attackInterval = data.attackInterval;
        this.attackNum = data.attackNum;
        this.param = data.param;
        this.dieShoot = data.dieShoot;
        this.dieBullet = data.dieBullet;


        // 0,-20;0,1002,1,0.3,100;0,-20;0,1003,1,1,100;0,-20;0,1004,1,1,100
        // 解析攻击点数据
        if (data.hasOwnProperty("attackData")) {
            const sections = data.attackData.split("#");

            for (let h = 0; h < sections.length; h++) {
                const attackGroups = []; // 二维数组，存储一个攻击点组的所有攻击点
                let isAttackAble = false;
                if (sections[h] !== "") {
                    const points = sections[h].split(";");
                    const attackPoints: any[] = []; // 一维数组，存储一个攻击点组中的单个攻击点

                    for (let n = 0; n < points.length; n += 2) {
                        if (points[n] !== "" && points[n + 1] !== "") {
                            const attackPoint = new EnemyAttackPointData();
                            attackPoint.loadJson(points[n], points[n + 1]);
                            attackPoints.push(attackPoint);
                            isAttackAble = true;
                        }
                    }

                    attackGroups.push(attackPoints); // 将当前攻击点组添加到二维数组
                }
                this.attackPointArr.push(attackGroups); // 将二维数组作为一个整体添加到三维数组
                this.attackArrNums.push(attackGroups.length); // 存储当前攻击点组的数量
                this.bAttackAbles.push(isAttackAble); // 存储当前攻击点组是否可用
            }
        }
    }
}



class TrackGroup {

    points = []; // 轨迹点数组

    /**
     * 从 JSON 数据加载轨迹组
     * @param {string} data 轨迹数据
     */
    loadJson(data: string) {
        Tools.stringToPoint(data, ',');
        /*const points = data.split(';');
        for (const point of points) {
            if (point !== '') {
                this.points.push(Tools.stringToPoint(point, ','));
            }
        }*/
    }
}

/**
 * 敌人数据类
 */
export class EnemyData {

    id = 0; // 敌人 ID
    pos = v2(0, 0); // 敌人位置
    trackArr: TrackGroup[] = []; // 敌人轨迹数组


    /**
     * 从 JSON 数据加载敌人数据
     * @param {Object} json JSON 数据
     */
    loadJson(json:any) {
        const dataParts = json.split("#");
        if (dataParts.length > 0) {
            const positionData = Tools.stringToNumber(dataParts[0], ",");
            this.id = positionData[0];
            this.pos = v2(positionData[1], positionData[2]);
        }

        if (dataParts.length > 1 && dataParts[1] !== "") {
            const trackData = dataParts[1].split("#");
            for (let i = 0; i < trackData.length; i++) {
                if (trackData[i] !== "" && trackData[i].split(";").length > 1) {
                    const trackGroup = new TrackGroup();
                    trackGroup.loadJson(trackData[i]);
                    this.trackArr.push(trackGroup);
                }
            }
        }
    }
}


export class EnemyUIData {
    id = 0; // 敌人 ID
    image = ''; // 敌人图片路径
    isAm: number = 0; // 是否为动画
    collider: number[] = []; // 碰撞器数据
    hpParam: number[] = []; // 血量参数
    damageParam: number[] = []; // 伤害参数
    clipArr = []; // 动画剪辑数组
    blastSound = 0; // 爆炸音效 ID
    blastCount = 0; // 爆炸次数
    blastParam: number[] = []; // 爆炸参数
    blastDurations: number[] = []; // 爆炸持续时间
    blastShake: number[] = []; // 爆炸震动参数
    extraParam: number[] = []; // 额外参数
    extraParam1 = ''; // 额外参数 1
    skillResistUIDict: Map<number, number[]> = new Map(); // 技能抗性字典
    lootParam0: number[] = []; // 掉落参数 0
    lootParam1: number[] = []; // 掉落参数 1
    sneakParam: number[] = []; // 潜行参数
    sneakAnim = ''; // 潜行动画
    showParam: number[] = []; // 显示参数

    /**
     * 从 JSON 数据加载敌人 UI 数据
     * @param {Object} data JSON 数据
     */
    loadJson(data: EnemyUI) {
        this.id = data.id;
        this.image = data.image;
        this.isAm = data.isAm;
        this.collider = Tools.stringToNumber(data.collider, ',');
        this.hpParam = Tools.stringToNumber(data.hpParam, ',');
        this.blastSound = data.blastSound;
        this.blastSound = data.blastSound;


        if (data.hasOwnProperty('blp')) {
            const params = data.blp.split(';');
            if (params.length > 0 && params[0] !== '') {
                this.blastCount = parseInt(params[0]);
                for (let i = 1; i < params.length; i++) {
                    if (params[i] !== '') {
                        this.blastParam = Tools.stringToNumber(params[i], ',');
                    }
                }
            }
        }

        this.blastDurations = Tools.stringToNumber(data.blastDurations, ',');
        this.blastShake = Tools.stringToNumber(data.blastShake, ',');
        if (data.hasOwnProperty('damageParam')) {
            const damageParams = data.damageParam.split(';');
            for (const param of damageParams) {
                if (param !== '') {
                    this.damageParam = Tools.stringToNumber(param, ',');
                }
            }
        }

        if (data.hasOwnProperty('extraParam0')) {
            const extraParams = data.extraParam0.split(';');
            for (const param of extraParams) {
                if (param !== '') {
                    this.extraParam = Tools.stringToNumber(param, ',');
                }
            }
        }
        if (data.hasOwnProperty('extraParam1')) this.extraParam1 = data.extraParam1;

        if (data.hasOwnProperty('skillResistUIDict')) {
            const skillResistData = data.skillResistUIDict.split('#');
            for (const entry of skillResistData) {
                if (entry !== '') {
                    const parts = entry.split(';');
                    if (parts.length > 1) {
                        const key = parseInt(parts[0]);
                        var values: number[] = [];
                        for (let i = 1; i < parts.length; i++) {
                            if (parts[i] !== '') {
                                values = Tools.stringToNumber(parts[i], ',');
                            }
                        }
                        this.skillResistUIDict.set(key, values);
                    }
                }
            }
        }
        this.lootParam0 = Tools.stringToNumber(data.lootParam0, ',');
        this.lootParam1 = Tools.stringToNumber(data.lootParam1, ',');
        this.sneakAnim = data.sneakAnim;
        this.showParam = Tools.stringToNumber(data.showParam, ',');
    }
}

export class EnemyShootData {
    attackInterval = 0; // 攻击间隔
    attackNum = 0; // 攻击次数
    attackArrNum = 0; // 攻击数组数量
    attackPointArr:number[] = []; // 攻击点数组

    /**
     * 从 JSON 数据加载敌人射击数据
     * @param {Object} data JSON 数据
     */
    loadJson(data: any) {
        if (data.hasOwnProperty('ain')) this.attackInterval = parseFloat(data.ain);
        if (data.hasOwnProperty('aa')) this.attackNum = parseInt(data.aa);
        if (data.hasOwnProperty('arrNum')) this.attackArrNum = parseInt(data.arrNum);
        if (data.hasOwnProperty('points')) {
            const pointsData = data.points.split(';');
            for (const point of pointsData) {
                if (point !== '') {
                    this.attackPointArr = Tools.stringToNumber(point, ',');
                }
            }
        }
    }
}
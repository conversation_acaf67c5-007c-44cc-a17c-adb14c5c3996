import { _decorator, Node } from 'cc';
import csproto from '../../../AutoGen/PB/cs_proto.js';
import { EventMgr } from '../../../event/EventManager';
import { MyApp } from '../../../MyApp';
import { ButtonPlus } from '../../common/components/button/ButtonPlus';
import List from '../../common/components/list/List';
import { BaseUI, UILayer, UIMgr } from '../../UIMgr';
import { BattleUI } from '../BattleUI';
import { MailCellUI } from './MailCellUI';
import { PopupUI } from '../PopupUI';

const { ccclass, property } = _decorator;

@ccclass('MailUI')
export class MailUI extends BaseUI {
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;
    @property(List)
    list: List | null = null;
    keys: number[] = [];
    items: csproto.cs.ICSItem[] = [];
    public static getUrl(): string { return "ui/main/mail/MailUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    protected onLoad(): void {
        this.btnClose!.addClick(this.closeUI, this);
        this.keys = Array.from(MyApp.lubanTables.TbItem.getDataMap().keys());
        this.list!.node.active = true;
        this.list!.numItems = Math.min(this.keys.length, 20);
    }
    async closeUI() {
        UIMgr.closeUI(MailUI);
        await UIMgr.openUI(BattleUI)
    }
    async onShow(): Promise<void> {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ITEM_LIST, this.onGetItemListMsg.bind(this), this)
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ITEM_LIST, { get_item_list: {} })
    }
    public onGetItemListMsg(msg: csproto.cs.IS2CMsg): void {
        this.items = msg.body!.get_item_list!.items || []
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this);
        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onGetItemListMsg, this)
    }
    start() {

    }
    onListRender(listItem: Node, row: number) {// 有数据要在 this.list.numItems 之前设置
        const key = row < this.keys.length ? this.keys[row] : this.keys[0];
        const cell = listItem.getComponent(MailCellUI);
        if (cell !== null) {
            cell.setData(key);
        }
    }
}



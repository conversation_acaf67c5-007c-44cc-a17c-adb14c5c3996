import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import {LevelDataWave} from 'db://assets/scripts/leveldata/leveldata';

import { LevelEditorElemUI } from './LevelEditorElemUI';
import { LevelEditorWaveParam } from './LevelEditorWaveParam';

@ccclass('LevelEditorWaveUI')
@executeInEditMode()
export class LevelEditorWaveUI extends LevelEditorElemUI {
    @property(Prefab)
    public wavePrefab: Prefab | null = null;
    @property(CCInteger)
    public planeID: number = 0;
    // map property name to value
    @property([LevelEditorWaveParam])
    public params: LevelEditorWaveParam[] = [];

    public initByLevelData(data: LevelDataWave) {
        super.initByLevelData(data);
        this.planeID = data.planeID;
        this.params = [];
        if (data.params) {
            data.params.forEach((v, k) => {
                var param = new LevelEditorWaveParam()
                param.name = k
                param.value = v
                this.params.push(param);
            });
        }
        if (data.waveUUID != "") {
            assetManager.loadAny({uuid:data.waveUUID}, (err, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load wave prefab err", err);
                    return
                }
                this.wavePrefab = prefab;
            })
        }
    }

    public fillLevelData(data: LevelDataWave) {
        super.fillLevelData(data)
        data.planeID = this.planeID;
        data.waveUUID = this.wavePrefab?.uuid ?? "";
        data.params = new Map<string, number>();
        this.params.forEach((param) => {
            data.params.set(param.name,param.value);
        })
    }
}
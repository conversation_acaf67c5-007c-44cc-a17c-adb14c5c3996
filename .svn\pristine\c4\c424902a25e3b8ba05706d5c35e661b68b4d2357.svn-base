import { director } from "cc";
import { SingletonBase } from "../../core/base/SingletonBase";
import GameEnum from "../const/GameEnum";
import { GameIns } from "../GameIns";

export class GameRuleManager extends SingletonBase<GameRuleManager> {

    _gameResult = false;
    _gameState = GameEnum.GameState.Idle;

    /**
     * 重置游戏规则
     */
    reset() {
        this._gameResult = false;
        this._gameState = GameEnum.GameState.Idle;
    }

    /**
     * 设置游戏为空闲状态
     */
    gameIdle() {
        this.setGameState(GameEnum.GameState.Idle);
    }

    /**
     * 设置游戏为出击状态
     */
    gameSortie() {
        if (!this.isInBattle()) {
            this.setGameState(GameEnum.GameState.Sortie);
        }
    }

    /**
     * 开始游戏
     */
    gameStart() {
        this.setGameState(GameEnum.GameState.Battle);
    }

    /**
     * 暂停游戏
     */
    gamePause() {
        this.setGameState(GameEnum.GameState.Pause);
    }

    /**
     * 恢复游戏
     */
    gameResume() {
        this.setGameState(GameEnum.GameState.Battle);
        director.resume();
    }

    /**
     * 设置游戏为即将结束状态
     */
    gameWillOver() {
        this.setGameState(GameEnum.GameState.WillOver);
    }

    /**
     * 设置游戏为结束状态
     */
    gameOver() {
        this.setGameState(GameEnum.GameState.Over);
    }

    /**
     * 更新游戏逻辑
     * @param {number} dt 每帧的时间间隔
     */
    updateGameLogic(dt:number) {
        if (this._gameResult || GameIns.mainPlaneManager.mainData.die) {
            return;
        }

        if (GameIns.battleManager.gameType === GameEnum.GameType.Gold) {
            if (GameIns.enemyManager.isEnemyOver()) {
                this._gameResult = true;
            }
        } else {
            const waveOver = GameIns.waveManager.isEnemyOver();
            const enemyOver = GameIns.enemyManager.isEnemyOver();

            if (waveOver && enemyOver && GameIns.bossManager.isBossOver()) {
                this._gameResult = true;
                GameIns.battleManager.battleSucc();
            }
        }
    }

    /**
     * 判断游戏是否处于战斗状态
     * @returns {boolean} 是否处于战斗状态
     */
    isInBattle() {
        return this._isGameState(GameEnum.GameState.Battle);
    }

    /**
     * 判断游戏是否即将结束
     * @returns {boolean} 是否即将结束
     */
    isGameWillOver() {
        return this._isGameState(GameEnum.GameState.WillOver);
    }

    /**
     * 判断游戏是否结束
     * @returns {boolean} 是否结束
     */
    isGameOver() {
        return this._isGameState(GameEnum.GameState.Over);
    }

    /**
     * 获取游戏结果
     * @returns {boolean} 游戏结果
     */
    getGameResult() {
        return this._gameResult;
    }

    /**
     * 设置游戏状态
     * @param {GameEnum.GameState} state 游戏状态
     */
    setGameState(state:number) {
        this._gameState = state;
    }

    /**
     * 获取游戏状态
     */
    get gameState() {
        return this._gameState;
    }

    /**
     * 判断当前游戏状态是否为指定状态
     * @param {GameEnum.GameState} state 游戏状态
     * @returns {boolean} 是否为指定状态
     */
    _isGameState(state:number) {
        return this._gameState === state;
    }
}
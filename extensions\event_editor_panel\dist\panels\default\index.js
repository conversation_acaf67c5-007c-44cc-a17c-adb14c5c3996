"use strict";
/* eslint-disable vue/one-component-per-file */
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = require("fs");
const path_1 = require("path");
// @ts-ignore
const vue_1 = require("vue");
const EventGroupDataManager_1 = require("../../utils/EventGroupDataManager");
const panelDataMap = new WeakMap();
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('Event Editor Panel shown'); },
        hide() { console.log('Event Editor Panel hidden'); },
    },
    messages: {
        'select-event-group'(eventGroupName) {
            // Call the selectEventGroup method defined in methods section
            const panel = this;
            if (panel.methods && panel.methods.selectEventGroup) {
                panel.methods.selectEventGroup.call(panel, eventGroupName);
            }
        },
    },
    template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    methods: {
        /**
         * Open and select a specific event group
         */
        selectEventGroup(eventGroupName) {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                // Send message to Vue app to select the event group
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.selectEventGroup) {
                    vueInstance.selectEventGroup(eventGroupName);
                }
            }
        },
        /**
         * Reload all event group data
         */
        reloadEventGroups() {
            const app = panelDataMap.get(this);
            if (app && app._instance) {
                const vueInstance = app._instance.ctx;
                if (vueInstance && vueInstance.reloadEventGroups) {
                    vueInstance.reloadEventGroups();
                }
            }
        }
    },
    ready() {
        if (this.$.app) {
            const app = (0, vue_1.createApp)({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            // Main Event Editor Component
            app.component('EventEditor', (0, vue_1.defineComponent)({
                setup() {
                    const manager = EventGroupDataManager_1.EventGroupDataManager.getInstance();
                    // Reactive state
                    const state = (0, vue_1.reactive)({
                        selectedCategory: EventGroupDataManager_1.EventGroupCategory.Emitter,
                        selectedEventGroup: null,
                        eventGroups: {
                            [EventGroupDataManager_1.EventGroupCategory.Emitter]: [],
                            [EventGroupDataManager_1.EventGroupCategory.Bullet]: [],
                            [EventGroupDataManager_1.EventGroupCategory.Others]: []
                        },
                        searchQuery: '',
                        isDirty: false,
                        undoStack: [],
                        redoStack: []
                    });
                    // Computed properties
                    const filteredEventGroups = (0, vue_1.computed)(() => {
                        const groups = state.eventGroups[state.selectedCategory];
                        if (!state.searchQuery)
                            return groups;
                        return groups.filter((group) => group.name.toLowerCase().includes(state.searchQuery.toLowerCase()));
                    });
                    const categories = (0, vue_1.computed)(() => Object.values(EventGroupDataManager_1.EventGroupCategory));
                    // Methods
                    const loadEventGroups = () => {
                        for (const category of Object.values(EventGroupDataManager_1.EventGroupCategory)) {
                            state.eventGroups[category] = manager.loadEventGroupsByCategory(category);
                        }
                    };
                    const selectEventGroup = (eventGroup) => {
                        if (state.isDirty) {
                            // TODO: Show confirmation dialog
                        }
                        // Save current state to undo stack
                        if (state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.redoStack = []; // Clear redo stack
                        }
                        state.selectedEventGroup = Object.assign({}, eventGroup);
                        state.isDirty = false;
                    };
                    const selectEventGroupByName = (name) => {
                        const found = manager.findEventGroup(name);
                        if (found) {
                            state.selectedCategory = found.category;
                            selectEventGroup(found.data);
                        }
                    };
                    const saveCurrentEventGroup = () => {
                        if (!state.selectedEventGroup)
                            return false;
                        const success = manager.saveEventGroup(state.selectedCategory, state.selectedEventGroup);
                        if (success) {
                            state.isDirty = false;
                            loadEventGroups(); // Reload to reflect changes
                        }
                        return success;
                    };
                    const createNewEventGroup = () => {
                        const newEventGroup = manager.createNewEventGroup(state.selectedCategory);
                        state.eventGroups[state.selectedCategory].push(newEventGroup);
                        selectEventGroup(newEventGroup);
                    };
                    const duplicateEventGroup = () => {
                        if (!state.selectedEventGroup)
                            return;
                        const duplicate = manager.duplicateEventGroup(state.selectedCategory, state.selectedEventGroup.name);
                        if (duplicate) {
                            state.eventGroups[state.selectedCategory].push(duplicate);
                            selectEventGroup(duplicate);
                        }
                    };
                    const deleteEventGroup = (eventGroup) => {
                        var _a;
                        if (confirm(`Are you sure you want to delete "${eventGroup.name}"?`)) {
                            manager.deleteEventGroup(state.selectedCategory, eventGroup.name);
                            loadEventGroups();
                            if (((_a = state.selectedEventGroup) === null || _a === void 0 ? void 0 : _a.name) === eventGroup.name) {
                                state.selectedEventGroup = null;
                                state.isDirty = false;
                            }
                        }
                    };
                    const markDirty = () => {
                        state.isDirty = true;
                    };
                    const undo = () => {
                        if (state.undoStack.length > 0 && state.selectedEventGroup) {
                            state.redoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.undoStack.pop();
                            state.isDirty = true;
                        }
                    };
                    const redo = () => {
                        if (state.redoStack.length > 0 && state.selectedEventGroup) {
                            state.undoStack.push(Object.assign({}, state.selectedEventGroup));
                            state.selectedEventGroup = state.redoStack.pop();
                            state.isDirty = true;
                        }
                    };
                    const reloadEventGroups = () => {
                        loadEventGroups();
                        state.selectedEventGroup = null;
                        state.isDirty = false;
                    };
                    const addCondition = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.push({
                            op: 0, // And
                            type: 0,
                            compareOp: 0, // Equal
                            targetValue: 0
                        });
                        markDirty();
                    };
                    const removeCondition = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.conditions.splice(index, 1);
                        markDirty();
                    };
                    const addAction = () => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.push({
                            type: 0,
                            duration: 0,
                            targetValue: 0,
                            easing: 0 // Linear
                        });
                        markDirty();
                    };
                    const removeAction = (index) => {
                        if (!state.selectedEventGroup)
                            return;
                        state.selectedEventGroup.actions.splice(index, 1);
                        markDirty();
                    };
                    // Lifecycle
                    (0, vue_1.onMounted)(() => {
                        loadEventGroups();
                    });
                    // Expose methods for external access
                    return {
                        state,
                        filteredEventGroups,
                        categories,
                        selectEventGroup,
                        selectEventGroupByName,
                        saveCurrentEventGroup,
                        createNewEventGroup,
                        duplicateEventGroup,
                        deleteEventGroup,
                        markDirty,
                        undo,
                        redo,
                        reloadEventGroups,
                        addCondition,
                        removeCondition,
                        addAction,
                        removeAction
                    };
                },
                template: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/vue/event-editor.html'), 'utf-8'),
            }));
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
//# sourceMappingURL=data:application/json;base64,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
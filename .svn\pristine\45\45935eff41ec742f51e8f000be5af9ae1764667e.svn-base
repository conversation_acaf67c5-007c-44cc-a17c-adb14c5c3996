import { _decorator, Component, Sprite, Vec2, Node, tween, v2, UITransform, Tween, v3, Prefab, instantiate, UIOpacity} from "cc";
import { GameIns } from "../../../GameIns";
import { Tools } from "../../../utils/Tools";
import EnemyAnim from "./EnemyAnim";
import { GameConst } from "../../../const/GameConst";
import { EnemyUIData } from "../../../data/EnemyData";

const { ccclass, property } = _decorator;

@ccclass
export default class EnemyPlaneRole extends Component {
    @property(Sprite)
    pedestal:Sprite | null = null;
    @property(Sprite)
    role:Sprite | null = null;
    @property(Sprite)
    white:Sprite | null = null;

    @property(Node)
    fireNode:Node | null = null;
    @property([Node])
    tailFireArr:Node[] = [];

    _data:EnemyUIData | null = null;

    _curUId = -1;
    _anim: EnemyAnim | null = null;
    _curAnim = "";

    /**
     * 预加载 UI
     * @param {Object} data 敌机数据
     */
    preLoadUI(data: EnemyUIData) {
        if (this._data = data) {
            this._initUI(true);
        }
    }

    /**
     * 初始化敌机
     * @param {Object} data 敌机数据
     * @param {Object} target 目标对象
     * @param {string} param 参数
     */
    async init(data: EnemyUIData, target: any, param: string = "") {
        this._reset();
        this._data = data;

        if (!this._data.isAm) {
            await this._initUI();
        } else {
            if (this._anim) {
                this.playAnim("idle1");
                this._anim!.node!.getComponent(UIOpacity)!.opacity = 255;
            }
        }
    }

    /**
     * 重置敌机状态
     */
    _reset() {
        this.white!.node!.getComponent(UIOpacity)!.opacity = 0;
        this.pedestal!.spriteFrame = null;
        this._curAnim = "";
        this.stopAnim();
        if (this._anim) this._anim!.node!.getComponent(UIOpacity)!.opacity = 0;
    }

    /**
     * 更新游戏逻辑
     * @param {number} dt 时间增量
     */
    updateGameLogic(dt: number) {

    }

    /**
     * 初始化 UI
     * @param {boolean} isPreload 是否预加载
     */
    async _initUI(isPreload = false) {
        if (this._data!.isAm) {
            this.role!.spriteFrame = null;
            this.white!.spriteFrame = null;
            // this._winkAct = tween().to(0, { opacity: 204 }).to(3 * GameConst.ActionFrameTime, { opacity: 0 });

            if (this._curUId === this._data!.id && this._anim) {
                this._anim!.node!.getComponent(UIOpacity)!.opacity = 255;
            } else {
                this._curUId = this._data!.id;
                if (this._anim) {
                    this._anim.node.destroy();
                    this._anim = null;
                }
                let pf = await GameIns.enemyManager.getPlaneRole(this._data!.image);
                const animNode = instantiate(pf);
                this.node.addChild(animNode);
                this._anim = animNode.getComponent(EnemyAnim);
                this._anim!.init(this._data!.extraParam);
            }
        } else {
            if (this._anim) this._anim.node.active = false;
            GameIns.enemyManager.setPlaneFrame(this.role!, this._data!.image);
            GameIns.enemyManager.setPlaneFrame(this.white!, this._data!.image);

            const frameTime = GameConst.ActionFrameTime; 
            const param = this._data!.extraParam;
            for (let i = 0; i < this._data!.extraParam.length; i++) {
                let tailFire:Node = this.tailFireArr[i];
                if (!tailFire) {
                    tailFire = new Node();
                    this.fireNode!.addChild(tailFire);
                    tailFire.addComponent(Sprite);
                    tailFire!.getComponent(UITransform)!.anchorY = 0;
                    this.tailFireArr.push(tailFire);
                }
                Tween.stopAllByTarget(tailFire);
                tailFire.active = true;
                GameIns.enemyManager.setPlaneFrame(tailFire!.getComponent(Sprite)!, "fire" + param[0]);
                tailFire.setPosition(param[1], param[2]);
                tailFire!.getComponent(UITransform)!.setContentSize(param[3], param[4]);
                tailFire.angle = param[7] || 0;


            
                tween(tailFire)
                    .to(frameTime * param[5], { scale: v3(param[6],param[6]) })
                    .to(frameTime * param[5], { scale: v3(1,1) })
                    .repeatForever()
                    .start();
            }

            for (let i = this._data!.extraParam.length; i < this.tailFireArr.length; i++) {
                const tailFire = this.tailFireArr[i];
                Tween.stopAllByTarget(tailFire);
                tailFire.active = false;
            }
        }
    }

    /**
     * 设置动画事件回调
     * @param {string} eventName 事件名称
     * @param {Function} callback 回调函数
     */
    setEventCallback(eventName: string, callback: Function) {
        this._anim && this._anim.setAnimEventCall(eventName, callback);
    }

    /**
     * 开始攻击
     */
    startAttack() {
        
    }

    /**
     * 攻击结束
     */
    attackOver() {
        
    }



    /**
     * 播放动画
     * @param {string} animName 动画名称
     * @param {Function} [callback] 动画结束回调
     * @returns {boolean} 是否成功播放
     */
    playAnim(animName: string, callback?: Function) {
        if (!this._data!.isAm) return false;
        if (this._curAnim === animName) {
            Tools.log("save anim", animName);
            return true;
        }
        if (this._anim) {
            this._anim.playAnim(animName, callback);
            return true;
        }
        Tools.log("anim is null", animName);
        return false;
    }

    /**
     * 暂停动画
     */
    pauseAnim() {
        this._anim && this._anim.pauseAnim();
    }

    /**
     * 恢复动画
     */
    resumeAnim() {
        this._anim && this._anim.resumeAnim();
    }

    /**
     * 停止动画
     */
    stopAnim() {
        this._anim && this._anim.stopAnim();
    }
}
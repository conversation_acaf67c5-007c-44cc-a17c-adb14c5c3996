{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts"], "names": ["GameRuleManager", "director", "SingletonBase", "GameEnum", "GameIns", "_gameResult", "_gameState", "GameState", "Idle", "reset", "gameIdle", "setGameState", "gameSortie", "isInBattle", "<PERSON><PERSON><PERSON>", "gameStart", "Battle", "gamePause", "Pause", "gameResume", "resume", "gameWillOver", "WillOver", "gameOver", "Over", "updateGameLogic", "dt", "mainPlaneManager", "mainData", "die", "battleManager", "gameType", "GameType", "Gold", "enemyManager", "isEnemyOver", "waveOver", "waveManager", "enemyOver", "boss<PERSON><PERSON><PERSON>", "isBossOver", "battleSucc", "_isGameState", "isGameWillOver", "isGameOver", "getGameResult", "state", "gameState"], "mappings": ";;;2HAKaA,e;;;;;;;;;;;;;;;;;;;;;;;AALJC,MAAAA,Q,OAAAA,Q;;AACAC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;;;;;;;;;iCAEIJ,e,GAAN,MAAMA,eAAN;AAAA;AAAA,0CAA6D;AAAA;AAAA;AAAA,eAEhEK,WAFgE,GAElD,KAFkD;AAAA,eAGhEC,UAHgE,GAGnD;AAAA;AAAA,oCAASC,SAAT,CAAmBC,IAHgC;AAAA;;AAKhE;AACJ;AACA;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKJ,WAAL,GAAmB,KAAnB;AACA,eAAKC,UAAL,GAAkB;AAAA;AAAA,oCAASC,SAAT,CAAmBC,IAArC;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,QAAQ,GAAG;AACP,eAAKC,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBC,IAArC;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,UAAU,GAAG;AACT,cAAI,CAAC,KAAKC,UAAL,EAAL,EAAwB;AACpB,iBAAKF,YAAL,CAAkB;AAAA;AAAA,sCAASJ,SAAT,CAAmBO,MAArC;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAG;AACR,eAAKJ,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBS,MAArC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAG;AACR,eAAKN,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBW,KAArC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAKR,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBS,MAArC;AACAf,UAAAA,QAAQ,CAACmB,MAAT;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAG;AACX,eAAKV,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBe,QAArC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,QAAQ,GAAG;AACP,eAAKZ,YAAL,CAAkB;AAAA;AAAA,oCAASJ,SAAT,CAAmBiB,IAArC;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,EAAD,EAAY;AACvB,cAAI,KAAKrB,WAAL,IAAoB;AAAA;AAAA,kCAAQsB,gBAAR,CAAyBC,QAAzB,CAAkCC,GAA1D,EAA+D;AAC3D;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,QAAtB,KAAmC;AAAA;AAAA,oCAASC,QAAT,CAAkBC,IAAzD,EAA+D;AAC3D,gBAAI;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,WAArB,EAAJ,EAAwC;AACpC,mBAAK9B,WAAL,GAAmB,IAAnB;AACH;AACJ,WAJD,MAIO;AACH,gBAAM+B,QAAQ,GAAG;AAAA;AAAA,oCAAQC,WAAR,CAAoBF,WAApB,EAAjB;AACA,gBAAMG,SAAS,GAAG;AAAA;AAAA,oCAAQJ,YAAR,CAAqBC,WAArB,EAAlB;;AAEA,gBAAIC,QAAQ,IAAIE,SAAZ,IAAyB;AAAA;AAAA,oCAAQC,WAAR,CAAoBC,UAApB,EAA7B,EAA+D;AAC3D,mBAAKnC,WAAL,GAAmB,IAAnB;AACA;AAAA;AAAA,sCAAQyB,aAAR,CAAsBW,UAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACI5B,QAAAA,UAAU,GAAG;AACT,iBAAO,KAAK6B,YAAL,CAAkB;AAAA;AAAA,oCAASnC,SAAT,CAAmBS,MAArC,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACI2B,QAAAA,cAAc,GAAG;AACb,iBAAO,KAAKD,YAAL,CAAkB;AAAA;AAAA,oCAASnC,SAAT,CAAmBe,QAArC,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIsB,QAAAA,UAAU,GAAG;AACT,iBAAO,KAAKF,YAAL,CAAkB;AAAA;AAAA,oCAASnC,SAAT,CAAmBiB,IAArC,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIqB,QAAAA,aAAa,GAAG;AACZ,iBAAO,KAAKxC,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIM,QAAAA,YAAY,CAACmC,KAAD,EAAe;AACvB,eAAKxC,UAAL,GAAkBwC,KAAlB;AACH;AAED;AACJ;AACA;;;AACiB,YAATC,SAAS,GAAG;AACZ,iBAAO,KAAKzC,UAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIoC,QAAAA,YAAY,CAACI,KAAD,EAAe;AACvB,iBAAO,KAAKxC,UAAL,KAAoBwC,KAA3B;AACH;;AA/I+D,O", "sourcesContent": ["import { director } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport { GameIns } from \"../GameIns\";\r\n\r\nexport class GameRuleManager extends SingletonBase<GameRuleManager> {\r\n\r\n    _gameResult = false;\r\n    _gameState = GameEnum.GameState.Idle;\r\n\r\n    /**\r\n     * 重置游戏规则\r\n     */\r\n    reset() {\r\n        this._gameResult = false;\r\n        this._gameState = GameEnum.GameState.Idle;\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为空闲状态\r\n     */\r\n    gameIdle() {\r\n        this.setGameState(GameEnum.GameState.Idle);\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为出击状态\r\n     */\r\n    gameSortie() {\r\n        if (!this.isInBattle()) {\r\n            this.setGameState(GameEnum.GameState.Sortie);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始游戏\r\n     */\r\n    gameStart() {\r\n        this.setGameState(GameEnum.GameState.Battle);\r\n    }\r\n\r\n    /**\r\n     * 暂停游戏\r\n     */\r\n    gamePause() {\r\n        this.setGameState(GameEnum.GameState.Pause);\r\n    }\r\n\r\n    /**\r\n     * 恢复游戏\r\n     */\r\n    gameResume() {\r\n        this.setGameState(GameEnum.GameState.Battle);\r\n        director.resume();\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为即将结束状态\r\n     */\r\n    gameWillOver() {\r\n        this.setGameState(GameEnum.GameState.WillOver);\r\n    }\r\n\r\n    /**\r\n     * 设置游戏为结束状态\r\n     */\r\n    gameOver() {\r\n        this.setGameState(GameEnum.GameState.Over);\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    updateGameLogic(dt:number) {\r\n        if (this._gameResult || GameIns.mainPlaneManager.mainData.die) {\r\n            return;\r\n        }\r\n\r\n        if (GameIns.battleManager.gameType === GameEnum.GameType.Gold) {\r\n            if (GameIns.enemyManager.isEnemyOver()) {\r\n                this._gameResult = true;\r\n            }\r\n        } else {\r\n            const waveOver = GameIns.waveManager.isEnemyOver();\r\n            const enemyOver = GameIns.enemyManager.isEnemyOver();\r\n\r\n            if (waveOver && enemyOver && GameIns.bossManager.isBossOver()) {\r\n                this._gameResult = true;\r\n                GameIns.battleManager.battleSucc();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 判断游戏是否处于战斗状态\r\n     * @returns {boolean} 是否处于战斗状态\r\n     */\r\n    isInBattle() {\r\n        return this._isGameState(GameEnum.GameState.Battle);\r\n    }\r\n\r\n    /**\r\n     * 判断游戏是否即将结束\r\n     * @returns {boolean} 是否即将结束\r\n     */\r\n    isGameWillOver() {\r\n        return this._isGameState(GameEnum.GameState.WillOver);\r\n    }\r\n\r\n    /**\r\n     * 判断游戏是否结束\r\n     * @returns {boolean} 是否结束\r\n     */\r\n    isGameOver() {\r\n        return this._isGameState(GameEnum.GameState.Over);\r\n    }\r\n\r\n    /**\r\n     * 获取游戏结果\r\n     * @returns {boolean} 游戏结果\r\n     */\r\n    getGameResult() {\r\n        return this._gameResult;\r\n    }\r\n\r\n    /**\r\n     * 设置游戏状态\r\n     * @param {GameEnum.GameState} state 游戏状态\r\n     */\r\n    setGameState(state:number) {\r\n        this._gameState = state;\r\n    }\r\n\r\n    /**\r\n     * 获取游戏状态\r\n     */\r\n    get gameState() {\r\n        return this._gameState;\r\n    }\r\n\r\n    /**\r\n     * 判断当前游戏状态是否为指定状态\r\n     * @param {GameEnum.GameState} state 游戏状态\r\n     * @returns {boolean} 是否为指定状态\r\n     */\r\n    _isGameState(state:number) {\r\n        return this._gameState === state;\r\n    }\r\n}"]}
import { Vec2 } from "cc";
import { Wave as tbWave } from "../../AutoGen/Luban/schema";
import { Tools } from "../utils/Tools";
import { Wave } from "../wave/Wave";

/**
 * 敌人波次类
 */
export class EnemyWave {
    enemyGroupID: number = 0;
    groupInterval: number = 0;
    type: number = 0;
    enemyID: number = 0;
    enemyInterval: number = 0;
    posDX: number = 0;
    posDY: number = 0;
    enemyNum: number = 0;
    bSetStartPos: boolean = false;
    startPosX: number = 0;
    startPosY: number = 0;
    trackGroups: TrackGroup[] = [];
    liveParam: number[] = [];
    exp: number = 0;
    // normalLoot: WaveLootData | null = null;
    // randomLoot: WaveLootData | null = null;
    rotateSpeed: number = 0;
    firstShootDelay: number[] = [];

    /**
     * 从 JSON 数据加载波次信息
     * @param data JSON 数据
     */
    loadJson(data: tbWave): void {
        this.enemyGroupID = data.enemyGroupID;
        this.groupInterval = data.delay;
        this.type = data.planeType;
        this.enemyID = data.planeId;
        this.enemyInterval = data.interval;
        this.enemyNum = data.num;
        this.rotateSpeed = data.rotatioSpeed;


        const point = Tools.stringToPoint(data.offsetPos, ",");
        this.posDX = point.x;
        this.posDY = point.y;

        if (data.hasOwnProperty("pos")) {
            const startPos = Tools.stringToNumber(data.pos, ",");
            if (startPos.length === 2) {
                this.startPosX = startPos[0];
                this.startPosY = startPos[1];
                this.bSetStartPos = true;
            } else {
                this.bSetStartPos = false;
            }
        }


        if (data.hasOwnProperty("track")) {
            const ways = data.track.split("#");
            for (const way of ways) {
                if (way !== "" && way.split(";").length > 1) {
                    const trackGroup = new TrackGroup();
                    trackGroup.loadJson(way);
                    this.trackGroups.push(trackGroup);
                }
            }
        }
        if (data.hasOwnProperty("trackParams")) {
            const types = Tools.stringToNumber(data.trackParams, ",");
            for (let i = 0; i < types.length; i++) {
                if (this.trackGroups.length > i) {
                    this.trackGroups[i].type = types[i];
                }
            }
        }
        if (data.hasOwnProperty("FirstShootDelay") && data.FirstShootDelay !== "") {
            this.firstShootDelay = Tools.stringToNumber(data.FirstShootDelay, ",");
        }
    }
    static fromLevelWave(wave: Wave, posX: number, posY: number) {
        const enemyWave = new EnemyWave();
        enemyWave.enemyGroupID = wave.enemyGroupID;
        enemyWave.groupInterval = wave.delay;
        enemyWave.type = wave.planeType;
        enemyWave.enemyID = wave.planeID;
        enemyWave.enemyInterval = wave.interval
        enemyWave.posDX = posX
        enemyWave.posDY = posY
        enemyWave.enemyNum = wave.num;
        enemyWave.bSetStartPos = true
        enemyWave.startPosX = wave.startPos.x
        enemyWave.startPosY = wave.startPos.y
        enemyWave.trackGroups = wave.trackGroups.map(group => {
            const trackGroup = new TrackGroup();
            trackGroup.loopNum = group.loopNum;
            trackGroup.formIndex = group.formIndex;
            trackGroup.trackIDs = group.tracks.map(track => track.id);
            trackGroup.speeds = group.tracks.map(track => track.speed);
            trackGroup.accelerates = group.tracks.map(track => track.accelerate);
            trackGroup.trackIntervals = group.tracks.map(track => track.Interval);
            trackGroup.type = group.type;
            return trackGroup;
        })
        enemyWave.firstShootDelay = wave.firstShootDelay;
        return enemyWave;
    }
}

/**
 * 轨迹组类
 */
export class TrackGroup {
    type: number = 0;
    loopNum: number = 0;
    formIndex: number = 0;
    trackIDs: number[] = [];
    speeds: number[] = [];
    accelerates: number[] = [];
    trackIntervals: number[] = [];

    /**
     * 从 JSON 数据加载轨迹组信息
     * @param data JSON 数据
     */
    loadJson(data: string): void {
        const parts = data.split(";");
        if (parts.length > 0) {
            const header = Tools.stringToNumber(parts[0], ",");
            this.loopNum = header[0];
            this.formIndex = header[1];
        }
        for (let i = 1; i < parts.length; i++) {
            const part = parts[i];
            if (part !== "") {
                const values = Tools.stringToNumber(part, ",");
                this.trackIDs.push(values[0]);
                this.speeds.push(values[1]);
                this.trackIntervals.push(values[2]);
            }
        }
    }
}

/**
 * 波次掉落数据类
 */
export class WaveLootData {
    enemys: number[] = [];
    lootId: number = 0;

    /**
     * 从 JSON 数据加载掉落信息
     * @param data JSON 数据
     */
    loadJson(data: string): void {
        const parts = data.split(";");
        if (parts.length > 1) {
            this.enemys = Tools.stringToNumber(parts[0], ",");
            this.lootId = parseInt(parts[1]);
        }
    }
}
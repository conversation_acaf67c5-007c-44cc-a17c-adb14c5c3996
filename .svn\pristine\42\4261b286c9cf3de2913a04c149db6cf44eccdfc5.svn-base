import { _decorator, Enum } from "cc";
import { eEasing } from "../../bullet/Easing";
import { eEventConditionType, eEmitterCondition } from "./EventConditionType";
import { eEventActionType, eEmitterActionType } from "./EventActionType";
const { ccclass, property } = _decorator;

export enum eConditionOp {
    And, Or
}

export enum eCompareOp {
    Equal, // 等于
    NotEqual, // 不等于
    Greater, // 大于
    Less, // 小于
    GreaterEqual, // 大于等于
    LessEqual, // 小于等于
}

export class EventConditionData {
    op: eConditionOp = eConditionOp.And;

    type: eEventConditionType = eEmitterCondition.Level_Duration;
    compareOp: eCompareOp = eCompareOp.Equal;
    targetValue: number = 0; // 条件值: 例如持续时间、距离

    static fromJSON(json: any): EventConditionData {
        const data = new EventConditionData();
        if (json) Object.assign(data, json);
        return data;
    }
}

export class EventActionData {
    type: eEventActionType = eEmitterActionType.Emitter_Active;

    @property({ displayName: '持续时间' })
    duration : number = 0; // 持续时间: 0表示立即执行

    @property({ displayName: '目标值' })
    targetValue: number = 0;

    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    easing : eEasing = eEasing.Linear;

    static fromJSON(json: any): EventActionData {
        const data = new EventActionData();
        if (json) Object.assign(data, json);
        return data;
    }
}

export class EventGroupData {
    public name: string = "";

    // 重复触发次数(默认1,只能触发一次; -1表示循环触发)
    public triggerCount: number = 1;
    public conditions: EventConditionData[] = [];
    public actions: EventActionData[] = [];

    public static fromJSON(json: any): EventGroupData {
        const data = new EventGroupData();
        if (json) {
            Object.assign(data, json);
            data.conditions = (json.conditions || []).map(EventConditionData.fromJSON);
            data.actions = (json.actions || []).map(EventActionData.fromJSON);
        }
        return data;
    }
}

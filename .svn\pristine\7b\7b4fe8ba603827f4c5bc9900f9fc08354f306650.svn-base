import { _decorator, director, Label, Node } from 'cc';
import { GameMode } from '../../AutoGen/Luban/schema';
import { MyApp } from '../../MyApp';
import { EventMgr } from '../../event/EventManager';
import { LoadingUI } from '../LoadingUI';
import { BaseUI, UILayer, UIMgr } from '../UIMgr';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
import { BattleUI } from './BattleUI';
import { BottomUI } from './BottomUI';
import { BuidingUI } from './BuidingUI';
import { BuildingInfoUI } from './BuildingInfoUI';
import { MainEvent } from './MainEvent';
import { PopupUI } from './PopupUI';
import { ShopUI } from './ShopUI';
import { TalentUI } from './TalentUI';
import { TopUI } from './TopUI';
import { PlaneUI } from './plane/PlaneUI';

const { ccclass, property } = _decorator;

@ccclass("MapModeUI")
export class MapModeUI extends BaseUI {
    @property(ButtonPlus)
    btnMap: ButtonPlus | null = null;
    @property(Label)
    lblBattle: Label | null = null;;
    @property(Node)
    buidings: Node | null = null;
    private index: any = 0;
    private imageUrls: string[] = ["item_7_1", "item_7_2", "item_7_3", "item_7_4", "item_7_5", "item_7_6", "item_7_8"];
    gameMode: GameMode | undefined = undefined;
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;

    public static getUrl(): string { return "ui/main/MapModeUI" };
    public static getLayer(): UILayer { return UILayer.Default }
    protected onLoad(): void {
        EventMgr.on(MainEvent.BattleItemClick, this.onBattleItemClick, this);

        this.gameMode = MyApp.lubanTables.TbGameMode.get(2001);
        //let list: GameMode[] = MyApp.lubanTables.TbGameMode.getDataList().filter(element => element.modeType == res.ModeType.STORY);
        let list: GameMode[] = MyApp.lubanTables.TbGameMode.getDataList();
        let row = 0;
        const bagItems = this.buidings!.children.forEach(element => {
            element!.getComponent(BuidingUI)!.setNewFrame(this.imageUrls[row]);
            element!.getComponent(BuidingUI)!.setTitle(list[row].ID, `第${(row + 1)}关`);
            row++;
        });
        this.btnClose!.addClick(this.closeUI, this);
    }
    async closeUI() {
        await UIMgr.openUI(BattleUI)
        UIMgr.closeUI(MapModeUI)
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }
    private onBattleItemClick(index: number, item: string) {
        this.index = index;
        this.lblBattle!.string = `${item}(${index})`;
        UIMgr.openUI(BuildingInfoUI);
    }
    async onShow(...args: any[]): Promise<void> {
        this.btnMap!.addClick(this.onMapClick, this);
    }

    async onHide(...args: any[]): Promise<void> { }

    async onClose(...args: any[]): Promise<void> { }

    onListRender(listItem: Node, row: number) {
        listItem.name = `listItem${row}`
        listItem.getComponentInChildren(Label)!.string = `第${(row + 1)}关`;
    }

    async onMapClick() {
        if (this.index == 0) {
            UIMgr.openUI(PopupUI, "未选择地图");
            return;
        }
        await UIMgr.openUI(LoadingUI)
        UIMgr.closeUI(MapModeUI)
        UIMgr.closeUI(BattleUI)
        UIMgr.closeUI(BottomUI)
        UIMgr.closeUI(PlaneUI)
        UIMgr.closeUI(TalentUI)
        UIMgr.closeUI(ShopUI)
        UIMgr.closeUI(TopUI)
        director.preloadScene("Game", async () => {
            director.loadScene("Game")
        })
    }
}

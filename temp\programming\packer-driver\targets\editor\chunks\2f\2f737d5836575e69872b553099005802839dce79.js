System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, LubanMgr, NetMgr, audioManager, CreateLoginSDK, ResManager, GlobalDataManager, _dec, _class, _class2, _crd, ccclass, MyApp;

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "./IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFnOnUpdate(extras) {
    _reporterNs.report("FnOnUpdate", "./IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFnOnLateUpdate(extras) {
    _reporterNs.report("FnOnLateUpdate", "./IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLubanMgr(extras) {
    _reporterNs.report("LubanMgr", "./Luban/LubanMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfNetMgr(extras) {
    _reporterNs.report("NetMgr", "./Network/NetMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfaudioManager(extras) {
    _reporterNs.report("audioManager", "./ResUpdate/audioManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIPlatformSDK(extras) {
    _reporterNs.report("IPlatformSDK", "./PlatformSDK/IPlatformSDK", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCreateLoginSDK(extras) {
    _reporterNs.report("CreateLoginSDK", "./PlatformSDK/IPlatformSDK", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResManager(extras) {
    _reporterNs.report("ResManager", "./core/base/ResManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGlobalDataManager(extras) {
    _reporterNs.report("GlobalDataManager", "./Game/manager/GlobalDataManager", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      LubanMgr = _unresolved_2.LubanMgr;
    }, function (_unresolved_3) {
      NetMgr = _unresolved_3.NetMgr;
    }, function (_unresolved_4) {
      audioManager = _unresolved_4.audioManager;
    }, function (_unresolved_5) {
      CreateLoginSDK = _unresolved_5.CreateLoginSDK;
    }, function (_unresolved_6) {
      ResManager = _unresolved_6.ResManager;
    }, function (_unresolved_7) {
      GlobalDataManager = _unresolved_7.GlobalDataManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "815bcg6mBVI2oWeCmHI13ZP", "MyApp", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Game']);

      ({
        ccclass
      } = _decorator);

      _export("MyApp", MyApp = (_dec = ccclass("MyApp"), _dec(_class = (_class2 = class MyApp extends Component {
        constructor(...args) {
          super(...args);
          this.ManagerPool = [];
          this._updateContainer = [];
          this._lateUpdateContainer = [];
          this._lubanMgr = null;
          this._netMgr = null;
          this._resMgr = null;
          this._platformSDK = null;
          this._globalDataManager = null;
        }

        static GetInstance() {
          return MyApp._instance;
        }

        onLoad() {
          MyApp._instance = this;
          this.ManagerPool.push((_crd && audioManager === void 0 ? (_reportPossibleCrUseOfaudioManager({
            error: Error()
          }), audioManager) : audioManager).instance);
          this._lubanMgr = new (_crd && LubanMgr === void 0 ? (_reportPossibleCrUseOfLubanMgr({
            error: Error()
          }), LubanMgr) : LubanMgr)();
          this.ManagerPool.push(this._lubanMgr);
          this._netMgr = new (_crd && NetMgr === void 0 ? (_reportPossibleCrUseOfNetMgr({
            error: Error()
          }), NetMgr) : NetMgr)();
          this.ManagerPool.push(this._netMgr);
          this._resMgr = new (_crd && ResManager === void 0 ? (_reportPossibleCrUseOfResManager({
            error: Error()
          }), ResManager) : ResManager)();
          this.ManagerPool.push(this._resMgr);
          this._platformSDK = (_crd && CreateLoginSDK === void 0 ? (_reportPossibleCrUseOfCreateLoginSDK({
            error: Error()
          }), CreateLoginSDK) : CreateLoginSDK)();
          this._globalDataManager = new (_crd && GlobalDataManager === void 0 ? (_reportPossibleCrUseOfGlobalDataManager({
            error: Error()
          }), GlobalDataManager) : GlobalDataManager)();
          this.ManagerPool.forEach(manager => {
            manager.init();

            this._updateContainer.push(manager.onUpdate.bind(manager));

            this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));
          });
        }

        update(deltaTime) {
          for (let i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
          }
        }

        lateUpdate() {
          for (let i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i]();
          }
        }

        static get netMgr() {
          return MyApp.GetInstance()._netMgr;
        }

        static get lubanMgr() {
          return MyApp.GetInstance()._lubanMgr;
        }

        static get lubanTables() {
          return MyApp.GetInstance()._lubanMgr.table;
        }

        static get platformSDK() {
          return MyApp.GetInstance()._platformSDK;
        }

        static get resMgr() {
          return MyApp.GetInstance()._resMgr;
        }

        static get globalDataManager() {
          return MyApp.GetInstance()._globalDataManager;
        }

      }, _class2._instance = null, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2f737d5836575e69872b553099005802839dce79.js.map
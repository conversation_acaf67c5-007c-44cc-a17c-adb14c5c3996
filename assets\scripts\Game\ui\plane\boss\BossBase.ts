import { _decorator, Component, Vec2, Node, v2, tween, v3, UITransform} from 'cc';
import BossHurt from './BossHurt';
import { Tools } from '../../../utils/Tools';
import BossCollider from './BossCollider';
import { GameIns } from '../../../GameIns';
import GameEnum from '../../../const/GameEnum';
import { GameConst } from '../../../const/GameConst';
const { ccclass, property } = _decorator;

@ccclass("BossBase")
export default class BossBase extends BossHurt {
    baseData: any = null;
    propertyRate: number[] = [];
    tip: string = "";
    exp: number = 0;
    uiNode: Node | null = null;
    unitArr: any[] = [];
    unitPosArr: Vec2[] = [];
    spineArr: any[] = [];
    actArr: any[] = [];
    collideArr: any[] = [];
    blastParam: any[] = [];
    blastShake: any[] = [];
    onlyLoot: any[] = [];
    lootArr: any[] = [];
    lootParam0: number[] = [167, 250, 0.6];
    lootParam1: number[] = [250, 350, 1.2];
    dieBullet: boolean = false;
    bullets: any[] = [];
    m_totalHp: number = 0;
    m_curHp: number = 0;
    m_swordUnit: any[] = [];

    /**
     * 设置 Boss 数据
     * @param data Boss 数据
     */
    setData(data: any) {
        this.baseData = data;
        this.exp = this.baseData.exp;
        this.onlyLoot = this.baseData.onlyLoot;
        this.blastParam = this.baseData.blastParam;
        this.blastShake = this.baseData.blastShake;
        this.lootArr = this.baseData.lootArr;
        this.attack = data.attack;
        this.collideAtk = data.collideAttack;

        if (this.baseData.lootParam0.length > 0) {
            this.lootParam0 = this.baseData.lootParam0;
        }
        if (this.baseData.lootParam1.length > 0) {
            this.lootParam1 = this.baseData.lootParam1;
        }
    }

    /**
     * 添加单元
     * @param unit 单元
     * @param position 单元位置
     */
    addUnit(unit: any, position?: Vec2) {
        this.unitArr.push(unit);
        if (position) {
            this.unitPosArr.push(position);
        }
    }

    /**
     * 移除单元
     * @param unit 单元
     */
    removeUnit(unit: any) {
        const index = this.unitArr.indexOf(unit);
        if (index >= 0) {
            this.unitArr.splice(index, 1);
            if (this.unitPosArr.length > index) {
                this.unitPosArr.splice(index, 1);
            }
        }
    }

    /**
     * 清空单元
     */
    clearUnit() {
        this.unitArr.splice(0);
        this.unitPosArr.splice(0);
    }

    /**
     * 添加骨骼动画
     * @param spine 骨骼动画
     */
    addSpine(spine: any) {
        this.spineArr.push(spine);
    }

    /**
     * 移除骨骼动画
     * @param spine 骨骼动画
     */
    removeSpine(spine: any) {
        Tools.arrRemove(this.spineArr, spine);
    }

    /**
     * 添加行为
     * @param action 行为
     */
    addAction(action: any) {
        this.actArr.push(action);
    }

    /**
     * 移除行为
     * @param action 行为
     */
    removeAction(action: any) {
        Tools.arrRemove(this.actArr, action);
    }

    /**
     * 创建自身碰撞体
     */
    createSelfCollides() {
        for (const collideData of this.baseData.collideArr) {
            const collider = this.addCollider(collideData);
            this.collideArr.push(collider);
        }
    }

    /**
     * 设置自身碰撞体是否可用
     * @param enable 是否可用
     */
    setSelfColliderAble(enable: boolean) {
        for (const collider of this.collideArr) {
            if (collider) {
                collider.setCollideAble(enable);
            }
        }
    }

    /**
     * 添加碰撞体
     * @param data 碰撞体数据
     */
    addCollider(data: any): BossCollider | null {
        const node = new Node();
        node.addComponent(UITransform);
        this.node.addChild(node);
        const collider = Tools.addScript(node, BossCollider);
        collider!.create(this, data);
        collider!.collideAtk = this.baseData.collideAttack;
        return collider;
    }

    /**
     * 设置位置
     * @param x X 坐标
     * @param y Y 坐标
     * @param update 是否更新
     */
    setPos(x: number, y: number, update: boolean = false) {
        this.node.setPosition(x, y);
    }

    /**
     * 设置属性倍率
     * @param rates 属性倍率
     */
    setPropertyRate(rates: number[]) {
        this.propertyRate = rates;
    }

    /**
     * 设置提示信息
     * @param tip 提示信息
     */
    setTip(tip: string) {
        this.tip = tip;
    }

    /**
     * 变形结束
     */
    transformEnd() {
        if (this.tip !== "") {
            GameIns.battleManager.bossChangeFinish(this.tip);
        } else {
            GameIns.battleManager.bossFightStart();
        }
    }

    /**
     * Boss 死亡逻辑
     */
    toDie() {
        if (!this.isDead) {
            this.isDead = true;
            this.setSelfColliderAble(false);
            // this.checkLoot();
            this.onDie();
        }
    }

    /**
     * 开始战斗
     */
    startBattle() { }

    /**
     * Boss 死亡回调
     */
    onDie() {
        // TaskManager.taskNumberChange(TaskType.KillBoss, 1);
        // TaskManager.taskNumberChange(TaskType.KillPlane, 1);
        // TaskManager.achievementNumberChange(AchievementType.KillBoss, 1);
        // TaskManager.achievementNumberChange(AchievementType.KillEnemy, 1);

        // this.playDieAnim();
        // GameData.killEnemyNumber += 1;

        for (const plane of GameIns.enemyManager.planes) {
            plane.die(GameEnum.EnemyDestroyType.Die);
        }

        // if (this.exp > 0) {
        //     LootManager.addExp(this.exp);
        // }

        // MainPlaneManager.checkKillHp();
        // MainPlaneManager.checkKillAtk();
        this.removeBullets();
    }

    /**
     * 移除子弹
     */
    removeBullets() {
        for (const bullet of this.bullets) {
            bullet.dieRemove();
        }
        this.bullets.splice(0);
    }

    /**
     * 播放死亡动画
     */
    playDieAnim() {
        // for (let i = 0; i < this.blastParam.length; i++) {
        //     const blast = this.blastParam[i];
        //     const delay = blast[3] * GameConst.ActionFrameTime;

        //     this.scheduleOnce(() => {
        //         EnemyEffectLayer.addBlastEffect(this, blast[2], {
        //             x: blast[0],
        //             y: blast[1],
        //             scale: blast[4],
        //             angle: blast[5],
        //         });

        //         if (blast[6] > 0) {
        //             GameIns.audioManager.playEffect(`blast${blast[6]}`);
        //         }
        //     }, delay);
        // }

        // for (const shake of this.blastShake) {
        //     const delay = shake.x * GameConst.ActionFrameTime;
        //     this.scheduleOnce(() => {
        //         MainCamera.shake1(EnemyManager.shakeParam[shake.y]);
        //     }, delay);
        // }
    }

    /**
     * 播放震动动画
     */
    playShakeAnim() {
        const frameTime = GameConst.ActionFrameTime;
        if (this.uiNode) {
            tween(this.uiNode)
                .to(2 * frameTime, { position: v3(-3, -2) })
                .to(2 * frameTime, { position: v3(11, -14), angle: 1 })
                .to(2 * frameTime, { position: v3(7, 4) })
                .to(2 * frameTime, { position: v3(20, -9), angle: 0 })
                .to(2 * frameTime, { position: v3(29, 7) })
                .to(frameTime, { position: v3(13, -5) })
                .to(frameTime, { position: v3(17, 2) })
                .to(frameTime, { position: v3(4, -6) })
                .to(frameTime, { position: v3(14, 4) })
                .to(frameTime, { position: v3(-1, -4) })
                .to(frameTime, { position: v3(5, 6) })
                .to(frameTime, { position: v3(-3, -5) })
                .to(frameTime, { position: v3(1, 3) })
                .to(frameTime, { position: v3(-7, -6) })
                .to(frameTime, { position: v3(0, 2) })
                .to(frameTime, { position: v3(-3, -4) })
                .delay(frameTime)
                .to(frameTime, { position: v3(0, 0) })
                .start();
        }
    }

    /**
     * 播放白屏死亡动画
     */
    playDieWhiteAnim() {
        // this.scheduleOnce(() => {
        //     EffectLayer.showWhiteScreen(4 * GameConst.ActionFrameTime, 255);
        //     if (this.uiNode) {
        //         this.uiNode.opacity = 0;
        //     }
        // }, 41 * GameConst.ActionFrameTime);
    }

    /**
     * 死亡动画结束回调
     */
    onDieAnimEnd() { }

    /**
     * 改变血量
     * @param delta 血量变化值
     */
    hpChange(delta: number) {
        this.m_curHp += delta;
        if (this.m_curHp < 0) {
            this.m_curHp = 0;
        }
        // BossBattleManager.hpChange(delta, this.node);
    }

    /**
     * 检查掉落物品
     */
    checkLoot() {
        // if (BattleManager.isGameType(GameType.Boss)) {
        //     BossBattleManager.lootForDie(this.node.convertToWorldSpaceAR(Vec2.ZERO));
        // } else {
        //     let allBossesDead = true;
        //     for (const boss of BossManager.bosses) {
        //         if (!boss.isDead) {
        //             allBossesDead = false;
        //             break;
        //         }
        //     }

        //     if (this.isDead && allBossesDead) {
        //         const loot = LootManager.checkLoot(EnemyScale.Large, true);
        //         LootManager.addBossProp(this.node.position, this.lootParam1, loot);
        //     }
        // }
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        for (const collider of this.collideArr) {
            collider.updateGameLogic(deltaTime);
        }
    }

    /**
     * 获取所有单元
     */
    getUnits(): any[] {
        return [];
    }

    /**
     * 获取所有碰撞体
     */
    getCollides(): any[] {
        return this.collideArr;
    }

    /**
     * 获取血量百分比
     */
    getHpPercent(): number {
        return this.m_curHp / this.m_totalHp;
    }

    /**
     * 添加子弹
     * @param bullet 子弹对象
     */
    addBullet(bullet: any) {
        if (this.bullets) {
            this.bullets.push(bullet);
        }
    }

    /**
     * 移除子弹
     * @param bullet 子弹对象
     */
    removeBullet(bullet: any) {
        if (this.bullets) {
            const index = this.bullets.indexOf(bullet);
            if (index >= 0) {
                this.bullets.splice(index, 1);
            }
        }
    }

    /**
     * 暂停
     */
    pause() { }

    /**
     * 恢复
     */
    resume() { }
}
import { _decorator, director, EditBox, Label, Node, sys } from 'cc';

import { ButtonPlus } from '../common/components/button/ButtonPlus';
import { DropDown } from '../common/components/dropdown/DropDown';
import { LoadingUI } from '../LoadingUI';
import { BaseUI, UILayer, UIMgr } from '../UIMgr';
import { BottomUI } from './BottomUI';
import { DialogueUI } from './dialogue/DialogueUI';
import { RogueUI } from './fight/RogueUI';
import { FriendUI } from './friend/FriendUI';
import { MapModeUI } from './MapModeUI';
import { PKUI } from './pk/PKUI';
import { PlaneUI } from './plane/PlaneUI';
import { ShopUI } from './ShopUI';
import { TalentUI } from './TalentUI';
import { TopUI } from './TopUI';
import { MailUI } from './mail/MailUI';
import { MyApp } from '../../MyApp';

const { ccclass, property } = _decorator;

interface Item {
    id: number;
    name: string;
}

const items: Item[] = [
    { id: 0, name: "默认" },
    { id: 6, name: "邮件" },
    { id: 5, name: "P<PERSON>模式" },
    { id: 4, name: "好友" },
    { id: 3, name: "对话框" },
    { id: 2, name: "肉鸽" },
    { id: 1, name: "剧情章节" },

];

@ccclass('BattleUI')
export class BattleUI extends BaseUI {
    public static getUrl(): string { return "ui/main/BattleUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    @property(ButtonPlus)
    btnBattle: ButtonPlus | null = null;
    @property(DropDown)
    tabDropDown: DropDown | null = null;

    @property(EditBox)
    chapterID1: EditBox | null = null;
    @property(EditBox)
    chapterID2: EditBox | null = null;
    @property(ButtonPlus)
    btnBattle1: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnBattle2: ButtonPlus | null = null;

    protected onLoad(): void {
        this.btnBattle!.addClick(this.onBattleClick, this);
        this.btnBattle1!.addClick(this.onBattle1Click, this);
        this.btnBattle2!.addClick(this.onBattle2Click, this);
    }

    getItemIds(): number[] {
        return items.map(item => item.id);
    }

    public onShow(...args: any[]): Promise<void> {
        this.tabDropDown!.node.active = sys.isBrowser
        this.scheduleOnce(() => {
            this.tabDropDown!.init(this.getItemIds(), this.onDropDownOptionRender.bind(this), this.onDropDownOptionClick.bind(this))
        }, 0.1);
        return Promise.resolve();
    };

    private onDropDownOptionRender(nd: Node, optKey: number | string) {
        const item = items.find(item => item.id === optKey);
        nd.getComponentInChildren(Label)!.string = item?.name || "";
    }

    private async onDropDownOptionClick(optKey: number | string) {
        const item = items.find(item => item.id === optKey);
        switch (item!.name) {
            case "好友":
                await UIMgr.openUI(FriendUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "对话框":
                await UIMgr.openUI(DialogueUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "肉鸽":
                await UIMgr.openUI(RogueUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "剧情章节":
                await UIMgr.openUI(MapModeUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "PK模式":
                await UIMgr.openUI(PKUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "邮件":
                await UIMgr.openUI(MailUI)
                UIMgr.closeUI(BattleUI)
                break;
            default:
                break;
        }
    }

    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
    }
    protected onDestroy(): void {
        this.unscheduleAllCallbacks();
    }
    protected update(dt: number): void {
    }

    async onBattleClick() {
        MyApp.globalDataManager.chapterID = 0;
        this.onBattle();
    }
    async onBattle1Click() {
        MyApp.globalDataManager.chapterID = parseInt(this.chapterID1!.string);
        this.onBattle();
    }
    async onBattle2Click() {
        MyApp.globalDataManager.chapterID = parseInt(this.chapterID2!.string);
        this.onBattle();
    }
    async onBattle() {
        await UIMgr.openUI(LoadingUI)
        UIMgr.closeUI(BattleUI)
        UIMgr.closeUI(BottomUI)
        UIMgr.closeUI(PlaneUI)
        UIMgr.closeUI(TalentUI)
        UIMgr.closeUI(ShopUI)
        UIMgr.closeUI(TopUI)

        director.preloadScene("Game", async () => {
            director.loadScene("Game")
        })
    }
}


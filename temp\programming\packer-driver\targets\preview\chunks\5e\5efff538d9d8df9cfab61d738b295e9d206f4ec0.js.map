{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts"], "names": ["_decorator", "Component", "ccclass", "Entity", "new_uuid", "m_comps", "Map", "getComp", "type", "get", "getComps", "addComp", "comp", "set", "removeComp", "remove", "delete", "removeAllComp", "Array", "from", "values", "for<PERSON>ach", "clear", "removeOther<PERSON>omps", "keepComp", "onCollide", "collision"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OAIf;AAAEC,QAAAA;AAAF,O,GAAcF,U;;yBAGCG,M,WADpBD,OAAO,CAAC,QAAD,C,gBAAR,MACqBC,MADrB,SACoCF,SADpC,CAC8C;AAAA;AAAA;AAAA,eAE1CG,QAF0C,GAE/B,CAF+B;AAAA,eAG1CC,OAH0C,GAGhC,IAAIC,GAAJ,EAHgC;AAAA;;AAGrB;;AAGrB;AACJ;AACA;AACA;AACA;AACIC,QAAAA,OAAO,CAACC,IAAD,EAAc;AACjB,iBAAO,KAAKH,OAAL,CAAaI,GAAb,CAAiBD,IAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,QAAQ,CAACF,IAAD,EAAe;AACnB,iBAAO,KAAKH,OAAL,CAAaI,GAAb,CAAiBD,IAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,OAAO,CAACH,IAAD,EAAeI,IAAf,EAA+B;AAClC,eAAKP,OAAL,CAAaQ,GAAb,CAAiBL,IAAjB,EAAuBI,IAAvB;AACA,iBAAOA,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,UAAU,CAACN,IAAD,EAAe;AACrB,cAAMI,IAAI,GAAG,KAAKL,OAAL,CAAaC,IAAb,CAAb;;AACA,cAAII,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACG,MAAL;AACH;;AACD,eAAKV,OAAL,CAAaW,MAAb,CAAoBR,IAApB;AACH;AAED;AACJ;AACA;;;AACIS,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKZ,OAAL,IAAgB,IAApB,EAA0B;AACtBa,YAAAA,KAAK,CAACC,IAAN,CAAW,KAAKd,OAAL,CAAae,MAAb,EAAX,EAAkCC,OAAlC,CAA2CT,IAAD,IAAU;AAChDA,cAAAA,IAAI,CAACG,MAAL;AACH,aAFD;AAGA,iBAAKV,OAAL,CAAaiB,KAAb;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACC,QAAD,EAAqB;AACjC,cAAI,KAAKnB,OAAL,IAAgB,IAApB,EAA0B;AACtB,iBAAKA,OAAL,CAAagB,OAAb,CAAqB,CAACT,IAAD,EAAOJ,IAAP,KAAgB;AACjC,kBAAII,IAAI,KAAKY,QAAb,EAAuB;AACnB,qBAAKnB,OAAL,CAAaW,MAAb,CAAoBR,IAApB;AACH;AACJ,aAJD;AAKH;AACJ;;AAEDiB,QAAAA,SAAS,CAACC,SAAD,EAAuB,CAE/B;;AA3EyC,O", "sourcesContent": ["import { _decorator, Component } from 'cc';\r\nimport BaseComp from './BaseComp';\r\nimport FCollider from '../../collider-system/FCollider';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass('Entity')\r\nexport default class Entity extends Component {\r\n\r\n    new_uuid = 0;\r\n    m_comps = new Map(); // 存储组件的 Map\r\n\r\n\r\n    /**\r\n     * 获取指定类型的组件\r\n     * @param {string} type 组件类型\r\n     * @returns {any} 组件实例\r\n     */\r\n    getComp(type:string) {\r\n        return this.m_comps.get(type);\r\n    }\r\n\r\n    /**\r\n     * 获取指定类型的所有组件\r\n     * @param {string} type 组件类型\r\n     * @returns {any} 组件实例\r\n     */\r\n    getComps(type: string) {\r\n        return this.m_comps.get(type);\r\n    }\r\n\r\n    /**\r\n     * 添加组件\r\n     * @param {string} type 组件类型\r\n     * @param {any} comp 组件实例\r\n     * @returns {any} 添加的组件实例\r\n     */\r\n    addComp(type: string, comp: BaseComp) {\r\n        this.m_comps.set(type, comp);\r\n        return comp;\r\n    }\r\n\r\n    /**\r\n     * 移除指定类型的组件\r\n     * @param {string} type 组件类型\r\n     */\r\n    removeComp(type: string) {\r\n        const comp = this.getComp(type);\r\n        if (comp) {\r\n            comp.remove();\r\n        }\r\n        this.m_comps.delete(type);\r\n    }\r\n\r\n    /**\r\n     * 移除所有组件\r\n     */\r\n    removeAllComp() {\r\n        if (this.m_comps != null) {\r\n            Array.from(this.m_comps.values()).forEach((comp) => {\r\n                comp.remove();\r\n            });\r\n            this.m_comps.clear();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除除指定组件外的其他组件\r\n     * @param {BaseComp} keepComp 要保留的组件\r\n     */\r\n    removeOtherComps(keepComp: BaseComp) {\r\n        if (this.m_comps != null) {\r\n            this.m_comps.forEach((comp, type) => {\r\n                if (comp !== keepComp) {\r\n                    this.m_comps.delete(type);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    onCollide(collision: FCollider) {\r\n        \r\n    }\r\n}"]}
{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts"], "names": ["GameResourceList", "FrameAnim", "Bullet", "EnemyPlane", "HurtEffect", "HurtNum", "Hurt0", "font_hurtNum", "atlas_mainPlane", "atlas_enemyBullet", "atlas_mainBullet", "atlas_hurtEffects", "atlas_enemyBullet1", "atlas_package_enemy1", "atlas_package_turret1", "atlas_boss_unit", "texture_map_mask", "spine_boss_smoke", "spine_mainfire", "GameMap_1", "key"], "mappings": ";;;;;;;;;;;;;;AAAIA,MAAAA,gB,GAAmB;AAEnBC,QAAAA,SAAS,EAAE,mBAFQ;AAGnBC,QAAAA,MAAM,EAAE,gBAHW;AAInBC,QAAAA,UAAU,EAAE,oBAJO;AAKnBC,QAAAA,UAAU,EAAE,oBALO;AAMnBC,QAAAA,OAAO,EAAE,iBANU;AAOnBC,QAAAA,KAAK,EAAE,sBAPY;AASnBC,QAAAA,YAAY,EAAE,cATK;AAWnBC,QAAAA,eAAe,EAAE,4CAXE;AAanBC,QAAAA,iBAAiB,EAAE,2BAbA;AAcnBC,QAAAA,gBAAgB,EAAE,8BAdC;AAenBC,QAAAA,iBAAiB,EAAC,gCAfC;AAgBnBC,QAAAA,kBAAkB,EAAE,8BAhBD;AAiBnBC,QAAAA,oBAAoB,EAAE,gCAjBH;AAkBnBC,QAAAA,qBAAqB,EAAE,iCAlBJ;AAmBnBC,QAAAA,eAAe,EAAC,wBAnBG;AAqBnBC,QAAAA,gBAAgB,EAAC,gCArBE;AAuBnBC,QAAAA,gBAAgB,EAAC,uBAvBE;AAwBnBC,QAAAA,cAAc,EAAC,yCAxBI;AA0BnBC,QAAAA,SAAS,EAAC;AA1BS,O,EA6BvB;;AACA,OAAC,MAAM;AACC,aAAK,MAAMC,GAAX,IAAkBpB,gBAAlB,EAAoC;AACpCA,UAAAA,gBAAgB,CAACoB,GAAD,CAAhB,GAA0D,QAAOpB,gBAAgB,CAACoB,GAAD,CAAuC,EAAxH;AACH;AACJ,OAJD;;yBAMepB,gB", "sourcesContent": ["let GameResourceList = {\r\n\r\n    FrameAnim: \"prefabs/FrameAnim\",\r\n    Bullet: \"prefabs/Bullet\",\r\n    EnemyPlane: \"prefabs/EnemyPlane\",\r\n    HurtEffect: \"prefabs/HurtEffect\",\r\n    HurtNum: \"prefabs/HurtNum\",\r\n    Hurt0: \"prefabs/effect/Hurt0\",\r\n\r\n    font_hurtNum: \"font/hurtNum\",\r\n\r\n    atlas_mainPlane: \"texture/mainPlane/package_mainPlane_trans_\",\r\n\r\n    atlas_enemyBullet: \"texture/enemy/enemyBullet\",\r\n    atlas_mainBullet: \"texture/mainPlane/mainBullet\",\r\n    atlas_hurtEffects:\"texture/hurtEffect/hurtEffects\",\r\n    atlas_enemyBullet1: \"texture/enemy/1/enemyBullet1\",\r\n    atlas_package_enemy1: \"texture/enemy/1/package_enemy1\",\r\n    atlas_package_turret1: \"texture/enemy/1/package_turret1\",\r\n    atlas_boss_unit:\"texture/boss/boss_unit\",\r\n\r\n    texture_map_mask:\"texture/mask/mask1/spriteFrame\",\r\n\r\n    spine_boss_smoke:\"spine/skel_boss_smoke\",\r\n    spine_mainfire:\"spine/mainPlane/firePoint/skel_mainfire\",\r\n\r\n    GameMap_1:\"normal/chapter_1/GameMap_1\"\r\n};\r\n\r\n// Add \"Game/\" prefix to all values\r\n(() => {\r\n        for (const key in GameResourceList) {\r\n        GameResourceList[key as keyof typeof GameResourceList] = `Game/${GameResourceList[key as keyof typeof GameResourceList]}`;\r\n    }\r\n})();\r\n\r\nexport default GameResourceList;"]}
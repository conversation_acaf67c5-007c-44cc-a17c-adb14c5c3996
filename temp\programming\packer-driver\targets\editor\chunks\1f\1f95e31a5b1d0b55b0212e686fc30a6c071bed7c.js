System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, _crd;

  function _reportPossibleCrUseOfEventConditionData(extras) {
    _reporterNs.report("EventConditionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b4989OWpMhNsKm4kTnR6YHD", "IEventCondition", undefined); // Base interfaces


      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1f95e31a5b1d0b55b0212e686fc30a6c071bed7c.js.map
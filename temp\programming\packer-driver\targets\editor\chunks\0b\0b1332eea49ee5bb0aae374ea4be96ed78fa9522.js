System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, res, ResGM, MyApp, GM, _crd;

  function _reportPossibleCrUseOfres(extras) {
    _reporterNs.report("res", "../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResGM(extras) {
    _reporterNs.report("ResGM", "../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "../DataManager", _context.meta, extras);
  }

  _export("GM", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      res = _unresolved_2.res;
      ResGM = _unresolved_2.ResGM;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "871bczforJDpKqcW3eOuxEm", "GM", undefined);

      _export("GM", GM = class GM {
        constructor() {
          /**key:gm界面页签名 value:gm结构信息 */
          this._gmMap = new Map();
        }

        init() {
          const tbGM = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbGM;
          tbGM.getDataList().forEach(e => {
            const gmInfo = this._gmMap.get(e.tabID);

            if (gmInfo) {
              gmInfo.push({
                cfg: e
              });
            } else {
              this._gmMap.set(e.tabID, [{
                cfg: e
              }]);
            }
          });
          this.localClientGM();
        }

        get tabIDList() {
          return Array.from(this._gmMap.keys());
        }

        getCmdBtnListByTabID(tabID) {
          var _this$_gmMap$get;

          return ((_this$_gmMap$get = this._gmMap.get(tabID)) == null ? void 0 : _this$_gmMap$get.filter(v => v.cfg !== undefined)) || [];
        }
        /**
         * 客户端注册gm按钮点击事件(GM.xlxs表里配置了才会有button显示)
         * @param tabID gm界面页签名
         * @param cmd gm命令
         * @param onSendClick 点击事件(返回值会显示在console)
         */


        registerClientGMHandler(tabID, cmd, onSendClick) {
          let gmList = this._gmMap.get(tabID);

          if (!gmList) {
            gmList = [{
              cfg: new (_crd && ResGM === void 0 ? (_reportPossibleCrUseOfResGM({
                error: Error()
              }), ResGM) : ResGM)({
                tabID: tabID,
                cmd: cmd
              }),
              onSendClick: onSendClick
            }];

            this._gmMap.set(tabID, gmList);
          } else {
            const info = gmList.find(info => {
              var _info$cfg;

              return ((_info$cfg = info.cfg) == null ? void 0 : _info$cfg.cmd) === cmd;
            });

            if (info) {
              info.onSendClick = onSendClick;
            } else {
              gmList.push({
                cfg: new (_crd && ResGM === void 0 ? (_reportPossibleCrUseOfResGM({
                  error: Error()
                }), ResGM) : ResGM)({
                  tabID: tabID,
                  cmd: cmd,
                  desc: "",
                  name: "",
                  tabName: ""
                }),
                onSendClick: onSendClick
              });
            }
          }
        }
        /**
         * 本地客户端gm指令例子(这个是例子，可以在其他代码模块调用,不要在这里调后面会做bundle，不然可能会有引用问题)
         */


        localClientGM() {
          //配置表配置过数据，显示button,如何配置了没有和表里命令对上，就会发给服务器
          this.registerClientGMHandler((_crd && res === void 0 ? (_reportPossibleCrUseOfres({
            error: Error()
          }), res) : res).GMTabID.BATTLE, "//local_client_test", args => {
            return "local_client_test---完成";
          }); //没有配置表数据，不显示button

          this.registerClientGMHandler((_crd && res === void 0 ? (_reportPossibleCrUseOfres({
            error: Error()
          }), res) : res).GMTabID.BATTLE, "//local_client_test1", args => {
            return "local_client_test1---完成";
          });
        }

        update() {}

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0b1332eea49ee5bb0aae374ea4be96ed78fa9522.js.map
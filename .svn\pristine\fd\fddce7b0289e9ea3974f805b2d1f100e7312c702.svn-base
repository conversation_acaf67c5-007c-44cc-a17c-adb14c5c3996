
export enum eEasing {
    Linear,
    InSine, OutSine, InOutSine,
    InQuad, OutQuad, InOutQuad
}

export class Easing {
    static lerp(easing: eEasing, start: number, end: number, t: number): number {
        switch (easing) {
            case eEasing.Linear:
                return start + (end - start) * t;
            case eEasing.InSine:
                return start + (end - start) * (1 - Math.cos((t * Math.PI) / 2));
            case eEasing.OutSine:
                return start + (end - start) * Math.sin((t * Math.PI) / 2);
            case eEasing.InOutSine:
                return start + (end - start) * (t < 0.5 ? (1 - Math.cos(t * Math.PI)) / 2 : (1 + Math.sin(t * Math.PI)) / 2);
            case eEasing.InQuad:
                return start + (end - start) * t * t;
            case eEasing.OutQuad:
                return start + (end - start) * (1 - (1 - t) * (1 - t));
            case eEasing.InOutQuad:
                return start + (end - start) * (t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2);
            default:
                return start + (end - start) * t;
        }
    }
}
import { _decorator, Component, Vec2 } from 'cc';
import { LevelBaseUI } from './LevelBaseUI';
import { LevelDataElem } from '../../../leveldata/leveldata';
const { ccclass } = _decorator;

@ccclass('LevelElemUI')
export class LevelElemUI extends Component {
    public get time(): number {
        const layerNode = this.node.parent?.parent
        if (!layerNode) {
            return 0;
        }
        const rootNode = layerNode.parent?.parent
        if (!rootNode) {
            return 0;
        }
        const baseUI = rootNode.getComponent(LevelBaseUI)
        if (!baseUI) {
            return 0;
        }
        const layer = baseUI.floorLayers.find((layer) => layer.node == layerNode) || baseUI.skyLayers.find((layer) => layer.node == layerNode)
        if (!layer) {
            return 0;
        }
        return this.node.position.y / layer.speed;
    }
    public elemID = "";
    protected onLoad(): void {
        if (this.elemID == "") {
            this.elemID = this.uuid;
        }
    }
    public initByLevelData(data: LevelDataElem) {
        this.node.setPosition(data.position.x, data.position.y);
        this.elemID = data.elemID;
    }
}
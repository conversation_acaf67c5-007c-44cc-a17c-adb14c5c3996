import { _decorator, misc, Component, Node, Sprite, Color } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletData } from '../data/bullet/BulletData';
import { ObjectPool } from './ObjectPool';
import { Movable } from '../move/Movable';
import { BulletSystem } from './BulletSystem';
import { EventGroup } from './EventGroup';
import { Property, PropertyContainerComponent } from './PropertyContainer';
const { ccclass, property, executeInEditMode } = _decorator;

// 用枚举定义属性
export enum eBulletProp {
    IsDestructive,
    IsDestructiveOnHit,
    Duration,
    Damage,
    DelayDestroy,

    // 移动属性
    IsFacingMoveDir,
    IsTrackingTarget,
    Speed,
    SpeedAngle,
    Acceleration,
    AccelerationAngle,
}


// 子弹 Bullet 伤害计算 
// Weapon -> 发射器, 喷火, 技能武器, 激光
// WeaponSlot -> SetWeapon
@ccclass('Bullet')
@executeInEditMode
export class Bullet extends PropertyContainerComponent<eBulletProp> {

    @property({type: Movable, displayName: "移动组件"})
    public mover: Movable = null;

    @property({type: Sprite, displayName: "子弹精灵"})
    public bulletSprite: Sprite = null;

    @property({type: BulletData})
    public readonly bulletData: BulletData = null;

    public isRunning: boolean = false;
    public elapsedTime: number = 0;

    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
    // public isDestructive : Property<boolean>;          // 是否可被破坏
    // public isDestructiveOnHit : Property<boolean>;     // 命中时是否被销毁
    public duration : Property<number>;                // 子弹持续时间(超出后销毁回收)
    public delayDestroy : Property<number>;            // 延迟销毁时间

    public isFacingMoveDir : Property<boolean>;        // 是否面向移动方向
    public isTrackingTarget : Property<boolean>;       // 是否追踪目标
    public speed : Property<number>;                   // 子弹速度
    public speedAngle : Property<number>;              // 子弹速度角度
    public acceleration : Property<number>;            // 子弹加速度
    public accelerationAngle : Property<number>;       // 子弹加速度角度

    onLoad(): void {
        if (!this.mover) {
            this.mover = this.getComponent(Movable);
            if (!this.mover) {
                this.mover = this.addComponent(Movable);
            }
        }

        this.mover.onBecomeInvisible = () => {
            BulletSystem.onDestroyBullet(this);
        };

        // this._eventRunners = [];
        // this.bulletData.eventGroupData.forEach(eventGroup => {
        //     const runner = new EventGroupRunner(eventGroup);
        //     this._eventRunners.push(runner);
        // });
    }

    /**
     * TODO: 如果后续自己写碰撞, 这里要相应进行替换
     */
    onCollisionEnter(other: Node, self: Node): void {
        // 判断另一个node也是子弹或者非子弹, 进行相应处理
        // 根据this.isDestructive 和 this.isDestructiveOnHit
        BulletSystem.onDestroyBullet(this);
    }
    
    public onCreate(): void {
        this.isRunning = true;
        this.elapsedTime = 0;

        this.resetProperties();

        // this._eventRunners.forEach(runner => {
        //     runner.init(this);
        // });
    }

    public resetProperties(): void {
        this.clear();
        if (!this.bulletData) return;

        this.duration = this.addProperty(eBulletProp.Duration, this.bulletData.duration);
        this.delayDestroy = this.addProperty(eBulletProp.DelayDestroy, this.bulletData.delayDestroy);
        // this.isDestructive = this.addProperty(eBulletProp.IsDestructive, this.bulletData.isDestructive);
        // this.isDestructiveOnHit = this.addProperty(eBulletProp.IsDestructiveOnHit, this.bulletData.isDestructiveOnHit);
        // this.damage = this.addProperty(eBulletProp.Damage, this.bulletData.damage);

        this.isFacingMoveDir = this.addProperty(eBulletProp.IsFacingMoveDir, this.bulletData.isFacingMoveDir);
        this.isTrackingTarget = this.addProperty(eBulletProp.IsTrackingTarget, this.bulletData.isTrackingTarget);
        this.speed = this.addProperty(eBulletProp.Speed, this.bulletData.speed);
        this.acceleration = this.addProperty(eBulletProp.Acceleration, this.bulletData.acceleration);
        this.accelerationAngle = this.addProperty(eBulletProp.AccelerationAngle, this.bulletData.accelerationAngle);

        // listen to property changes
        this.isFacingMoveDir.on((value) => {
            this.mover.isFacingMoveDir = value;
        });
        this.isTrackingTarget.on((value) => {
            this.mover.isTrackingTarget = value;
        });
        this.speed.on((value) => {
            this.mover.speed = value;
        });
        this.speedAngle.on((value) => {
            this.mover.speedAngle = value;
        });
        this.acceleration.on((value) => {
            this.mover.acceleration = value;
        });
        this.accelerationAngle.on((value) => {
            this.mover.accelerationAngle = value;
        });
    }

    public tick(dt:number) : void {
        if (!this.isRunning) return;

        this.elapsedTime += dt;
        if (this.elapsedTime > this.duration.value) {
            this.destroySelf();
            return;
        }

        this.mover?.tick(dt);
    }

    public destroySelf(): void {
        this.isRunning = false;
        const cb = () => {
            if (!this.node || !this.node.isValid) return;
            
            if (EDITOR) {
                this.node.destroy();
            } else {
                ObjectPool.returnNode(this.node);
            }
        };
        if (this.delayDestroy.value > 0) {
            this.scheduleOnce(() => {
                cb();
            }, this.delayDestroy.value);
        } else {
            cb();
        }
    }

    // public applyAction(actType: eBulletActionType, actValue: number) {
    //     switch (actType) {
    //         case eBulletActionType.Bullet_Duration:
    //             this.duration = actValue;
    //             break;
    //         case eBulletActionType.Bullet_ElapsedTime:
    //             this.elapsedTime = actValue;
    //             break;
    //         case eBulletActionType.Bullet_PosX:
    //             this.node.setPosition(actValue, this.node.position.y, this.node.position.z);
    //             break;
    //         case eBulletActionType.Bullet_PosY:
    //             this.node.setPosition(this.node.position.x, actValue, this.node.position.z);
    //             break;
    //         case eBulletActionType.Bullet_Damage:
    //             this.damage = actValue;
    //             break;
    //         case eBulletActionType.Bullet_Speed:
    //             this.mover.speed = actValue;
    //             break;
    //         case eBulletActionType.Bullet_SpeedAngle:
    //             this.mover.speedAngle = actValue;
    //             break;
    //         case eBulletActionType.Bullet_Acceleration:
    //             this.mover.acceleration = actValue;
    //             break;
    //         case eBulletActionType.Bullet_AccelerationAngle:
    //             this.mover.accelerationAngle = actValue;
    //             break;
    //         case eBulletActionType.Bullet_Scale:
    //             this.node.setScale(actValue, actValue, actValue);
    //             break;
    //         case eBulletActionType.Bullet_ColorR:
    //             const colorR = this.bulletSprite.color;
    //             this.bulletSprite.color = new Color(actValue, colorR.g, colorR.b, colorR.a);
    //             break;
    //         case eBulletActionType.Bullet_ColorG:
    //             const colorG = this.bulletSprite.color;
    //             this.bulletSprite.color = new Color(colorG.r, actValue, colorG.b, colorG.a);
    //             break;
    //         case eBulletActionType.Bullet_ColorB:
    //             const colorB = this.bulletSprite.color;
    //             this.bulletSprite.color = new Color(colorB.r, colorB.g, actValue, colorB.a);
    //             break;
    //         case eBulletActionType.Bullet_ColorA:
    //             const colorA = this.bulletSprite.color;
    //             this.bulletSprite.color = new Color(colorA.r, colorA.g, colorA.b, actValue);
    //             break;
    //         case eBulletActionType.Bullet_FaceMovingDir:
    //             this.mover.isFacingMoveDir = actValue === 1 ? true : false;
    //             break;
    //         case eBulletActionType.Bullet_TrackingTarget:
    //             this.mover.isTrackingTarget = actValue === 1 ? true : false;
    //             // TODO: 子弹可能需要自动选择目标并追踪
    //             break;
    //         case eBulletActionType.Bullet_Destructive:
    //             this.isDestructive = actValue === 1 ? true : false;
    //             break;
    //         case eBulletActionType.Bullet_DestructiveOnHit:
    //             this.isDestructiveOnHit = actValue === 1 ? true : false;
    //             break;
    //     }
    // }
}

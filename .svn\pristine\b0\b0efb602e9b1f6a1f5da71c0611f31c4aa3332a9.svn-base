import { _decorator, Component, Label, resources, Sprite, SpriteFrame } from "cc";
import { ButtonPlus } from "../common/components/button/ButtonPlus";
import { EventMgr } from "../../event/EventManager";
import { MainEvent } from "./MainEvent";
const { ccclass, property } = _decorator;

//参考 BagItem
@ccclass('BuidingUI')
export class BuidingUI extends Component {

    @property(Label)
    title: Label | null = null;

    @property(Sprite)
    sprt: Sprite | null = null;

    private index: any = 0;

    protected onLoad(): void {
        this.getComponent(ButtonPlus)!.addClick(this.onClick, this)
    }

    protected onDestroy(): void {

    }

    private onClick() {
        EventMgr.emit(MainEvent.BattleItemClick, this.index, this.title!.string);
    }

    public setNewFrame(imageUrl: string) {
        resources.load(`Game/texture/common/itemImage/${imageUrl}`, SpriteFrame, (err, spriteFrame) => {
            this.sprt!.spriteFrame = spriteFrame;
        });
    }

    public setTitle(index: any, title: string) {
        this.index = index;
        this.title!.string = title;

    }

    //cell的容器去手动调用
    onRenderItem(item: { name: string }) {

    }
}
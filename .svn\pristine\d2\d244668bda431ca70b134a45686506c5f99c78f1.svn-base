import { _decorator, Component } from 'cc';
import BaseComp from './BaseComp';

const { ccclass } = _decorator;

@ccclass('Entity')
export default class Entity extends Component {

    new_uuid = 0;
    m_comps = new Map(); // 存储组件的 Map


    /**
     * 获取指定类型的组件
     * @param {string} type 组件类型
     * @returns {any} 组件实例
     */
    getComp(type:string) {
        return this.m_comps.get(type);
    }

    /**
     * 获取指定类型的所有组件
     * @param {string} type 组件类型
     * @returns {any} 组件实例
     */
    getComps(type: string) {
        return this.m_comps.get(type);
    }

    /**
     * 添加组件
     * @param {string} type 组件类型
     * @param {any} comp 组件实例
     * @returns {any} 添加的组件实例
     */
    addComp(type: string, comp: BaseComp) {
        this.m_comps.set(type, comp);
        return comp;
    }

    /**
     * 移除指定类型的组件
     * @param {string} type 组件类型
     */
    removeComp(type: string) {
        const comp = this.getComp(type);
        if (comp) {
            comp.remove();
        }
        this.m_comps.delete(type);
    }

    /**
     * 移除所有组件
     */
    removeAllComp() {
        if (this.m_comps != null) {
            Array.from(this.m_comps.values()).forEach((comp) => {
                comp.remove();
            });
            this.m_comps.clear();
        }
    }

    /**
     * 移除除指定组件外的其他组件
     * @param {BaseComp} keepComp 要保留的组件
     */
    removeOtherComps(keepComp: BaseComp) {
        if (this.m_comps != null) {
            this.m_comps.forEach((comp, type) => {
                if (comp !== keepComp) {
                    this.m_comps.delete(type);
                }
            });
        }
    }
}
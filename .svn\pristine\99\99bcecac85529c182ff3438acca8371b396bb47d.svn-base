import { _decorator, Component, Label, Sprite } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('PKRewardIcon')
export class PKRewardIcon extends Component {

    @property(Label)
    rewardText: Label | null = null;
    @property(Sprite)
    rewardIcon: Sprite | null = null;
    @property(Label)
    rewardNum: Label | null = null;

    protected onLoad(): void {

    }

    public setData(num: number): void {
        this.rewardIcon!.node.setScale(0.5, 0.5);
        this.rewardNum!.string = num.toString();
    }
}




import { _decorator, Vec2, v2, Size, BoxCollider2D } from 'cc';
const { ccclass, property, menu } = _decorator;

import FCollider, { ColliderType } from "./FCollider";
import { GameConst } from '../const/GameConst';
import Entity from '../ui/base/Entity';

@ccclass('FBoxCollider')
@menu("碰撞组件Ex/FBoxCollider")
export default class FBoxCollider extends FCollider {
    public worldPoints: Vec2[] = [v2(), v2(), v2(), v2()];
    public worldEdge: Vec2[] = [];
    public isConvex: boolean = true;
    public get type() {
        return ColliderType.Box;
    }

    @property(Size)
    private _size: Size = Size.ONE;

    @property
    public get size(): Size {
        return this._size;
    }
    public set size(value: Size) {
        this._size = new Size(
            value.width < 0 ? 0 : value.width,
            value.height < 0 ? 0 : value.height
        );
    }

    onLoad(): void {
        let collider  =  this.node.getComponent(BoxCollider2D);
        if (collider) {
            this.size = collider.size;
            this.offset = v2(collider.offset.x, collider.offset.y);
        }
    }

    init(entity:Entity, size: Size = Size.ZERO, offset: Vec2 = v2(0, 0)) {
        this.initBaseData(entity, offset);
        if (size) {
            this.size = size;
        }
    }

    

    draw() {
        if (!GameConst.ColliderDraw) {
            return;
        }

        let collider  =  this.node.getComponent(BoxCollider2D);
        if (!collider) {
            collider = this.node.addComponent(BoxCollider2D);
            collider.size = this.size;
            collider.offset.x = this.offset.x;
            collider.offset.y = this.offset.y;
        }
    }
}

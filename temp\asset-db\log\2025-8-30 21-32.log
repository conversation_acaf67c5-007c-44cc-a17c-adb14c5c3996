2025-8-30 21:32:50-debug: start **** info
2025-8-30 21:32:50-log: Cannot access game frame or container.
2025-8-30 21:32:50-debug: asset-db:require-engine-code (511ms)
2025-8-30 21:32:50-log: meshopt wasm decoder initialized
2025-8-30 21:32:51-log: [bullet]:bullet wasm lib loaded.
2025-8-30 21:32:51-log: [box2d]:box2d wasm lib loaded.
2025-8-30 21:32:51-log: Cocos Creator v3.8.6
2025-8-30 21:32:51-log: Forward render pipeline initialized.
2025-8-30 21:32:51-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:31.34MB, end 79.94MB, increase: 48.61MB
2025-8-30 21:32:51-log: Using legacy pipeline
2025-8-30 21:32:52-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.67MB, end 287.09MB, increase: 206.41MB
2025-8-30 21:32:51-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.82MB, end 83.99MB, increase: 3.17MB
2025-8-30 21:32:52-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.02MB, end 288.82MB, increase: 204.80MB
2025-8-30 21:32:52-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.97MB, end 287.12MB, increase: 207.15MB
2025-8-30 21:32:52-debug: run package(web-mobile) handler(enable) start
2025-8-30 21:32:52-debug: run package(web-mobile) handler(enable) success!
2025-8-30 21:32:52-debug: run package(wechatgame) handler(enable) start
2025-8-30 21:32:52-debug: run package(wechatprogram) handler(enable) start
2025-8-30 21:32:52-debug: run package(wechatgame) handler(enable) success!
2025-8-30 21:32:52-debug: run package(wechatprogram) handler(enable) success!
2025-8-30 21:32:52-debug: run package(windows) handler(enable) success!
2025-8-30 21:32:52-debug: run package(windows) handler(enable) start
2025-8-30 21:32:52-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-30 21:32:52-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-30 21:32:52-debug: run package(cocos-service) handler(enable) success!
2025-8-30 21:32:52-debug: run package(cocos-service) handler(enable) start
2025-8-30 21:32:52-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-30 21:32:52-debug: run package(im-plugin) handler(enable) success!
2025-8-30 21:32:52-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-30 21:32:52-debug: run package(im-plugin) handler(enable) start
2025-8-30 21:32:52-debug: run package(bullet_editor) handler(enable) success!
2025-8-30 21:32:52-debug: run package(event_editor_panel) handler(enable) start
2025-8-30 21:32:52-debug: run package(event_editor_panel) handler(enable) success!
2025-8-30 21:32:52-debug: run package(level-editor) handler(enable) start
2025-8-30 21:32:52-debug: run package(bullet_editor) handler(enable) start
2025-8-30 21:32:52-debug: run package(placeholder) handler(enable) success!
2025-8-30 21:32:52-debug: run package(level-editor) handler(enable) success!
2025-8-30 21:32:52-debug: run package(placeholder) handler(enable) start
2025-8-30 21:32:52-debug: asset-db:worker-init: initPlugin (1119ms)
2025-8-30 21:32:52-debug: [Assets Memory track]: asset-db:worker-init start:31.33MB, end 288.19MB, increase: 256.87MB
2025-8-30 21:32:52-debug: Run asset db hook programming:beforePreStart ...
2025-8-30 21:32:52-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-30 21:32:52-debug: Run asset db hook programming:beforePreStart success!
2025-8-30 21:32:52-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-30 21:32:53-debug: asset-db:worker-init (2680ms)
2025-8-30 21:32:53-debug: asset-db-hook-programming-beforePreStart (931ms)
2025-8-30 21:32:53-debug: asset-db-hook-engine-extends-beforePreStart (930ms)
2025-8-30 21:32:53-debug: Preimport db internal success
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\leveldata
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Network
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data\gm
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data\bag
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data\equip
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\collider-system
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\const
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\scenes
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\leveldata\trigger
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\gm
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\texture\map
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\mail
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\pk
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\conditions
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\bulletDanmu
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\friend
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\mail
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\pk
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\boss
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\enemy
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\plane\components\display
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\MainUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\MyApp.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorElemUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorWaveParam.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditorWaveUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data\DataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\leveldata\leveldata.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Network\NetMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\DevLoginUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data\bag\Bag.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data\equip\EquipSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Data\gm\GM.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\Easing.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\EventRunner.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\PropertyContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\collider-system\FBoxCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\collider-system\FCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\const\GameResourceList.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\BossData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\EventConditionData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\EnemyWave.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\BulletManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\GlobalDataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\MainPlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\leveldata\trigger\LevelDataEventTrigger.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\leveldata\trigger\newTrigger.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\leveldata\trigger\LevelDataEventTriggerWave.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\gm\GmUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\BattleUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\BuidingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\BuildingInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\MapModeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\PopupUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\WheelSpinnerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\EmitterEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\IEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\conditions\IEventCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\bullet\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\data\bullet\EventGroupData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\AngleComp.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\BaseComp.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\Entity.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\PfFrameAnim.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\TrackComponent.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\bullet\CircleZoomFly.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\BaseScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\CircleZoomScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\map\LevelBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\map\LevelElemUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\friend\FriendAddUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\friend\FriendCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\friend\FriendStrangerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\friend\FriendListUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\mail\MailCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\mail\MailUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\pk\PKRewardIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\pk\PKUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossBase.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossEntity.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossUnit.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossUnitBase.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAnim.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyBase.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyEntity.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlaneRole.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\ui\main\plane\components\display\CombineDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:32:54-debug: Preimport db assets success
2025-8-30 21:32:54-debug: Run asset db hook programming:afterPreStart ...
2025-8-30 21:32:54-debug: starting packer-driver...
2025-8-30 21:33:05-debug: initialize scripting environment...
2025-8-30 21:33:05-debug: [[Executor]] prepare before lock
2025-8-30 21:33:05-debug: Set detail map pack:///resolution-detail-map.json: {
  "./chunks/73/73ceddc080e3578026d7a71d8933049a50b1a439.js": {
    "__unresolved_2": {
      "error": "Error: Module \"../../data/EventActionData\" not found for file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/BulletActionHandlers.ts",
      "messages": [
        {
          "level": "warn",
          "text": "Did you forget the extension? Please note that you can not omit extension in module specifier."
        }
      ]
    }
  },
  "./chunks/54/5481ee518aa4100fad485a10be86b96398900683.js": {
    "__unresolved_2": {
      "error": "Error: Module \"../../data/EventActionData\" not found for file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterActionHandlers.ts",
      "messages": [
        {
          "level": "warn",
          "text": "Did you forget the extension? Please note that you can not omit extension in module specifier."
        }
      ]
    }
  }
}

2025-8-30 21:33:05-debug: [[Executor]] prepare after unlock
2025-8-30 21:33:05-debug: Run asset db hook programming:afterPreStart success!
2025-8-30 21:33:05-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-30 21:33:05-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-30 21:33:05-debug: Start up the 'internal' database...
2025-8-30 21:33:05-debug: asset-db-hook-programming-afterPreStart (11442ms)
2025-8-30 21:33:05-debug: asset-db:worker-effect-data-processing (578ms)
2025-8-30 21:33:05-debug: asset-db-hook-engine-extends-afterPreStart (579ms)
2025-8-30 21:33:06-debug: Start up the 'assets' database...
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: asset-db:worker-startup-database[internal] (14645ms)
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\prefab\event.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\editor\level\prefab\wave.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\BattleUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\PopupUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\texture\map\chapter1_map1.png
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\mail\MailCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\mail\MailUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\pk\PKRewardIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\ui\main\pk\PKUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\texture\map\chapter1_map1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\texture\map\chapter1_map1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\texture\map\chapter1_map1.plist
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cReImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\texture\map\chapter1_map1.plist@28e3b
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: %cReImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\texture\map\chapter1_map1.plist@89f65
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:33:06-debug: [Assets Memory track]: asset-db:worker-init: startup start:175.50MB, end 193.59MB, increase: 18.10MB
2025-8-30 21:33:06-debug: lazy register asset handler *
2025-8-30 21:33:06-debug: lazy register asset handler directory
2025-8-30 21:33:06-debug: lazy register asset handler dragonbones
2025-8-30 21:33:06-debug: lazy register asset handler text
2025-8-30 21:33:06-debug: lazy register asset handler spine-data
2025-8-30 21:33:06-debug: lazy register asset handler dragonbones-atlas
2025-8-30 21:33:06-debug: lazy register asset handler javascript
2025-8-30 21:33:06-debug: lazy register asset handler json
2025-8-30 21:33:06-debug: lazy register asset handler terrain
2025-8-30 21:33:06-debug: lazy register asset handler typescript
2025-8-30 21:33:06-debug: lazy register asset handler prefab
2025-8-30 21:33:06-debug: lazy register asset handler sprite-frame
2025-8-30 21:33:06-debug: lazy register asset handler scene
2025-8-30 21:33:06-debug: lazy register asset handler image
2025-8-30 21:33:06-debug: lazy register asset handler sign-image
2025-8-30 21:33:06-debug: lazy register asset handler alpha-image
2025-8-30 21:33:06-debug: lazy register asset handler texture
2025-8-30 21:33:06-debug: lazy register asset handler texture-cube
2025-8-30 21:33:06-debug: lazy register asset handler erp-texture-cube
2025-8-30 21:33:06-debug: lazy register asset handler render-texture
2025-8-30 21:33:06-debug: lazy register asset handler texture-cube-face
2025-8-30 21:33:06-debug: lazy register asset handler tiled-map
2025-8-30 21:33:06-debug: lazy register asset handler rt-sprite-frame
2025-8-30 21:33:06-debug: lazy register asset handler gltf
2025-8-30 21:33:06-debug: lazy register asset handler buffer
2025-8-30 21:33:06-debug: lazy register asset handler gltf-mesh
2025-8-30 21:33:06-debug: lazy register asset handler gltf-skeleton
2025-8-30 21:33:06-debug: lazy register asset handler gltf-animation
2025-8-30 21:33:06-debug: lazy register asset handler gltf-embeded-image
2025-8-30 21:33:06-debug: lazy register asset handler gltf-material
2025-8-30 21:33:06-debug: lazy register asset handler material
2025-8-30 21:33:06-debug: lazy register asset handler physics-material
2025-8-30 21:33:06-debug: lazy register asset handler fbx
2025-8-30 21:33:06-debug: lazy register asset handler effect
2025-8-30 21:33:06-debug: lazy register asset handler effect-header
2025-8-30 21:33:06-debug: lazy register asset handler animation-clip
2025-8-30 21:33:06-debug: lazy register asset handler animation-graph
2025-8-30 21:33:06-debug: lazy register asset handler animation-graph-variant
2025-8-30 21:33:06-debug: lazy register asset handler audio-clip
2025-8-30 21:33:06-debug: lazy register asset handler gltf-scene
2025-8-30 21:33:06-debug: lazy register asset handler bitmap-font
2025-8-30 21:33:06-debug: lazy register asset handler animation-mask
2025-8-30 21:33:06-debug: lazy register asset handler sprite-atlas
2025-8-30 21:33:06-debug: lazy register asset handler ttf-font
2025-8-30 21:33:06-debug: lazy register asset handler auto-atlas
2025-8-30 21:33:06-debug: lazy register asset handler particle
2025-8-30 21:33:06-debug: lazy register asset handler label-atlas
2025-8-30 21:33:06-debug: lazy register asset handler render-stage
2025-8-30 21:33:06-debug: lazy register asset handler instantiation-material
2025-8-30 21:33:06-debug: lazy register asset handler render-flow
2025-8-30 21:33:06-debug: lazy register asset handler render-pipeline
2025-8-30 21:33:06-debug: lazy register asset handler instantiation-skeleton
2025-8-30 21:33:06-debug: lazy register asset handler instantiation-animation
2025-8-30 21:33:06-debug: lazy register asset handler instantiation-mesh
2025-8-30 21:33:06-debug: lazy register asset handler video-clip
2025-8-30 21:33:06-debug: asset-db:worker-startup-database[assets] (13377ms)
2025-8-30 21:33:06-debug: asset-db:start-database (14734ms)
2025-8-30 21:33:06-debug: asset-db:ready (19093ms)
2025-8-30 21:33:06-debug: fix the bug of updateDefaultUserData
2025-8-30 21:33:06-debug: init worker message success
2025-8-30 21:33:06-debug: programming:execute-script (4ms)
2025-8-30 21:33:07-debug: [Build Memory track]: builder:worker-init start:192.47MB, end 206.64MB, increase: 14.16MB
2025-8-30 21:33:07-debug: builder:worker-init (280ms)
2025-8-30 21:39:19-debug: refresh db internal success
2025-8-30 21:39:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-30 21:39:19-debug: refresh db assets success
2025-8-30 21:39:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-30 21:39:19-debug: asset-db:refresh-all-database (101ms)
2025-8-30 21:39:24-debug: refresh db internal success
2025-8-30 21:39:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-30 21:39:24-debug: refresh db assets success
2025-8-30 21:39:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-30 21:39:24-debug: asset-db:refresh-all-database (92ms)
2025-8-30 21:39:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-30 21:39:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-30 21:39:27-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-30 21:39:27-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (9ms)
2025-8-30 22:46:09-debug: refresh db internal success
2025-8-30 22:46:09-debug: refresh db assets success
2025-8-30 22:46:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-30 22:46:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-30 22:46:09-debug: asset-db:refresh-all-database (127ms)
2025-8-30 22:46:09-debug: asset-db:worker-effect-data-processing (5ms)
2025-8-30 22:46:09-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-8-31 13:51:52-debug: refresh db internal success
2025-8-31 13:51:52-debug: refresh db assets success
2025-8-31 13:51:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 13:51:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 13:51:52-debug: asset-db:refresh-all-database (117ms)
2025-8-31 13:51:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-31 13:51:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-31 13:52:13-debug: refresh db internal success
2025-8-31 13:52:13-debug: refresh db assets success
2025-8-31 13:52:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 13:52:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 13:52:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-31 13:52:13-debug: asset-db:refresh-all-database (92ms)
2025-8-31 13:52:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-31 13:52:19-debug: refresh db internal success
2025-8-31 13:52:19-debug: refresh db assets success
2025-8-31 13:52:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 13:52:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 13:52:19-debug: asset-db:refresh-all-database (94ms)
2025-8-31 13:52:19-debug: asset-db:worker-effect-data-processing (4ms)
2025-8-31 13:52:19-debug: asset-db-hook-engine-extends-afterRefresh (4ms)
2025-8-31 13:53:32-debug: refresh db internal success
2025-8-31 13:53:32-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\ExchangeMap.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\scenes
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\GameRuleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\StageManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\Controller.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\base\Entity.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:53:32-debug: refresh db assets success
2025-8-31 13:53:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 13:53:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 13:53:32-debug: asset-db:refresh-all-database (134ms)
2025-8-31 13:53:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-31 13:53:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-31 13:55:07-debug: refresh db internal success
2025-8-31 13:55:07-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\ActionRegistry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-31 13:55:07-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\EmitterActionHandlers.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-31 13:55:07-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\GenericActionRunner.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-31 13:55:07-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\BulletActionHandlers.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-31 13:55:07-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\ActionValue.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-31 13:55:07-debug: %cDestroy%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions\IActionHandler.ts
background: #ffb8b8; color: #000;
color: #000;
2025-8-31 13:55:07-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\bullet\actions
background: #aaff85; color: #000;
color: #000;
2025-8-31 13:55:07-debug: refresh db assets success
2025-8-31 13:55:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 13:55:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 13:55:07-debug: asset-db:refresh-all-database (128ms)
2025-8-31 13:55:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-31 13:55:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)

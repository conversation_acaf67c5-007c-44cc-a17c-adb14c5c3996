import { Bag } from "./bag/Bag";
import { BaseInfo } from "./BaseInfo";
import { Equip } from "./equip/Equip";
import { GameLevel } from "./GameLevel";
import { GM } from "./gm/GM";



//数据基础类
export interface IData {
    init(): void;
    update(): void;
}

// 数据管理类，负责游戏数据的管理和提供
export class DataManager {
    // 单例实例
    public static readonly Instance: DataManager = new DataManager();

    // 基础信息
    public baseInfo: BaseInfo = new BaseInfo();
    // 背包数据
    public bag: Bag = new Bag();
    // 装备数据
    public equip: Equip = new Equip()
    // 游戏关卡数据
    public gameLevel: GameLevel = new GameLevel();
    // gm数据
    public gm: GM = new GM();

    constructor() { }
    // 数据更新
    public update(): void {
        this.baseInfo.update();
        this.bag.update();
        this.equip.update();
        this.gameLevel.update();
    }

    // 初始化数据
    public init(): void {
        this.baseInfo.init();
        this.gm.init();
        this.bag.init();
        this.equip.init();
        this.gameLevel.init();
    }
}

export const DataMgr = DataManager.Instance
// @ts-ignore
window["DataMgr"] = DataMgr;
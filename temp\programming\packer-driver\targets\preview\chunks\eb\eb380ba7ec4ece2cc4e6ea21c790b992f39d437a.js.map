{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts"], "names": ["_decorator", "Component", "director", "EPhysics2DDrawFlags", "find", "Node", "PhysicsSystem2D", "GameIns", "GameEnum", "GameMapRun", "BattleLayer", "GameConst", "ccclass", "property", "GameMain", "onLoad", "gameMainUI", "fColliderManager", "enable", "setGlobalColliderEnterCall", "colliderA", "colliderB", "entity", "onCollide", "ColliderDraw", "instance", "debugDrawFlags", "<PERSON><PERSON><PERSON>", "start", "battleManager", "startLoading", "showGameResult", "isSuccess", "gameEnd", "active", "LabelWin", "LabelFail", "onBtnAgainClicked", "mainReset", "loadScene", "update", "deltaTime", "gameType", "GameType", "Common", "lateUpdate", "dt", "gameRuleManager", "isInBattle", "isGameWillOver"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAmBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,mB,OAAAA,mB;AAAqBC,MAAAA,I,OAAAA,I;AAAkBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,e,OAAAA,e;;AACrFC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;AACAC,MAAAA,U;;AACAC,MAAAA,W;;AAEEC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;0BAGjBc,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,qC,UAGRA,QAAQ,CAACR,IAAD,C,2BATb,MACaS,QADb,SAC8Bb,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAY1Bc,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,kCAAQC,UAAR,GAAqB,IAArB;AAEA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,MAAzB,GAAkC,IAAlC;AACA;AAAA;AAAA,kCAAQD,gBAAR,CAAyBE,0BAAzB,CAAoD,CAACC,SAAD,EAAuBC,SAAvB,KAA+C;AAAA;;AAC/F,iCAAAD,SAAS,CAACE,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BF,SAA9B;AACA,iCAAAA,SAAS,CAACC,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BH,SAA9B;AACH,WAHD;;AAKA,cAAI;AAAA;AAAA,sCAAUI,YAAd,EAA4B;AACxBlB,YAAAA,eAAe,CAACmB,QAAhB,CAAyBP,MAAzB,GAAkC,IAAlC;AACAZ,YAAAA,eAAe,CAACmB,QAAhB,CAAyBC,cAAzB,GAA0CvB,mBAAmB,CAACwB,IAA9D;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACH;;AAEDC,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,eAAKC,OAAL,CAAcC,MAAd,GAAuB,IAAvB;AAEA,cAAIC,QAAQ,GAAG/B,IAAI,CAAC,UAAD,EAAa,KAAK6B,OAAlB,CAAnB;AACA,cAAIG,SAAS,GAAGhC,IAAI,CAAC,WAAD,EAAc,KAAK6B,OAAnB,CAApB;AACAE,UAAAA,QAAQ,CAAED,MAAV,GAAmBF,SAAnB;AACAI,UAAAA,SAAS,CAAEF,MAAX,GAAoB,CAACF,SAArB;AACH;;AAEDK,QAAAA,iBAAiB,GAAG;AAChB,eAAKJ,OAAL,CAAcC,MAAd,GAAuB,KAAvB;AACA;AAAA;AAAA,kCAAQL,aAAR,CAAsBS,SAAtB;AACApC,UAAAA,QAAQ,CAACqC,SAAT,CAAmB,MAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B;AACA,cAAIA,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ,CADiB,CACc;AAClC;;AAED,kBAAQ;AAAA;AAAA,kCAAQZ,aAAR,CAAsBa,QAA9B;AACI,iBAAK;AAAA;AAAA,sCAASC,QAAT,CAAkBC,MAAvB;AACI;AAAA;AAAA,sCAAQf,aAAR,CAAsBW,MAAtB,CAA6BC,SAA7B;AACA;AAHR;AAKH;;AAEDI,QAAAA,UAAU,CAACC,EAAD,EAAmB;AACzB;AACA,cAAI;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,UAAxB,MAAwC;AAAA;AAAA,kCAAQD,eAAR,CAAwBE,cAAxB,EAA5C,EAAsF;AAClF;AAAA;AAAA,oCAAQhC,gBAAR,CAAyBuB,MAAzB,CAAgCM,EAAhC;AACH;AACJ;;AApEmC,O;;;;;iBAIJ,I;;;;;;;iBAEE,I;;;;;;;iBAGX,I", "sourcesContent": ["import { _decorator, Asset, Component, director, EPhysics2DDrawFlags, find, Game, game, Node, PhysicsSystem2D, Prefab, sp, SpriteAtlas } from 'cc';\r\nimport { GameIns } from '../GameIns';\r\nimport GameEnum from '../const/GameEnum';\r\nimport GameMapRun from '../ui/map/GameMapRun';\r\nimport BattleLayer from '../ui/layer/BattleLayer';\r\nimport FCollider from '../collider-system/FCollider';\r\nimport { GameConst } from '../const/GameConst';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameMain')\r\nexport class GameMain extends Component {\r\n\r\n\r\n    @property(GameMapRun)\r\n    GameMapRun: GameMapRun | null = null;\r\n    @property(BattleLayer)\r\n    BattleLayer: BattleLayer | null = null;\r\n\r\n    @property(Node)\r\n    gameEnd: Node | null = null;\r\n\r\n\r\n    protected onLoad(): void {\r\n        GameIns.gameMainUI = this;\r\n        \r\n        GameIns.fColliderManager.enable = true;\r\n        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider)=> {\r\n            colliderA.entity?.onCollide?.(colliderB);\r\n            colliderB.entity?.onCollide?.(colliderA);\r\n        });\r\n\r\n        if (GameConst.ColliderDraw) {\r\n            PhysicsSystem2D.instance.enable = true;\r\n            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;\r\n        }\r\n    }\r\n\r\n    start() {\r\n        GameIns.battleManager.startLoading(1);                           \r\n    }\r\n\r\n    showGameResult(isSuccess: boolean) {\r\n        this.gameEnd!.active = true;\r\n\r\n        let LabelWin = find(\"LabelWin\", this.gameEnd!);\r\n        let LabelFail = find(\"LabelFail\", this.gameEnd!);\r\n        LabelWin!.active = isSuccess;\r\n        LabelFail!.active = !isSuccess;\r\n    }\r\n\r\n    onBtnAgainClicked() {\r\n        this.gameEnd!.active = false;\r\n        GameIns.battleManager.mainReset();\r\n        director.loadScene(\"Main\");\r\n    }\r\n\r\n    /**\r\n     * 每帧更新逻辑\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        // 限制 deltaTime 的最大值\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667; // 约等于 1/60 秒\r\n        }\r\n\r\n        switch (GameIns.battleManager.gameType) {\r\n            case GameEnum.GameType.Common:\r\n                GameIns.battleManager.update(deltaTime);\r\n                break;\r\n        }\r\n    }\r\n\r\n    lateUpdate(dt: number): void {\r\n        // GameIns.colliderManager.update();\r\n        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {\r\n            GameIns.fColliderManager.update(dt);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}
import { _decorator, EditBox, Label, Node } from 'cc';
import csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';
import { res } from '../../AutoGen/Luban/schema';
import { DataMgr } from '../../Data/DataManager';
import { MyApp } from '../../MyApp';
import { BaseUI, UILayer, UIMgr, UIOpt } from '../UIMgr';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
import { DropDown } from '../common/components/dropdown/DropDown';
import List from '../common/components/list/List';
import { GmButtonUI } from './GmButtonUI';

const { ccclass, property } = _decorator;
@ccclass('GmUI')
export class GmUI extends BaseUI {
    public static getUrl(): string { return "ui/gm/GmUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getUIOption(): UIOpt {
        return {
            isClickBgHideUI: true,
        }
    }
    @property(DropDown)
    tabDropDown: DropDown = null;
    @property(List)
    cmdBtnList: List = null;
    @property(ButtonPlus)
    sendBtn: ButtonPlus = null;
    @property(Node)
    inputParentNode: Node = null;
    @property(Label)
    logLabel: Label = null;
    @property(ButtonPlus)
    clearBtn: ButtonPlus = null;

    private _inputNodeList: EditBox[] = [];

    protected onLoad(): void {
        this.inputParentNode.children.forEach(v => {
            this._inputNodeList.push(v.getComponentInChildren(EditBox))
        })
        this.sendBtn.addClick(this.onSendBtnClick, this)
        this.clearBtn.addClick(this.onClearBtnClick, this)
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GM, this.onGmMsg, this)
    }

    private onClearBtnClick() {
        this.logLabel.string = ""
    }

    private onGmMsg(res: csproto.cs.IS2CMsg) {
        this.log(`接收 => ${res.body.gm.text}`)
    }

    private onCmdBtnRender(item: Node, idx: number) {
        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey)[idx]
        const label = item.getComponentInChildren(Label)
        label.string = cmdInfo.cfg.name
    }

    private onCmdBtnClick(item: Node, idx: number) {
        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.name !== "")[idx]
        this._inputNodeList[0].string = cmdInfo.cfg.cmd
        this._inputNodeList[1].placeholder = cmdInfo.cfg.desc
    }

    private onDropDownOptionRender(nd: Node, optKey: res.GMTabID) {
        const cfg = DataMgr.gm.getCmdBtnListByTabID(optKey)
        nd.getComponentInChildren(Label).string = cfg[0].cfg.tabName
    }

    private onDropDownOptionClick(optKey: res.GMTabID) {
        const cfg = DataMgr.gm.getCmdBtnListByTabID(optKey).filter(v => v.cfg.name !== "")
        this.cmdBtnList.numItems = cfg.length
    }

    private log(msg: string) {
        this.logLabel.string += `[${new Date().toLocaleString()}] ${msg}\n`
    }

    private onSendBtnClick() {
        this._inputNodeList.forEach(v => {
            if (v.string == v.placeholder) {
                v.string = ""
            }
        })
        const cmd = this._inputNodeList[0].string
        const args = this._inputNodeList[1].string
        let target = this._inputNodeList[2].string
        if (target !== "") {
            target = `[destuin ${target}]`
        }
        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.cmd === cmd)[0]
        if (cmdInfo?.onSendClick) {
            const res = cmdInfo.onSendClick(args)
            this.log(`[${cmd}] 发送 => ${res}`)
        } else {
            const gmStr = cmd + " " + target + " " + args
            MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GM, {
                gm: { gm_str: gmStr }
            })
            this.log(`[${cmd}] 发送 => ${gmStr}`)
        }
    }

    // 显示 UI 的方法，需要子类实现
    public onShow(...args: any[]): Promise<void> {
        const tabIDList = DataMgr.gm.tabIDList
        this.tabDropDown.init(tabIDList, this.onDropDownOptionRender.bind(this), this.onDropDownOptionClick.bind(this))
        this.cmdBtnList.numItems = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).length
        this.onClearBtnClick()
        return
    };

    // 隐藏 UI 的方法，需要子类实现
    public onHide(...args: any[]): Promise<void> {
        UIMgr.openUI(GmButtonUI)
        return
    };
    // 关闭 UI 的方法，需要子类实现
    public onClose(...args: any[]): Promise<void> {
        UIMgr.openUI(GmButtonUI)
        return
    };
}


import { _decorator, size, UIOpacity, UITransform, v2, Vec2, Vec3 } from "cc";
import EnemyEntity from "./EnemyEntity";
import GameEnum from "../../../const/GameEnum";
import EnemyEffectComp from "./EnemyEffectComp";
import EnemyAttrComponent from "./EnemyAttrComponent";
import { Tools } from "../../../utils/Tools";
import BattleLayer from "../../layer/BattleLayer";
import { GameConst } from "../../../const/GameConst";
import Bullet from "../../bullet/Bullet";
import { GameIns } from "../../../GameIns";
import { MainPlane } from "../mainPlane/MainPlane";
import FCollider, { ColliderGroupType } from "../../../collider-system/FCollider";
import FBoxCollider from "../../../collider-system/FBoxCollider";
import { EnemyUI } from "db://assets/scripts/AutoGen/Luban/schema";
import Entity from "../../base/Entity";
import { EnemyUIData } from "../../../data/EnemyData";


const { ccclass, property } = _decorator;

@ccclass
export default class EnemyBase extends EnemyEntity {

    uiData: EnemyUIData | null = null;
    scaleType = -1;
    propertyRate: number[] = [];
    _curHp = 0;
    exp = 0;
    maxHp = 0;
    defence = 0;
    resist: number[] = [];
    hurtBuffMap = new Map();
    _hurtBuffTime = new Map();
    _fireDemage = 0;
    _fireHurtCd = 0;
    _fireHurtTime = 0;
    _isFireCirt = false;
    _buffCountArr = new Map();
    collideComp: FBoxCollider | null = null;
    _collideLevel = GameEnum.EnemyCollideLevel.Main;
    bCollideDead = false;
    damaged = false;
    _isTracked = false;
    _countTime = 0;
    _bStandBy = false;
    _standByTime = 0;
    _standByEnd = false;
    //_lootArr = [];
    _lootItemArr = [];
    _curLoot = null;
    _lootHp = 0;
    _lootNeedHp = 0;
    _lootHpUnit = 0;
    _itemParent: EnemyBase | null = null;
    _isItem = false;
    effectComp: EnemyEffectComp | null = null;
    attrCom: EnemyAttrComponent | null  = null;
    dieBullet = false;
    bullets: Bullet[] = [];

    get itemParent() {
        return this._itemParent;
    }
    set itemParent(value) {
        this._itemParent = value;
    }
    get isItem() {
        return this._isItem;
    }
    set isItem(value) {
        this._isItem = value;
    }
    get collideLevel() {
        return this._collideLevel;
    }
    set collideLevel(value) {
        this._collideLevel = value;
    }

    /**
     * 获取碰撞是否可用
     * @returns {boolean} 是否可用
     */
    get collideAble(): boolean{
        return (this.collideComp && this.collideComp.isEnable) ?? false;
    }

    /**
     * 设置碰撞是否可用
     * @param {boolean} value 是否可用
     */
    set collideAble(value: boolean) {
        if (this.collideComp) {
            this.collideComp.isEnable = value;
        }
    }
    /**
  * 预加载敌人组件
  */
    preLoad() {
        // 添加碰撞组件并初始化
        this.collideComp = this.addComponent(FBoxCollider);
        this.collideComp!.init(this);
        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;

        // 获取敌人效果组件
        this.effectComp = this.node.getComponent(EnemyEffectComp)!;

        // 添加属性组件
        this.attrCom = Tools.addScript(this.node, EnemyAttrComponent);
    }

    /**
     * 重置敌人状态
     */
    reset() {
        super.reset();
        this.uiData = null;
        this.scaleType = -1;
        this._curHp = 0;
        this.maxHp = 0;
        this.exp = 0;
        this.collideAtk = 0;
        this.hurtBuffMap.clear();
        this._hurtBuffTime.clear();
        this.resist = [];
        this._isTracked = false;
        this._countTime = 0;
        this._bStandBy = false;
        this._standByTime = 0;
        this._collideLevel = GameEnum.EnemyCollideLevel.Main;
        this.damaged = false;
        //this._lootArr = [];
        this._lootItemArr = [];
        this._curLoot = null;
        this._lootHp = 0;
        this._lootNeedHp = 0;
        this._lootHpUnit = 0;
        this._itemParent = this;
        this._isItem = false;
        this.removeAllBuffEffect();
        this.dieBullet = false;
        this.bullets = [];
    }

    /**
     * 设置 UI 数据
     * @param {Object} data UI 数据
     */
    setUIData(data: EnemyUIData) {
        this.uiData = data;
        if (this.uiData) {
            this.setCollideData(this.uiData.collider);
            this.initComps();
        }
    }

    /**
     * 设置缩放类型
     * @param {number} type 缩放类型
     */
    setScaleType(type: number) {
        this.scaleType = type;
    }

    /**
     * 获取缩放类型
     * @returns {number} 缩放类型
     */
    getScaleType() {
        return this.scaleType;
    }

    /**
     * 初始化属性
     * @param {string} attr 属性字符串
     */
    initAttr(attr: string) {
        this.attrCom!.init(this, attr || "");
    }

    /**
     * 检查是否具有指定属性
     * @param {string} attr 属性名称
     * @returns {boolean} 是否具有该属性
     */
    hasAttribution(attr: string) {
        return this.attrCom && this.attrCom.hasAttribution(attr);
    }

    /**
     * 设置经验值
     * @param {number} exp 经验值
     */
    setExp(exp: number) {
        this.exp = exp;
    }

    /**
     * 初始化组件
     */
    initComps() {
        this.m_comps.forEach((comp) => {
            comp.init(this);
        });
    }

    /**
     * 开始战斗
     */
    startBattle() {
        this.collideAble = true;
    }

    /**
     * 添加掉落物
     * @param {Object} loot 掉落物
     */
    /*addLoot(loot) {
        this._lootArr.push(loot);
    }*/

    /**
     * 更新游戏逻辑
     * @param {number} deltaTime 帧间隔时间
     */
    updateGameLogic(deltaTime: number) {
        if (this.isDead || this.checkStandby(deltaTime)) {
            return;
        }

        // 更新技能抗性
        this.updateSkillResist(deltaTime);

        // 更新属性组件
        if (this.attrCom) {
            this.attrCom.updateGameLogic(deltaTime);
        }

        this._isTracked = false;

        // 更新所有组件
        this.m_comps.forEach((comp) => {
            comp.update(deltaTime);
        });
    }

    /**
     * 检查待机状态
     * @param {number} deltaTime 帧间隔时间
     * @returns {boolean} 是否处于待机状态
     */
    checkStandby(deltaTime: number) {
        this._countTime += deltaTime;
        if (this._bStandBy) {
            if (this._countTime > this._standByTime) {
                this.active = true;
                this._bStandBy = false;
                this._countTime = 0;
                this._standByEnd = true;
                this.node!.getComponent(UIOpacity)!.opacity = 255;
                this.startBattle();
            }
            return true;
        }
        return false;
    }

    /**
     * 更新技能抗性
     * @param {number} deltaTime 帧间隔时间
     */
    updateSkillResist(deltaTime: number) {
        this.hurtBuffMap.forEach((value, key) => {
            const time = this._hurtBuffTime.get(key);
            if (time !== null && time > 0) {
                this._hurtBuffTime.set(key, time - deltaTime);
            } else {
                this.removeBuff(key);
            }
        });
    }

    /**
     * 初始化属性倍率
     * @param {Array<number>} rates 属性倍率数组
     */
    initPropertyRate(rates: number[]) {
        this.propertyRate = rates;
        if (this.propertyRate.length > 2) {
            this.curHp *= this.propertyRate[0];
            this.maxHp = this.curHp;
            this.attack *= this.propertyRate[1];
            this.collideAtk *= this.propertyRate[2];
        }
    }

    /**
     * 获取 UI 数据
     * @returns {Object} UI 数据
     */
    getUIData() {
        return this.uiData;
    }
    /**
 * 设置待机时间
 * @param {number} time 待机时间
 */
    setStandByTime(time: number) {
        this._bStandBy = true;
        this._standByTime = time;
        this.node!.getComponent(UIOpacity)!.opacity = 0;
    }

    /**
     * 检查是否处于待机状态
     * @returns {boolean} 是否待机
     */
    isStandBy() {
        return this._bStandBy;
    }

    /**
     * 设置敌人位置
     * @param {number} x X 坐标
     * @param {number} y Y 坐标
     * @param {boolean} isTracked 是否被追踪
     */
    setPos(x: number, y: number, isTracked = false) {
        this.node.setPosition(x, y);
        this._isTracked = isTracked;
    }

    /**
     * 获取是否被追踪
     * @returns {boolean} 是否被追踪
     */
    get isTracked() {
        return this._isTracked;
    }

    /**
     * 获取方向
     * @returns {Vec2} 方向向量
     */
    getDir() {
        return Vec2.ZERO;
    }

    /**
     * 获取角度
     * @returns {number} 角度
     */
    getAngle() {
        return 0;
    }

    /**
     * 检查是否满血
     * @returns {boolean} 是否满血
     */
    isFullBlood() {
        return this.curHp >= this.maxHp;
    }

    /**
     * 获取最大血量
     * @returns {number} 最大血量
     */
    getMaxHp() {
        return this.maxHp;
    }

    /**
     * 获取当前血量
     * @returns {number} 当前血量
     */
    get curHp() {
        return this._curHp;
    }

    /**
     * 设置当前血量
     * @param {number} hp 当前血量
     */
    set curHp(hp) {
        this._curHp = hp;
    }

    /**
     * 获取血量百分比
     * @returns {number} 血量百分比
     */
    getHpPercent() {
        return this.curHp / this.maxHp;
    }

    /**
     * 改变血量
     * @param {number} delta 血量变化值
     */
    changeHp(delta: number) {
        this.curHp += delta;
        if (this.curHp < 0) {
            this.curHp = 0;
        } else if (this.curHp > this.maxHp) {
            this.curHp = this.maxHp;
        }
        this.checkHp();
    }

    /**
     * 检查血量是否为 0
     * @returns {boolean} 是否死亡
     */
    checkHp() {
        if (this.curHp <= 0) {
            this.die(GameEnum.EnemyDestroyType.Die);
            return true;
        }
        return false;
    }

    /**
     * 设置当前血量
     * @param {number} hp 当前血量
     */
    setCurHp(hp: number) {
        this.curHp = hp;
    }

    /**
     * 获取当前血量
     * @returns {number} 当前血量
     */
    getCurHp() {
        return this.curHp;
    }

    /**
     * 检查敌人是否可以被伤害
     * @returns {boolean} 是否可以被伤害
     */
    isDamageable() {
        if (this.sceneLayer < 0 && !this._bStandBy) {
            if (this.damaged) {
                return true;
            }
            let position = this.node.position;
            if (this.itemParent !== this) {
                position = this.node!.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);
                position = BattleLayer.me.enemyPlaneLayer!.getComponent(UITransform)!.convertToNodeSpaceAR(position);
            }
            return (
                position.y < 0 &&
                position.x > -GameConst.ViewCenter.x &&
                position.x < GameConst.ViewCenter.x
            );
        }
        return !this._bStandBy;
    }

    /**
     * 设置敌人是否已被伤害
     * @param {boolean} value 是否已被伤害
     */
    setDamaged(value: boolean) {
        this.damaged = value;
    }
    /**
     * 获取技能抗性
     * @param {number} skillType 技能类型
     * @returns {number} 技能抗性
     */
    getSkillResist(skillType: number) {
        return this.resist[skillType] || 1;
    }

    /**
     * 添加 Buff
     * @param {number} buffType Buff 类型
     * @param {Array<number>} params Buff 参数
     * @param {boolean} isCritical 是否暴击
     */
    addBuff(buffType: number, params: number[], isCritical: boolean) {
        this.hurtBuffMap.set(buffType, true);
        switch (buffType) {
            case GameEnum.EnemyBuff.Ice:
                this._hurtBuffTime.set(buffType, params[0]);
                break;
            case GameEnum.EnemyBuff.Fire:
                this._isFireCirt = isCritical;
                this._hurtBuffTime.set(buffType, params[0]);
                this._fireDemage = params[1] === 0 ? this._fireDemage : params[1];
                this._fireHurtCd = params[2];
                break;
            case GameEnum.EnemyBuff.Treat:
                const count = this._buffCountArr.get(buffType) || 0;
                this._buffCountArr.set(buffType, count + 1);
                break;
        }
        this.onAddBuff(buffType);
    }

    /**
     * 移除 Buff
     * @param {number} buffType Buff 类型
     */
    removeBuff(buffType: number) {
        this.hurtBuffMap.delete(buffType);
        this._hurtBuffTime.delete(buffType);
        let shouldRemove = true;
        if (buffType === GameEnum.EnemyBuff.Treat) {
            const count = this._buffCountArr.get(buffType);
            if (count > 0) {
                this._buffCountArr.set(buffType, count - 1);
                if (count - 1 > 0) {
                    shouldRemove = false;
                }
            }
        }
        if (shouldRemove) {
            this.onRemoveBuff(buffType);
        }
    }

    /**
     * 移除所有 Buff 效果
     */
    removeAllBuffEffect() {
        this._buffCountArr.clear();
        if (this.effectComp) {
            this.effectComp.removeAllBuffEffect();
        }
    }

    /**
     * 当 Buff 被移除时的回调
     * @param {number} buffType Buff 类型
     */
    onRemoveBuff(buffType: number) {
        if (this.effectComp) {
            this.effectComp.removeBuff(buffType);
        }
    }

    /**
     * 当 Buff 被添加时的回调
     * @param {number} buffType Buff 类型
     */
    onAddBuff(buffType: number) {
        if (this.effectComp && this.uiData) {
            const buffData = this.uiData.skillResistUIDict.get(buffType);
            switch (buffType) {
                case GameEnum.EnemyBuff.Ice:
                    if (buffData && buffData.length > 0) {
                        this.effectComp.addBuff(buffType, buffData);
                    }
                    break;
                case GameEnum.EnemyBuff.Fire:
                    const hpParam = this.uiData.hpParam;
                    const fireDamage = 0.4 * hpParam[3];
                    if (!buffData && fireDamage) {
                        // @ts-ignore
                        buffData[0][0] = fireDamage;
                    }
                    this.effectComp.addBuff(buffType, buffData);
                    break;
                case GameEnum.EnemyBuff.Treat:
                    this.effectComp.addBuff(buffType, buffData);
                    break;
            }
        }
    }

    /**
     * 检查是否具有指定的伤害 Buff
     * @param {number} buffType Buff 类型
     * @returns {boolean} 是否具有该 Buff
     */
    hasHurtBuff(buffType: number) {
        return this.hurtBuffMap.get(buffType);
    }

    /**
     * 设置碰撞数据
     * @param {Array<number>} collider 碰撞数据
     * @param {number} scale 缩放比例
     * @param {Vec2} offset 偏移量
     */
    setCollideData(collider: number[] , entity = null, offset = v2(0, 0)) {
        if (this.collideComp) {
            this.collideComp.init(entity || this,size(collider[3],collider[4]), offset);
        }
    }

    /**
     * 设置碰撞实体
     * @param {Entity} entity 碰撞实体
     */
    setCollideEntity(entity: Entity) {
        if (this.collideComp) {
            this.collideComp.entity = entity;
        }
    }

    /**
     * 设置碰撞偏移
     * @param {Vec2} offset 偏移量
     */
    setCollideOffset(offset: Vec2) {
        if (this.collideComp) {
            this.collideComp.offset = offset;
        }
    }

    /**
     * 设置碰撞缩放
     * @param {number} widthScale 宽度缩放
     * @param {number} heightScale 高度缩放
     */
    setCollideScale(widthScale: number, heightScale: number) {
        if (this.collideComp && this.uiData) {   
            const values = this.uiData.collider;
            this.collideComp.size = size(
                values[3] * widthScale,
                values[4] * heightScale
            );
        }
    }

    onCollide(collider: FCollider) {
        if (this.active && !this.isDead) {
            if (collider.entity instanceof Bullet) {
                const attack = collider.entity.getAttack(this);
                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(),attack);
                const finalDamage = Math.max(attack / 10, attack - this.defence);
                if (this.hurt(finalDamage)) {
                }
            } else if (collider.entity instanceof MainPlane && this.collideLevel === GameEnum.EnemyCollideLevel.Main && this.bCollideDead) {
                this.die(GameEnum.EnemyDestroyType.Die);
            }
        }
    }
    /**
     * 处理敌人受到的伤害
     * @param {number} damage 伤害值
     * @returns {boolean} 是否成功处理伤害
     */
    hurt(damage: number) {
        if (!this.active || this.isDead || !this.isDamageable()) {
            return false;
        }
        this.changeHp(-damage);
        this._checkHurtLoot(damage);
        this.onHurt();
        return true;
    }

    /**
     * 检查是否需要生成掉落物
     * @param {number} damage 伤害值
     */
    _checkHurtLoot(damage: number) {
        if (this._curLoot) {
            this.checkLoot(damage);
        }
    }

    /**
     * 检查并生成掉落物
     * @param {number} damage 伤害值
     * @param {Vec2} position 掉落物生成位置
     */
    checkLoot(damage = 0, position = Vec2.ZERO) {
        // if (this.isDead && this.scaleType !== GameEnum.EnemyScale.None) {
        //     const lootType = GameIns.lootManager.checkLoot(this.scaleType);
        //     GameIns.lootManager.addProp(
        //         this.node.convertToWorldSpaceAR(position),
        //         this.uiData.lootParam1,
        //         lootType
        //     );
        // }
    }

    /**
     * 当敌人受到伤害时的回调
     */
    onHurt() { }

    /**
     * 处理敌人死亡逻辑
     * @param {number} destroyType 敌人销毁类型
     */
    die(destroyType: number) {
        if (this.isDead) {
            return;
        }
        this.isDead = true;
        this.collideAble = false;

        if (this.attrCom) {
            this.attrCom.die();
        }

        this.onDie(destroyType);
    }

    /**
     * 敌人死亡时的回调
     * @param {number} destroyType 敌人销毁类型
     */
    onDie(destroyType: number) {
        if (destroyType === GameEnum.EnemyDestroyType.Die) {

            if (this.dieBullet) {
                for (const bullet of this.bullets) {
                    bullet.dieRemove();
                }
            }

            if (!this.isItem) {
                // TaskManager.TaskMgr.taskNumberChange(TaskManager.TaskType.KillPlane, 1);
                // TaskManager.TaskMgr.achievementNumberChange(TaskManager.AchievementType.KillEnemy, 1);
                // GameData.GData.killEnemyNumber += 1;
            }
        }

        this.bullets = [];

        switch (this.type) {
            case GameEnum.EnemyType.Ligature:
            case GameEnum.EnemyType.LigatureLine:
                break;
            default:
                let position = this.node.position;
                if (this.sceneLayer < 0) {
                    if (
                        this.type === GameEnum.EnemyType.Turret ||
                        this.type === GameEnum.EnemyType.GoldBox ||
                        this.type === GameEnum.EnemyType.LigatureUnit
                    ) {
                        position = this.node!.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);
                        position = BattleLayer.me.enemyPlaneLayer!.getComponent(UITransform)!.convertToNodeSpaceAR(position);
                    }
                } else {
                    const worldPosition = this.node!.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);
                    position = BattleLayer.me!.enemyPlaneLayer!.getComponent(UITransform)!.convertToNodeSpaceAR(worldPosition);
                }
                GameIns.enemyManager.checkEnemyDieBomb(position);
        }
    }

    /**
     * 准备移除敌人时的回调
     */
    willRemove() { }

    /**
     * 显示属性护盾
     */
    showAttrShield() {
        if (this.attrCom) {
            this.attrCom.showAttrShield();
        }
    }

    /**
     * 播放敌人死亡动画
     */
    playDieAnim() {
        if (!this.uiData) {
            return;
        }

        if (this.uiData.blastSound > 0) {
            // GameIns.audioManager.playEffect(`blast${this.uiData.blastSound}`);
        }

        this.scheduleOnce(() => {
            this.removeAllBuffEffect();
            this.onDieAnimEnd();
        }, 0.1);
    }

    /**
     * 敌人死亡动画结束时的回调
     */
    onDieAnimEnd() { }

    /**
     * 添加子弹到敌人
     * @param {Bullet} bullet 子弹对象
     */
    addBullet(bullet: Bullet) {
        if (this.dieBullet && this.bullets) {
            this.bullets.push(bullet);
        }
    }

    /**
     * 从敌人移除子弹
     * @param {Bullet} bullet 子弹对象
     */
    removeBullet(bullet: Bullet) {
        if (this.dieBullet && this.bullets) {
            const index = this.bullets.indexOf(bullet);
            if (index >= 0) {
                this.bullets.splice(index, 1);
            }
        }
    }
}

import { misc, v2, Vec2 } from "cc";
import { GameConst } from "../../const/GameConst";
import { GameIns } from "../../GameIns";
import BaseComp from "../base/BaseComp";

export default class CircleZoomFly extends BaseComp {
    private m_nowR: number = 0;
    private index: number = 1;
    private initR: number = 0;
    private trueR: number = 0;
    private offsetR: number = 0;
    private dir: number = 1;
    private speedVec: Vec2 = Vec2.ZERO;
    private useSpeed: Vec2 = Vec2.ZERO;
    private m_angle: number = 0;
    private m_mainEntity: any;
    private m_atkPos: Vec2 = Vec2.ZERO;
    private m_maxR: number = 0;
    private m_speed: number = 0;
    private m_beginTime: number = 0;
    private m_changeTime: number = 0;
    private paras: {
        r: number;
        difV: number;
        maxR: number;
        XV: number;
        YV: number;
        counts: number;
        face: number;
    };
    props: any;

    constructor(props: any) {
        super();
        this.props = props;
        const para = props.para;
        this.paras = {
            r: para[0],
            difV: para[1],
            maxR: para[2],
            XV: para[3],
            YV: para[4],
            counts: para[5],
            face: para[6],
        };
    }

    onInit() {
        this.m_entity!.node.setScale(this.m_entity!.node.scale.x, -1);
        this.m_beginTime = 0;
        this.m_changeTime = 0;
        this.m_speed = this.props.initialve + Math.random() * this.props.spdiff;
        this.speedVec = new Vec2(0, this.m_speed);
    }

    setData(angle: number, mainEntity: any, initR: number, maxR: number, trueR: number, atkPos: Vec2) {
        this.m_mainEntity = mainEntity;
        this.m_angle = angle;
        this.useSpeed = this.speedVec.rotate(misc.degreesToRadians(this.m_angle));
        this.initR = initR;
        this.m_maxR = maxR;
        this.trueR = trueR;
        this.m_atkPos = atkPos;
        this.index = 0;
        this.offsetR = this.initR - this.trueR;
    }

    calculateSpeed(deltaTime: number) {
        if (this.m_beginTime >= this.props.time && this.m_changeTime < this.props.accnumber) {
            this.m_changeTime += deltaTime;
            this.useSpeed = this.speedVec.rotate(misc.degreesToRadians(this.m_angle));
        }

        let layerSpeed = 0;
        if (this.m_mainEntity) {
            layerSpeed = GameIns.sceneManager.getLayerSpeed(this.m_mainEntity);
        }

        this.m_entity!.node.x += this.useSpeed.x * deltaTime;
        this.m_entity!.node.y += this.useSpeed.y * deltaTime - layerSpeed * deltaTime;
    }

    calculateAngle(deltaTime: number) {
        deltaTime = 0.016666666666667; // Fixed time step
        const atkX = this.m_atkPos.x;
        const atkY = this.m_atkPos.y;

        this.dir = this.initR > this.m_maxR ? -1 : 1;
        this.m_nowR = this.trueR * this.index * this.dir + this.trueR;

        const angleStep = (this.m_speed * this.paras.difV * deltaTime) / this.trueR * (180 / Math.PI);
        const direction = Math.pow(-1, this.paras.face);

        this.m_angle += angleStep * direction;
        this.m_angle += 3600; // Prevent negative angles

        const radius = this.m_nowR + this.offsetR;
        const rotatedPos = new Vec2(radius, 0).rotate(misc.degreesToRadians(this.m_angle));

        let pos = rotatedPos.add(new Vec2(atkX, atkY));
        this.m_entity!.node.setPosition(pos.x, pos.y);
        this.m_entity!.node.angle = (this.m_angle % 360) - 180 * this.paras.face;

        this.index += this.paras.difV * deltaTime;
        this.useSpeed = this.speedVec.rotate(misc.degreesToRadians(this.m_angle));
    }

    angToDeg(angle: number): number {
        return angle * (Math.PI / 180);
    }

    getTrueR(targetPos: Vec2): number {
        return Math.sqrt(
            Math.pow(this.m_entity!.node.x - targetPos.x, 2) +
            Math.pow(this.m_entity!.node.y - targetPos.y, 2)
        );
    }

    calculateXYMove(deltaTime: number) {
        this.m_atkPos.y += this.paras.YV * 0.016666666666667;
        this.m_atkPos.x += this.paras.XV * 0.016666666666667;
    }

    update(deltaTime: number) {
        if (GameConst.GameAble) {
            this.m_beginTime += deltaTime = deltaTime > 0.2 ? 0.016666666666667 : deltaTime;

            if (Math.abs(this.m_nowR - this.m_maxR) > 1) {
                this.calculateAngle(deltaTime);
            } else {
                this.calculateSpeed(deltaTime);
            }

            this.calculateXYMove(deltaTime);
        }
    }
}
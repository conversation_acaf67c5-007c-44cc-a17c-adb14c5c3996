import { _decorator, Button, Component, Label, Node, resources, Sprite, SpriteAtlas, SpriteFrame } from 'cc';
import { MyApp } from '../../../MyApp';
import { ResItem } from '../../../AutoGen/Luban/schema';
import { UIMgr } from '../../UIMgr';
import { PopupUI } from '../PopupUI';
import Long from 'long';
const { ccclass, property } = _decorator;

@ccclass('MailCellUI')
export class MailCellUI extends Component {

    @property(Sprite)
    mailIcon: Sprite | null = null;

    @property(Label)
    mailTitle: Label | null = null;

    @property(Label)
    mailContent: Label | null = null;

    @property(Button)
    btnClick: Button | null = null;

    itemID: number | null = null;

    start() {
        const id = Long.fromString("1234567890"); // 从字符串创建
        if (id.lte(0)) {

        } else {

        }
    }

    update(deltaTime: number) {

    }
    onButtonClick() {
        UIMgr.openUI(PopupUI, '物品ID：' + this.itemID);
    }
    public setData(itemID: number): void {
        this.itemID = itemID;
        let item: ResItem | undefined = MyApp.lubanTables.TbItem.get(itemID);
        this.mailTitle!.string = item?.name || "";
        this.mailContent!.string = item?.name || "";
        resources.load(`Game/texture/common/common/ag_1`, SpriteFrame, (err, spriteFrame) => {
            this.mailIcon!.spriteFrame = spriteFrame;
        });

    }
}



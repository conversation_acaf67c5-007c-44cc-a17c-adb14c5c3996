{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BuildingInfoUI.ts"], "names": ["_decorator", "Layout", "Node", "ScrollView", "UITransform", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ButtonPlus", "ccclass", "property", "BuildingInfoUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "onLoad", "len", "scrollLayout", "node", "children", "length", "wid", "getComponent", "width", "sx", "spacingX", "newWid", "hei", "scrollContent", "height", "setContentSize", "closeBtn", "addClick", "closeUI", "onDestroy", "onShow", "args", "update", "dt", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;;AACtCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;gCAGjBW,c,WADZF,OAAO,CAAC,gBAAD,C,UAGHC,QAAQ,CAACP,UAAD,C,UAERO,QAAQ,CAACR,IAAD,C,UAERQ,QAAQ,CAACT,MAAD,C,UAERS,QAAQ;AAAA;AAAA,mC,2BATb,MACaC,cADb;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAWnB,eAANC,MAAM,GAAW;AAAE,iBAAO,wBAAP;AAAkC;;AAC7C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC9CC,QAAAA,MAAM,GAAS;AACrB,cAAIC,GAAG,GAAG,KAAKC,YAAL,CAAmBC,IAAnB,CAAwBC,QAAxB,CAAiCC,MAA3C;;AACA,cAAIJ,GAAG,GAAG,CAAV,EAAa;AACT,gBAAIK,GAAG,GAAG,KAAKJ,YAAL,CAAmBC,IAAnB,CAAyBC,QAAzB,CAAkC,CAAlC,EAAsCG,YAAtC,CAAmDlB,WAAnD,EAAiEmB,KAA3E;AACA,gBAAIC,EAAE,GAAG,KAAKP,YAAL,CAAmBQ,QAA5B;AACA,gBAAIC,MAAM,GAAG,CAACV,GAAG,GAAG,CAAP,IAAYQ,EAAZ,GAAiBH,GAAG,GAAGL,GAApC;AACA,gBAAIW,GAAG,GAAG,KAAKC,aAAL,CAAoBN,YAApB,CAAiClB,WAAjC,EAA+CyB,MAAzD;AACA,iBAAKD,aAAL,CAAoBN,YAApB,CAAiClB,WAAjC,EAA+C0B,cAA/C,CAA8DJ,MAA9D,EAAsEC,GAAtE;AACH;;AACD,eAAKI,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACH;;AACY,cAAPA,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAActB,cAAd;AACH;;AAESuB,QAAAA,SAAS,GAAS,CAE3B;;AAEW,cAANC,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAE3C;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAElC;;AAEW,cAANC,MAAM,CAAC,GAAGH,IAAJ,EAAgC,CAAG;;AAElC,cAAPI,OAAO,CAAC,GAAGJ,IAAJ,EAAgC,CAAG;;AA1CT,O;;;;;iBAGX,I;;;;;;;iBAEC,I;;;;;;;iBAEC,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator, Layout, Node, ScrollView, UITransform } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr } from '../UIMgr';\r\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"BuildingInfoUI\")\r\nexport class BuildingInfoUI extends BaseUI {\r\n\r\n    @property(ScrollView)\r\n    scroll: ScrollView | null = null;\r\n    @property(Node)\r\n    scrollContent: Node | null = null;\r\n    @property(Layout)\r\n    scrollLayout: Layout | null = null;\r\n    @property(ButtonPlus)\r\n    closeBtn: ButtonPlus | null = null;\r\n\r\n    public static getUrl(): string { return \"ui/main/BuildingInfoUI\"; };\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    protected onLoad(): void {\r\n        let len = this.scrollLayout!.node.children.length;\r\n        if (len > 0) {\r\n            let wid = this.scrollLayout!.node!.children[0]!.getComponent(UITransform)!.width;\r\n            let sx = this.scrollLayout!.spacingX;\r\n            let newWid = (len - 1) * sx + wid * len;\r\n            let hei = this.scrollContent!.getComponent(UITransform)!.height;\r\n            this.scrollContent!.getComponent(UITransform)!.setContentSize(newWid, hei);\r\n        }\r\n        this.closeBtn!.addClick(this.closeUI, this);\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(BuildingInfoUI);\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n    async onShow(...args: any[]): Promise<void> {\r\n\r\n    }\r\n\r\n    protected update(dt: number): void {\r\n\r\n    }\r\n\r\n    async onHide(...args: any[]): Promise<void> { }\r\n\r\n    async onClose(...args: any[]): Promise<void> { }\r\n\r\n}\r\n"]}
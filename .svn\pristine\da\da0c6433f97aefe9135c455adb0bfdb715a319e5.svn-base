import { _decorator, Node } from 'cc';
import { EventMgr } from '../../../event/EventManager';
import { BaseUI, UILayer, UIMgr } from '../../UIMgr';
import { ButtonPlus } from '../../common/components/button/ButtonPlus';
import { BattleUI } from '../BattleUI';
import { PopupUI } from '../PopupUI';
import { PKRewardIcon } from './PKRewardIcon';
const { ccclass, property } = _decorator;

@ccclass('PKUI')
export class PKUI extends BaseUI {
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;

    @property(Node)
    friendPk: Node | null = null;
    @property(Node)
    goldCoinPk: Node | null = null;
    @property(Node)
    diamondPk: Node | null = null;
    @property(Node)
    highDiamondPk: Node | null = null;

    public static getUrl(): string { return "ui/main/pk/PKUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    protected onLoad(): void {
        this.btnClose!.addClick(this.closeUI, this);
        this.friendPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onFriendPk, this);
        this.goldCoinPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onGoldCoinPk, this);
        this.diamondPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onDiamondPk, this);
        this.highDiamondPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onHighDiamondPk, this);

        this.friendPk!.getComponentInChildren(PKRewardIcon)!.setData(888);

        let data = {
            PKRewardIcon: 123,
            PKRewardIcon2: 456,
        };
        this.highDiamondPk!.getComponentsInChildren(PKRewardIcon)!.forEach(icon => {
            const key = icon.node.name as keyof typeof data;
            const value = data[key] ?? 0; // 默认值为 0
            icon.setData(value);
        });
    }
    private onFriendPk() {
        UIMgr.openUI(PopupUI, "点击了1");
    }
    private onGoldCoinPk() {
        UIMgr.openUI(PopupUI, "点击了2");
    }
    private onDiamondPk() {
        UIMgr.openUI(PopupUI, "点击了3");
    }
    private onHighDiamondPk() {
        UIMgr.openUI(PopupUI, "点击了4");
    }

    async closeUI() {
        UIMgr.closeUI(PKUI);
        await UIMgr.openUI(BattleUI)
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this);
    }
}



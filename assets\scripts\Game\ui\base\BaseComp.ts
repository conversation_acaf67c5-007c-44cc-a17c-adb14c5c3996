import Entity from "./Entity";

export default class BaseComp {

    m_entity: Entity | null = null; // 绑定的实体
    m_enabled = true; // 是否启用


    /**
     * 初始化组件
     * @param {Entity} entity 绑定的实体
     */
    init(entity: Entity) {
        this.m_entity = entity;
        this.enabled = true;
        this.onInit();
    }

    /**
     * 获取组件是否启用
     * @returns {boolean}
     */
    get enabled() {
        return this.m_enabled;
    }

    /**
     * 设置组件是否启用
     * @param {boolean} value 是否启用
     */
    set enabled(value) {
        if (this.m_enabled !== value) {
            this.m_enabled = value;
        }
    }

    /**
     * 获取绑定的实体
     * @returns {Entity}
     */
    get entity() {
        return this.m_entity;
    }

    /**
     * 移除组件
     */
    remove() {
        this.enabled = false;
    }

    /**
     * 初始化时的回调
     */
    onInit() {}
}
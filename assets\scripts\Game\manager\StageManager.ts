import { SingletonBase } from "../../core/base/SingletonBase";
import { StageData} from "../data/StageData";
import { GameIns } from "../GameIns";
import { MyApp } from "../../MyApp";

export class StageManager extends SingletonBase<StageManager> {

    _allStageDataArr:StageData[] = [];
    _curStageDataArr:StageData[] = [];

    constructor() {
        super();
        this.initConfig();
    }

    initConfig(){
        let stages = MyApp.lubanTables.TbStage.getDataList();
        for (let data of stages) {
            const stage = new StageData();
            stage.loadJson(data);
            this._allStageDataArr.push(stage);
        }
    }

    initBattle(mainId:number, subId:number) {
        this._curStageDataArr.splice(0);
        this._allStageDataArr.forEach(stage => {
            if (stage.mainStage === mainId && stage.subStage === subId) {
                this._curStageDataArr.push(stage);
            }
        });
    }

    getBossTips() {
        return "";
    }

    gameStart() {
        GameIns.waveManager.setEnemyActions(this._curStageDataArr);
    }

    checkStage(mainId:number,subId:number):boolean{
        // for (const stage of this._allStageDataArr) {
        //     if (stage.mainStage === mainId && stage.subStage === subId) {
        //         return true;
        //     }
        // }
        return false;
    }
}
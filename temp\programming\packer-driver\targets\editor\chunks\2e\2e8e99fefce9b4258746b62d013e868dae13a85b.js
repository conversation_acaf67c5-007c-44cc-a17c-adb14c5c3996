System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "long"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Button, Component, Label, resources, Sprite, SpriteFrame, MyApp, UIMgr, PopupUI, Long, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, MailCellUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResItem(extras) {
    _reporterNs.report("ResItem", "../../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPopupUI(extras) {
    _reporterNs.report("PopupUI", "../PopupUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Button = _cc.Button;
      Component = _cc.Component;
      Label = _cc.Label;
      resources = _cc.resources;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      UIMgr = _unresolved_3.UIMgr;
    }, function (_unresolved_4) {
      PopupUI = _unresolved_4.PopupUI;
    }, function (_long) {
      Long = _long.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "037ecasVAtGQLeU6IMKSXVm", "MailCellUI", undefined);

      __checkObsolete__(['_decorator', 'Button', 'Component', 'Label', 'Node', 'resources', 'Sprite', 'SpriteAtlas', 'SpriteFrame']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MailCellUI", MailCellUI = (_dec = ccclass('MailCellUI'), _dec2 = property(Sprite), _dec3 = property(Label), _dec4 = property(Label), _dec5 = property(Button), _dec(_class = (_class2 = class MailCellUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "mailIcon", _descriptor, this);

          _initializerDefineProperty(this, "mailTitle", _descriptor2, this);

          _initializerDefineProperty(this, "mailContent", _descriptor3, this);

          _initializerDefineProperty(this, "btnClick", _descriptor4, this);

          this.itemID = null;
        }

        start() {
          const id = (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
            error: Error()
          }), Long) : Long).fromString("1234567890"); // 从字符串创建

          if (id.lte(0)) {} else {}
        }

        update(deltaTime) {}

        onButtonClick() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, '物品ID：' + this.itemID);
        }

        setData(itemID) {
          this.itemID = itemID;
          let item = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbItem.get(itemID);
          this.mailTitle.string = (item == null ? void 0 : item.name) || "";
          this.mailContent.string = (item == null ? void 0 : item.name) || "";
          resources.load(`Game/texture/common/common/ag_1`, SpriteFrame, (err, spriteFrame) => {
            this.mailIcon.spriteFrame = spriteFrame;
          });
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "mailIcon", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "mailTitle", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "mailContent", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "btnClick", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2e8e99fefce9b4258746b62d013e868dae13a85b.js.map
import { IEventAction } from "./IEventAction";
import { eEmitterProp } from "../Emitter";
import { eEasing, Easing} from "../Easing";
import { EventGroupContext } from "../EventGroup";
import { EventActionData } from "../../data/bullet/EventGroupData";

export class EmitterEventActionBase implements IEventAction {
    readonly data: EventActionData;

    protected _isCompleted: boolean = false;
    protected _elapsedTime: number = 0;
    protected _startValue: number = 0;

    constructor(data: EventActionData) {
        this.data = data;
    }

    isCompleted(): boolean {
        return this._isCompleted;
    }

    canLerp(): boolean {
        return true;
    }

    onLoad(context: EventGroupContext): void {
        this._isCompleted = false;
        this._elapsedTime = 0;
        // override this to get the correct start value
        this._startValue = 0;
    }

    onExecute(context: EventGroupContext, dt: number): void {
        this._elapsedTime += dt;
        if (this._elapsedTime >= this.data.duration) {
            this.executeInternal(context, this.data.targetValue);
            this._isCompleted = true;
        }
        else if (this.canLerp()) {
            this.executeInternal(context, this.lerpValue(this._startValue, this.data.targetValue));
        }
    }

    lerpValue(startValue: number, targetValue: number): number {
        return Easing.lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this.data.duration));
    }

    protected executeInternal(context: EventGroupContext, value: number): void {
        // Default implementation does nothing
    }
}

// 修改发射器启用状态
export class EmitterEventAction_Active extends EmitterEventActionBase {
    canLerp(): boolean {
        return false;
    }

    // onLoad(context: EventGroupContext): void {
    //     super.onLoad(context);
    //     this._startValue = context.emitter.isActive.value ? 1 : 0;
    // }

    protected executeInternal(context: EventGroupContext, value: number): void {
        // context.emitter.isActive = this.data.targetValue;
        context.emitter.setProperty<boolean>(eEmitterProp.IsActive, value === 1);
    }
}

// 修改发射器初始延迟时间
export class EmitterEventAction_InitialDelay extends EmitterEventActionBase {
    onLoad(context: EventGroupContext): void {
        super.onLoad(context);
        this._startValue = context.emitter.initialDelay.value;
    }

    protected executeInternal(context: EventGroupContext, value: number): void {
        context.emitter.setProperty<number>(eEmitterProp.InitialDelay, value);
    }
}

// 修改发射器持续时间
export class EmitterEventAction_Duration extends EmitterEventActionBase {
    onLoad(context: EventGroupContext): void {
        super.onLoad(context);
        this._startValue = context.emitter.emitDuration.value;
    }

    protected executeInternal(context: EventGroupContext, value: number): void {
        context.emitter.setProperty<number>(eEmitterProp.EmitDuration, value);
    }
}

// 修改发射器已运行时间
export class EmitterEventAction_ElapsedTime extends EmitterEventActionBase {
    onLoad(context: EventGroupContext): void {
        super.onLoad(context);
        this._startValue = context.emitter.totalElapsedTime.value;
    }

    protected executeInternal(context: EventGroupContext, value: number): void {
        context.emitter.setProperty<number>(eEmitterProp.TotalElapsedTime, value);
    }
}


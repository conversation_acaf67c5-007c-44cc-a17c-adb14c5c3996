System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, eEmitterProp, Easing, EmitterEventActionBase, EmitterEventAction_Active, EmitterEventAction_InitialDelay, EmitterEventAction_Duration, EmitterEventAction_ElapsedTime, _crd;

  function _reportPossibleCrUseOfIEventAction(extras) {
    _reporterNs.report("IEventAction", "./IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterProp(extras) {
    _reporterNs.report("eEmitterProp", "../Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEasing(extras) {
    _reporterNs.report("Easing", "../Easing", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "../EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventActionData(extras) {
    _reporterNs.report("EventActionData", "../../data/bullet/EventGroupData", _context.meta, extras);
  }

  _export({
    EmitterEventActionBase: void 0,
    EmitterEventAction_Active: void 0,
    EmitterEventAction_InitialDelay: void 0,
    EmitterEventAction_Duration: void 0,
    EmitterEventAction_ElapsedTime: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      eEmitterProp = _unresolved_2.eEmitterProp;
    }, function (_unresolved_3) {
      Easing = _unresolved_3.Easing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cf3e5fqPZK4K903tZmgBYZ", "EmitterEventActions", undefined);

      _export("EmitterEventActionBase", EmitterEventActionBase = class EmitterEventActionBase {
        constructor(data) {
          this.data = void 0;
          this._isCompleted = false;
          this._elapsedTime = 0;
          this._startValue = 0;
          this.data = data;
        }

        isCompleted() {
          return this._isCompleted;
        }

        canLerp() {
          return true;
        }

        onLoad(context) {
          this._isCompleted = false;
          this._elapsedTime = 0; // override this to get the correct start value

          this._startValue = 0;
        }

        onExecute(context, dt) {
          this._elapsedTime += dt;

          if (this._elapsedTime >= this.data.duration) {
            this.executeInternal(context, this.data.targetValue);
            this._isCompleted = true;
          } else if (this.canLerp()) {
            this.executeInternal(context, this.lerpValue(this._startValue, this.data.targetValue));
          }
        }

        lerpValue(startValue, targetValue) {
          return (_crd && Easing === void 0 ? (_reportPossibleCrUseOfEasing({
            error: Error()
          }), Easing) : Easing).lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this.data.duration));
        }

        executeInternal(context, value) {// Default implementation does nothing
        }

      }); // 修改发射器启用状态


      _export("EmitterEventAction_Active", EmitterEventAction_Active = class EmitterEventAction_Active extends EmitterEventActionBase {
        canLerp() {
          return false;
        } // onLoad(context: EventGroupContext): void {
        //     super.onLoad(context);
        //     this._startValue = context.emitter.isActive.value ? 1 : 0;
        // }


        executeInternal(context, value) {
          // context.emitter.isActive = this.data.targetValue;
          context.emitter.setProperty((_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).IsActive, value === 1);
        }

      }); // 修改发射器初始延迟时间


      _export("EmitterEventAction_InitialDelay", EmitterEventAction_InitialDelay = class EmitterEventAction_InitialDelay extends EmitterEventActionBase {
        onLoad(context) {
          super.onLoad(context);
          this._startValue = context.emitter.initialDelay.value;
        }

        executeInternal(context, value) {
          context.emitter.setProperty((_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).InitialDelay, value);
        }

      }); // 修改发射器持续时间


      _export("EmitterEventAction_Duration", EmitterEventAction_Duration = class EmitterEventAction_Duration extends EmitterEventActionBase {
        onLoad(context) {
          super.onLoad(context);
          this._startValue = context.emitter.emitDuration.value;
        }

        executeInternal(context, value) {
          context.emitter.setProperty((_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).EmitDuration, value);
        }

      }); // 修改发射器已运行时间


      _export("EmitterEventAction_ElapsedTime", EmitterEventAction_ElapsedTime = class EmitterEventAction_ElapsedTime extends EmitterEventActionBase {
        onLoad(context) {
          super.onLoad(context);
          this._startValue = context.emitter.totalElapsedTime.value;
        }

        executeInternal(context, value) {
          context.emitter.setProperty((_crd && eEmitterProp === void 0 ? (_reportPossibleCrUseOfeEmitterProp({
            error: Error()
          }), eEmitterProp) : eEmitterProp).TotalElapsedTime, value);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3b293d9a5338c158b9d4a5f8ffa24ccd04836817.js.map
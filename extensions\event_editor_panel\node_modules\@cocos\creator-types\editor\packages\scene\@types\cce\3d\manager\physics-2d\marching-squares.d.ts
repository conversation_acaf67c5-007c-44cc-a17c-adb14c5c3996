export let NONE: number;
export let UP: number;
export let LEFT: number;
export let DOWN: number;
export let RIGHT: number;
export function getBlobOutlinePoints(data: any, width: any, height: any, loop: any): any[];
export function getFirstNonTransparentPixelTopDown(): {
    x: number;
    y: number;
} | null;
export function walkPerimeter(startX: any, startY: any): any[];
export function step(x: any, y: any, data: any): void;

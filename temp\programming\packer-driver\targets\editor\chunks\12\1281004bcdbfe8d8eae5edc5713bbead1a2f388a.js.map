{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagItem.ts"], "names": ["_decorator", "Component", "Label", "math", "Node", "Sprite", "DataMgr", "EventMgr", "MyApp", "ButtonPlus", "PlaneUIEvent", "TabStatus", "ccclass", "property", "BagItem", "_item", "_tabStatus", "None", "_fakeColors", "onLoad", "getComponent", "addClick", "onClick", "onDestroy", "targetOff", "<PERSON><PERSON>", "mask", "active", "selectedIcon", "equip", "eqCombine", "isFull", "emit", "BagItemClick", "onBagTabStatusRender", "item", "Bag", "onMetaDataRender", "onCombineTabStatusRender", "size", "info", "getByGuid", "guid", "isCanCombineWith", "EquipCfg", "lubanTables", "TbEquip", "get", "item_id", "onEquipDataRender", "onItemDataRender", "TbItem", "equipCfg", "itemNum", "node", "getComponentInChildren", "string", "name", "quality", "color", "itemCfg", "count", "toString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AAI1CC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;yBAGjBc,O,WADZF,OAAO,CAAC,SAAD,C,UAEHC,QAAQ,CAACT,IAAD,C,UAERS,QAAQ,CAACT,IAAD,C,UAERS,QAAQ,CAACX,KAAD,C,2BANb,MACaY,OADb,SAC6Bb,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAQ3Bc,KAR2B,GAQC,IARD;AAAA,eAS3BC,UAT2B,GASH;AAAA;AAAA,sCAAUC,IATP;AAAA,eAU3BC,WAV2B,GAUH,CAC5B,SAD4B,EAE5B,SAF4B,EAG5B,SAH4B,EAI5B,SAJ4B,EAK5B,SAL4B,EAM5B,WAN4B,EAO5B,SAP4B,CAVG;AAAA;;AAoBzBC,QAAAA,MAAM,GAAS;AACrB,eAAKC,YAAL;AAAA;AAAA,wCAA8BC,QAA9B,CAAuC,KAAKC,OAA5C,EAAqD,IAArD;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOF,QAAAA,OAAO,GAAG;AACd,cAAI,CAAC,KAAKP,KAAV,EAAiB;;AACjB,cAAI,KAAKC,UAAL,IAAmB;AAAA;AAAA,sCAAUS,KAAjC,EAAwC;AACpC,gBAAI,KAAKC,IAAL,CAAUC,MAAV,IAAoB,CAAC,KAAKC,YAA9B,EAA4C;AACxC;AACH;;AACD,gBAAI,CAAC,KAAKF,IAAL,CAAUC,MAAX,IAAqB;AAAA;AAAA,oCAAQE,KAAR,CAAcC,SAAd,CAAwBC,MAAxB,EAAzB,EAA2D;AACvD;AACH;AACJ;;AACD;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,4CAAaC,YAA3B,EAAyC,KAAKlB,KAA9C;AACH;;AAEDmB,QAAAA,oBAAoB,CAACC,IAAD,EAA2B;AAC3C,eAAKnB,UAAL,GAAkB;AAAA;AAAA,sCAAUoB,GAA5B;AACA,eAAKrB,KAAL,GAAaoB,IAAb;AACA,eAAKP,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,eAAKD,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA,eAAKU,gBAAL;AACH;;AAEDC,QAAAA,wBAAwB,CAACH,IAAD,EAA2B;AAC/C,eAAKnB,UAAL,GAAkB;AAAA;AAAA,sCAAUS,KAA5B;AACA,eAAKV,KAAL,GAAaoB,IAAb;;AACA,cAAI;AAAA;AAAA,kCAAQN,KAAR,CAAcC,SAAd,CAAwBS,IAAxB,KAAiC,CAArC,EAAwC;AACpC,kBAAMC,IAAI,GAAG;AAAA;AAAA,oCAAQX,KAAR,CAAcC,SAAd,CAAwBW,SAAxB,CAAkC,KAAK1B,KAAL,CAAW2B,IAA7C,CAAb;;AACA,gBAAIF,IAAJ,EAAU;AACN,mBAAKZ,YAAL,CAAkBD,MAAlB,GAA2B,IAA3B;AACA,mBAAKD,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH,aAHD,MAGO;AACH,mBAAKC,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,mBAAKD,IAAL,CAAUC,MAAV,GAAmB,CAAC;AAAA;AAAA,sCAAQE,KAAR,CAAcC,SAAd,CAAwBa,gBAAxB,CAAyC,KAAK5B,KAA9C,CAApB;AACH;AACJ,WATD,MASO;AACH,iBAAKa,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,iBAAKD,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AACD,eAAKU,gBAAL;AACH;;AAEOA,QAAAA,gBAAgB,GAAG;AACvB,gBAAMO,QAAQ,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8B,KAAKhC,KAAL,CAAWiC,OAAzC,CAAjB;;AACA,cAAIJ,QAAJ,EAAc;AACV,iBAAKK,iBAAL,CAAuBL,QAAvB;AACH,WAFD,MAEO;AACH,iBAAKM,gBAAL,CAAsB;AAAA;AAAA,gCAAML,WAAN,CAAkBM,MAAlB,CAAyBJ,GAAzB,CAA6B,KAAKhC,KAAL,CAAWiC,OAAxC,CAAtB;AACH;AACJ;;AAEOC,QAAAA,iBAAiB,CAACG,QAAD,EAAqB;AAC1C,eAAKC,OAAL,CAAaC,IAAb,CAAkB3B,MAAlB,GAA2B,KAA3B;AACA,eAAK4B,sBAAL,CAA4BrD,KAA5B,EAAmCsD,MAAnC,GAA4C,CAAAJ,QAAQ,QAAR,YAAAA,QAAQ,CAAEK,IAAV,IAAkB,OAAML,QAAP,oBAAOA,QAAQ,CAAEM,OAAQ,GAAtF;AACA,eAAKJ,IAAL,CAAUC,sBAAV,CAAiClD,MAAjC,EAAyCsD,KAAzC,GAAiDxD,IAAI,CAACwD,KAAL,CAAW,KAAKzC,WAAL,CAAiBkC,QAAQ,CAACM,OAA1B,CAAX,CAAjD;AACH;;AAEOR,QAAAA,gBAAgB,CAACU,OAAD,EAAmB;AACvC,eAAKP,OAAL,CAAaC,IAAb,CAAkB3B,MAAlB,GAA2B,IAA3B;AACA,eAAK0B,OAAL,CAAaG,MAAb,GAAsB,KAAKzC,KAAL,CAAW8C,KAAX,CAAiBC,QAAjB,EAAtB;AACA,eAAKP,sBAAL,CAA4BrD,KAA5B,EAAmCsD,MAAnC,GAA4C,CAAAI,OAAO,QAAP,YAAAA,OAAO,CAAEH,IAAT,IAAiB,OAAMG,OAAP,oBAAOA,OAAO,CAAEF,OAAQ,GAApF;AACA,eAAKJ,IAAL,CAAUC,sBAAV,CAAiClD,MAAjC,EAAyCsD,KAAzC,GAAiDxD,IAAI,CAACwD,KAAL,CAAW,KAAKzC,WAAL,CAAiB0C,OAAO,CAACF,OAAzB,CAAX,CAAjD;AACH;;AAxFkC,O;;;;;iBAEd,I;;;;;;;iBAER,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator, Component, Label, math, Node, Sprite } from 'cc';\n\nimport { ResEquip, ResItem } from 'db://assets/scripts/AutoGen/Luban/schema';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { DataMgr } from \"db://assets/scripts/Data/DataManager\";\nimport { EventMgr } from \"db://assets/scripts/event/EventManager\";\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { ButtonPlus } from \"../../../../common/components/button/ButtonPlus\";\nimport { PlaneUIEvent } from \"../../PlaneEvent\";\nimport { TabStatus } from \"../../PlaneTypes\";\nconst { ccclass, property } = _decorator;\n\n@ccclass('BagItem')\nexport class BagItem extends Component {\n    @property(Node)\n    selectedIcon: Node = null;\n    @property(Node)\n    mask: Node = null;\n    @property(Label)\n    itemNum: Label = null;\n\n    private _item: csproto.cs.ICSItem = null;\n    private _tabStatus: TabStatus = TabStatus.None;\n    private _fakeColors: string[] = [\n        \"#A0A0A0\",\n        \"#1EFF00\",\n        \"#0070FF\",\n        \"#A335EE\",\n        \"#FF8000\",\n        \"#80e6e6ff\",\n        \"#E6CC80\"\n    ]\n\n    protected onLoad(): void {\n        this.getComponent(ButtonPlus).addClick(this.onClick, this)\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n\n    private onClick() {\n        if (!this._item) return\n        if (this._tabStatus == TabStatus.Merge) {\n            if (this.mask.active && !this.selectedIcon) {\n                return\n            }\n            if (!this.mask.active && DataMgr.equip.eqCombine.isFull()) {\n                return\n            }\n        }\n        EventMgr.emit(PlaneUIEvent.BagItemClick, this._item)\n    }\n\n    onBagTabStatusRender(item: csproto.cs.ICSItem) {\n        this._tabStatus = TabStatus.Bag;\n        this._item = item;\n        this.selectedIcon.active = false;\n        this.mask.active = false;\n        this.onMetaDataRender()\n    }\n\n    onCombineTabStatusRender(item: csproto.cs.ICSItem) {\n        this._tabStatus = TabStatus.Merge;\n        this._item = item;\n        if (DataMgr.equip.eqCombine.size() > 0) {\n            const info = DataMgr.equip.eqCombine.getByGuid(this._item.guid)\n            if (info) {\n                this.selectedIcon.active = true;\n                this.mask.active = true;\n            } else {\n                this.selectedIcon.active = false;\n                this.mask.active = !DataMgr.equip.eqCombine.isCanCombineWith(this._item)\n            }\n        } else {\n            this.selectedIcon.active = false;\n            this.mask.active = false;\n        }\n        this.onMetaDataRender()\n    }\n\n    private onMetaDataRender() {\n        const EquipCfg = MyApp.lubanTables.TbEquip.get(this._item.item_id)\n        if (EquipCfg) {\n            this.onEquipDataRender(EquipCfg)\n        } else {\n            this.onItemDataRender(MyApp.lubanTables.TbItem.get(this._item.item_id))\n        }\n    }\n\n    private onEquipDataRender(equipCfg: ResEquip) {\n        this.itemNum.node.active = false;\n        this.getComponentInChildren(Label).string = equipCfg?.name + `(品质:${equipCfg?.quality})`\n        this.node.getComponentInChildren(Sprite).color = math.color(this._fakeColors[equipCfg.quality])\n    }\n\n    private onItemDataRender(itemCfg: ResItem) {\n        this.itemNum.node.active = true;\n        this.itemNum.string = this._item.count.toString()\n        this.getComponentInChildren(Label).string = itemCfg?.name + `(品质:${itemCfg?.quality})`\n        this.node.getComponentInChildren(Sprite).color = math.color(this._fakeColors[itemCfg.quality])\n    }\n}"]}
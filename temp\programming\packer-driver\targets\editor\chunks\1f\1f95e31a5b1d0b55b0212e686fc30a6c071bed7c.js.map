{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/conditions/IEventCondition.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;uFAGA", "sourcesContent": ["import { EventConditionData } from \"../../data/bullet/EventGroupData\";\r\nimport { EventGroupContext } from \"../EventGroup\";\r\n\r\n// Base interfaces\r\nexport interface IEventCondition {\r\n    readonly data: EventConditionData;\r\n\r\n    evaluate(context: EventGroupContext): boolean;\r\n}\r\n\r\n"]}
import { _decorator, Component, Animation, NodeEventType, EventTouch, Vec2, Vec3 } from 'cc';
import { GameIns } from '../../GameIns';
import GameEnum from '../../const/GameEnum';
import { MainPlane } from '../plane/mainPlane/MainPlane';

const { ccclass, property } = _decorator;

@ccclass('Controller')
export class Controller extends Component {

    target: MainPlane | null = null; // 目标对象（主飞机）
    _targetStartPos: Vec3 = new Vec3(0, 0); // 目标起始位置

    /**
     * 加载时初始化
     */
    onLoad() {
        
    }

    /**
     * 开始时绑定触摸事件
     */
    start() {
        this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);
    }

    /**
     * 触摸开始事件
     * @param {Event.EventTouch} event 触摸事件
     */
    onTouchStart(event:EventTouch) {
        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机
        if (!target) {
            return; // 如果主飞机不存在，则不处理
        }
        this._targetStartPos = target.node.getPosition(); // 记录主飞机的起始位置
    }

    /**
     * 触摸移动事件
     * @param {Event.EventTouch} event 触摸事件
     */
    onTouchMove(event:EventTouch) {
        if (GameIns.gameRuleManager.gameState != GameEnum.GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
        }

        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机
        if (!target) {
            return; // 如果主飞机不存在，则不处理
        }

        let startPos = event.getUIStartLocation();
        let location = event.getUILocation();   //得到手指鼠标位置,得到的是世界坐标

        let posX = location.x - startPos.x + this._targetStartPos.x;
        let posY = location.y - startPos.y + this._targetStartPos.y;
        target.onControl(posX, posY); // 控制主飞机移动
    }

    /**
     * 触摸结束事件
     * @param {Event.EventTouch} event 触摸事件
     */
    onTouchEnd(event:EventTouch) {
        // 当前实现中，触摸结束事件未处理任何逻辑
        if (this.target) {
            // 可扩展逻辑
        }
    }
}
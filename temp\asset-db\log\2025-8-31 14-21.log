2025-8-31 14:21:45-debug: start **** info
2025-8-31 14:21:45-log: Cannot access game frame or container.
2025-8-31 14:21:45-debug: asset-db:require-engine-code (433ms)
2025-8-31 14:21:45-log: [bullet]:bullet wasm lib loaded.
2025-8-31 14:21:45-log: [box2d]:box2d wasm lib loaded.
2025-8-31 14:21:45-log: meshopt wasm decoder initialized
2025-8-31 14:21:45-log: Cocos Creator v3.8.6
2025-8-31 14:21:45-log: Using legacy pipeline
2025-8-31 14:21:45-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.94MB, end 80.09MB, increase: 49.14MB
2025-8-31 14:21:46-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.32MB, end 224.83MB, increase: 140.51MB
2025-8-31 14:21:45-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.96MB, end 84.29MB, increase: 3.33MB
2025-8-31 14:21:46-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.08MB, end 228.24MB, increase: 3.15MB
2025-8-31 14:21:47-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.81MB, end 228.44MB, increase: 147.63MB
2025-8-31 14:21:47-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.11MB, end 228.90MB, increase: 148.79MB
2025-8-31 14:21:45-log: Forward render pipeline initialized.
2025-8-31 14:21:47-debug: run package(mac) handler(enable) start
2025-8-31 14:21:47-debug: run package(migu-mini-game) handler(enable) start
2025-8-31 14:21:47-debug: run package(mac) handler(enable) success!
2025-8-31 14:21:47-debug: run package(migu-mini-game) handler(enable) success!
2025-8-31 14:21:47-debug: run package(native) handler(enable) success!
2025-8-31 14:21:47-debug: run package(ohos) handler(enable) start
2025-8-31 14:21:47-debug: run package(ohos) handler(enable) success!
2025-8-31 14:21:47-debug: run package(oppo-mini-game) handler(enable) start
2025-8-31 14:21:47-debug: run package(oppo-mini-game) handler(enable) success!
2025-8-31 14:21:47-debug: run package(native) handler(enable) start
2025-8-31 14:21:47-debug: run package(taobao-mini-game) handler(enable) start
2025-8-31 14:21:47-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-31 14:21:47-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-31 14:21:47-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-31 14:21:47-debug: run package(vivo-mini-game) handler(enable) start
2025-8-31 14:21:47-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-31 14:21:47-debug: run package(web-desktop) handler(enable) start
2025-8-31 14:21:47-debug: run package(web-desktop) handler(enable) success!
2025-8-31 14:21:47-debug: run package(web-mobile) handler(enable) start
2025-8-31 14:21:47-debug: run package(web-mobile) handler(enable) success!
2025-8-31 14:21:47-debug: run package(wechatprogram) handler(enable) start
2025-8-31 14:21:47-debug: run package(wechatprogram) handler(enable) success!
2025-8-31 14:21:47-debug: run package(wechatgame) handler(enable) start
2025-8-31 14:21:47-debug: run package(wechatgame) handler(enable) success!
2025-8-31 14:21:47-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-31 14:21:47-debug: run package(windows) handler(enable) success!
2025-8-31 14:21:47-debug: run package(windows) handler(enable) start
2025-8-31 14:21:47-debug: run package(cocos-service) handler(enable) start
2025-8-31 14:21:47-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-31 14:21:47-debug: run package(im-plugin) handler(enable) start
2025-8-31 14:21:47-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-31 14:21:47-debug: run package(im-plugin) handler(enable) success!
2025-8-31 14:21:47-debug: run package(cocos-service) handler(enable) success!
2025-8-31 14:21:47-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-31 14:21:47-debug: run package(bullet_editor) handler(enable) success!
2025-8-31 14:21:47-debug: run package(event_editor_panel) handler(enable) start
2025-8-31 14:21:47-debug: run package(event_editor_panel) handler(enable) success!
2025-8-31 14:21:47-debug: run package(level-editor) handler(enable) start
2025-8-31 14:21:47-debug: run package(level-editor) handler(enable) success!
2025-8-31 14:21:47-debug: run package(placeholder) handler(enable) success!
2025-8-31 14:21:47-debug: run package(bullet_editor) handler(enable) start
2025-8-31 14:21:47-debug: run package(placeholder) handler(enable) start
2025-8-31 14:21:47-debug: asset-db:worker-init: initPlugin (1287ms)
2025-8-31 14:21:47-debug: [Assets Memory track]: asset-db:worker-init start:30.94MB, end 225.12MB, increase: 194.18MB
2025-8-31 14:21:47-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-31 14:21:47-debug: Run asset db hook programming:beforePreStart ...
2025-8-31 14:21:47-debug: Run asset db hook programming:beforePreStart success!
2025-8-31 14:21:47-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-31 14:21:48-debug: asset-db:worker-init (2771ms)
2025-8-31 14:21:48-debug: asset-db-hook-programming-beforePreStart (964ms)
2025-8-31 14:21:48-debug: asset-db-hook-engine-extends-beforePreStart (964ms)
2025-8-31 14:21:48-debug: Preimport db internal success
2025-8-31 14:21:48-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:21:48-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:21:48-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events\Bullet
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:21:48-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events\Emitter
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:21:48-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events\Others
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:21:48-debug: Preimport db assets success
2025-8-31 14:21:48-debug: Run asset db hook programming:afterPreStart ...
2025-8-31 14:21:48-debug: starting packer-driver...
2025-8-31 14:21:53-debug: initialize scripting environment...
2025-8-31 14:21:53-debug: [[Executor]] prepare before lock
2025-8-31 14:21:53-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-31 14:21:53-debug: [[Executor]] prepare after unlock
2025-8-31 14:21:53-debug: Run asset db hook programming:afterPreStart success!
2025-8-31 14:21:53-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-31 14:21:53-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-31 14:21:53-debug: Start up the 'internal' database...
2025-8-31 14:21:53-debug: asset-db-hook-programming-afterPreStart (5425ms)
2025-8-31 14:21:53-debug: asset-db:worker-effect-data-processing (618ms)
2025-8-31 14:21:53-debug: asset-db-hook-engine-extends-afterPreStart (618ms)
2025-8-31 14:21:53-debug: Start up the 'assets' database...
2025-8-31 14:21:54-debug: asset-db:worker-startup-database[internal] (6964ms)
2025-8-31 14:21:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events\Bullet\BulletEvent_001.json
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:21:54-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events\Emitter\SampleEvent_001.json
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:21:54-debug: [Assets Memory track]: asset-db:worker-init: startup start:175.05MB, end 195.91MB, increase: 20.87MB
2025-8-31 14:21:54-debug: lazy register asset handler *
2025-8-31 14:21:54-debug: lazy register asset handler directory
2025-8-31 14:21:54-debug: lazy register asset handler spine-data
2025-8-31 14:21:54-debug: lazy register asset handler text
2025-8-31 14:21:54-debug: lazy register asset handler dragonbones-atlas
2025-8-31 14:21:54-debug: lazy register asset handler terrain
2025-8-31 14:21:54-debug: lazy register asset handler javascript
2025-8-31 14:21:54-debug: lazy register asset handler dragonbones
2025-8-31 14:21:54-debug: lazy register asset handler typescript
2025-8-31 14:21:54-debug: lazy register asset handler sprite-frame
2025-8-31 14:21:54-debug: lazy register asset handler scene
2025-8-31 14:21:54-debug: lazy register asset handler tiled-map
2025-8-31 14:21:54-debug: lazy register asset handler prefab
2025-8-31 14:21:54-debug: lazy register asset handler image
2025-8-31 14:21:54-debug: lazy register asset handler buffer
2025-8-31 14:21:54-debug: lazy register asset handler alpha-image
2025-8-31 14:21:54-debug: lazy register asset handler texture
2025-8-31 14:21:54-debug: lazy register asset handler sign-image
2025-8-31 14:21:54-debug: lazy register asset handler texture-cube
2025-8-31 14:21:54-debug: lazy register asset handler render-texture
2025-8-31 14:21:54-debug: lazy register asset handler texture-cube-face
2025-8-31 14:21:54-debug: lazy register asset handler json
2025-8-31 14:21:54-debug: lazy register asset handler rt-sprite-frame
2025-8-31 14:21:54-debug: lazy register asset handler erp-texture-cube
2025-8-31 14:21:54-debug: lazy register asset handler gltf
2025-8-31 14:21:54-debug: lazy register asset handler gltf-mesh
2025-8-31 14:21:54-debug: lazy register asset handler gltf-animation
2025-8-31 14:21:54-debug: lazy register asset handler gltf-scene
2025-8-31 14:21:54-debug: lazy register asset handler gltf-material
2025-8-31 14:21:54-debug: lazy register asset handler gltf-skeleton
2025-8-31 14:21:54-debug: lazy register asset handler gltf-embeded-image
2025-8-31 14:21:54-debug: lazy register asset handler fbx
2025-8-31 14:21:54-debug: lazy register asset handler material
2025-8-31 14:21:54-debug: lazy register asset handler physics-material
2025-8-31 14:21:54-debug: lazy register asset handler effect
2025-8-31 14:21:54-debug: lazy register asset handler animation-clip
2025-8-31 14:21:54-debug: lazy register asset handler animation-graph
2025-8-31 14:21:54-debug: lazy register asset handler animation-graph-variant
2025-8-31 14:21:54-debug: lazy register asset handler animation-mask
2025-8-31 14:21:54-debug: lazy register asset handler audio-clip
2025-8-31 14:21:54-debug: lazy register asset handler bitmap-font
2025-8-31 14:21:54-debug: lazy register asset handler ttf-font
2025-8-31 14:21:54-debug: lazy register asset handler effect-header
2025-8-31 14:21:54-debug: lazy register asset handler auto-atlas
2025-8-31 14:21:54-debug: lazy register asset handler sprite-atlas
2025-8-31 14:21:54-debug: lazy register asset handler render-pipeline
2025-8-31 14:21:54-debug: lazy register asset handler render-stage
2025-8-31 14:21:54-debug: lazy register asset handler render-flow
2025-8-31 14:21:54-debug: lazy register asset handler particle
2025-8-31 14:21:54-debug: lazy register asset handler label-atlas
2025-8-31 14:21:54-debug: lazy register asset handler instantiation-material
2025-8-31 14:21:54-debug: lazy register asset handler instantiation-animation
2025-8-31 14:21:54-debug: lazy register asset handler instantiation-mesh
2025-8-31 14:21:54-debug: lazy register asset handler instantiation-skeleton
2025-8-31 14:21:54-debug: lazy register asset handler video-clip
2025-8-31 14:21:54-debug: asset-db:worker-startup-database[assets] (5659ms)
2025-8-31 14:21:54-debug: asset-db:start-database (7046ms)
2025-8-31 14:21:54-debug: fix the bug of updateDefaultUserData
2025-8-31 14:21:54-debug: init worker message success
2025-8-31 14:21:54-debug: asset-db:ready (11048ms)
2025-8-31 14:21:54-debug: programming:execute-script (4ms)
2025-8-31 14:21:54-debug: [Build Memory track]: builder:worker-init start:193.38MB, end 207.03MB, increase: 13.65MB
2025-8-31 14:21:54-debug: builder:worker-init (272ms)
2025-8-31 14:24:16-debug: refresh db internal success
2025-8-31 14:24:16-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events\Bullet
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:24:16-debug: %cImport%c: D:\Workspace\Projects\Moolego\M2Game\Client\assets\resources\Game\emitter\events\Emitter
background: #aaff85; color: #000;
color: #000;
2025-8-31 14:24:16-debug: refresh db assets success
2025-8-31 14:24:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 14:24:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 14:24:16-debug: asset-db:refresh-all-database (107ms)
2025-8-31 14:24:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-31 14:24:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-31 14:24:50-debug: refresh db internal success
2025-8-31 14:24:50-debug: refresh db assets success
2025-8-31 14:24:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 14:24:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 14:24:50-debug: asset-db:refresh-all-database (97ms)
2025-8-31 14:24:50-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-31 14:24:50-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-31 14:25:13-debug: refresh db internal success
2025-8-31 14:25:13-debug: refresh db assets success
2025-8-31 14:25:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 14:25:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 14:25:13-debug: asset-db:refresh-all-database (92ms)
2025-8-31 14:25:13-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-31 14:25:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-31 14:25:50-debug: refresh db internal success
2025-8-31 14:25:50-debug: refresh db assets success
2025-8-31 14:25:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 14:25:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 14:25:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-31 14:25:50-debug: asset-db:refresh-all-database (108ms)
2025-8-31 14:25:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-31 14:26:18-debug: refresh db internal success
2025-8-31 14:26:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-31 14:26:18-debug: refresh db assets success
2025-8-31 14:26:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-31 14:26:18-debug: asset-db:refresh-all-database (91ms)
2025-8-31 14:26:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-31 14:26:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)

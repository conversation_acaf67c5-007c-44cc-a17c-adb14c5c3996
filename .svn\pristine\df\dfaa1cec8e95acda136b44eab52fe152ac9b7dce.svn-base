import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, AudioClip } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LevelDataEvent } from 'db://assets/scripts/leveldata/leveldata';
import { LevelDataEventTrigger, LevelDataEventTriggerType } from 'db://assets/scripts/leveldata/trigger/LevelDataEventTrigger';
import { LevelDataEventTriggerLog } from 'db://assets/scripts/leveldata/trigger/LevelDataEventTriggerLog';
import { LevelDataEventTriggerAudio } from 'db://assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio';
import { LevelDataEventTriggerWave } from 'db://assets/scripts/leveldata/trigger/LevelDataEventTriggerWave';
import { newTrigger } from 'db://assets/scripts/leveldata/trigger/newTrigger';
import { LevelDataEventCondtionType } from 'db://assets/scripts/leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionWave } from 'db://assets/scripts/leveldata/condition/LevelDataEventCondtionWave';

import { LevelEditorElemUI } from './LevelEditorElemUI';
import { LevelEditorWaveParam } from './LevelEditorWaveParam';
import { LevelEditorCondition } from './LevelEditorCondition';

@ccclass('LevelEditorEventTrigger')
export class LevelEditorEventTrigger{
    public _index = 0;
    public data : LevelDataEventTrigger = new LevelDataEventTriggerLog();

    @property({
        type:Enum(LevelDataEventTriggerType),
    })
    public get type(): LevelDataEventTriggerType{
        return this.data._type;
    }
    public set type(value: LevelDataEventTriggerType) {
        if (this.data._type != value) {
            this.data = newTrigger({_type: value});
        }
    }

    @property({
        type :CCString,
        visible () {
            return this.type == LevelDataEventTriggerType.Log ;
        }
    })
    public get message(): string {
        return (this.data as LevelDataEventTriggerLog).message;
    }
    public set message(value: string) {
        (this.data as LevelDataEventTriggerLog).message = value;
    }

    public _audio: AudioClip|null = null;
    @property({
        type :AudioClip,
        visible () {
            return this.type == LevelDataEventTriggerType.Audio;
        }
    })
    public get audio(): AudioClip|null {
        return this._audio;
    }
    public set audio(value: AudioClip|null) {
        this._audio = value;
        if (value) {
            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;
        } else {
            (this.data as LevelDataEventTriggerAudio).audioUUID = "";
        }
    }

    public _wave: Prefab|null = null;
    @property({
        type: Prefab,
        visible () {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get wave(): Prefab|null {
        return this._wave;
    }
    public set wave(value: Prefab|null) {
        this._wave = value;
        if (value) {
            (this.data as LevelDataEventTriggerWave).waveUUID = value.uuid;
        } else {
            (this.data as LevelDataEventTriggerWave).waveUUID = "";
        }
    }

    @property({
        type :CCInteger,
        visible () {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get planeID(): number {
        return (this.data as LevelDataEventTriggerWave).planeID;
    }
    public set planeID(value: number) {
        (this.data as LevelDataEventTriggerWave).planeID = value;
    }

    public _params: LevelEditorWaveParam[] = [];
    @property({
        type: [LevelEditorWaveParam],
        visible () {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get params(): LevelEditorWaveParam[] {
        return this._params;
    }
    public set params(value: LevelEditorWaveParam[]) {
        this._params = value;
        let waveTrigger = this.data as LevelDataEventTriggerWave;
        waveTrigger.params = new Map<string, number>();
        for (let p of this._params) {
            waveTrigger.params.set(p.name, p.value)
        }
    }
}

@ccclass('LevelEditorEventUI')
@executeInEditMode()
export class LevelEditorEventUI extends LevelEditorElemUI {
    @property([LevelEditorCondition])
    public conditions: LevelEditorCondition[] = [];
    @property([LevelEditorEventTrigger])
    public triggers: LevelEditorEventTrigger[] = [];

    public update(dt: number): void {
        for (let i = 0; i < this.conditions.length; i++) {
            const cond = this.conditions[i];
            cond._index = i;
            if (cond.type == LevelDataEventCondtionType.Wave 
                && (cond.data as LevelDataEventCondtionWave).targetElemID != "" 
                && cond._targetElem == null) {
                const elems = this.node.scene.getComponentsInChildren(LevelEditorElemUI);
                for (let elem of elems) {
                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {
                        cond._targetElem = elem;
                        break;
                    }
                }
            }
        }
    }

    public initByLevelData(data: LevelDataEvent) {
        super.initByLevelData(data)
        if (data.conditions) {
            for (let i = 0; i < data.conditions.length; i++) {
                const condition = new LevelEditorCondition();
                condition._index = i;
                condition.data = data.conditions[i];
                this.conditions.push(condition);
            }
        }
        if (data.triggers) {
            for (let i = 0; i < data.triggers.length; i++) {
                const trigger = new LevelEditorEventTrigger();
                trigger._index = i;
                trigger.data = data.triggers[i];
                this.triggers.push(trigger);
                if (trigger.data._type == LevelDataEventTriggerType.Audio) {
                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;
                    assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {
                        if (err) {
                            console.error("LevelEditorEventUI initByLevelData load audio err", err);
                            return;
                        }
                        trigger._audio = audio;
                    });
                }
                if (trigger.data._type == LevelDataEventTriggerType.Wave) {
                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;
                    let uuid = waveTrigger.waveUUID;
                    assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {
                        if (err) {
                            console.error("LevelEditorEventUI initByLevelData load wave prefab err", err);
                            return;
                        }
                        trigger._wave = prefab;
                    });
                    trigger._params = []
                    waveTrigger.params.forEach((v, k) => {
                        let param = new LevelEditorWaveParam();
                        param.name = k;
                        param.value = v;
                        trigger._params.push(param);
                    });
                }
            }
        }
    }

    public fillLevelData(data: LevelDataEvent) {
        super.fillLevelData(data)
        data.conditions = []
        this.conditions.forEach((cond) => {
            if (cond != null) {
                data.conditions.push(cond.data);
            }
        })
        this.triggers.forEach((trigger) => {
            if (trigger != null) {
                data.triggers.push(trigger.data);
            }
        })
    }
}
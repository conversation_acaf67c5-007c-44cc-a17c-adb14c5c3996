14:21:48.535 debug: 2025/8/31 14:21:48
14:21:48.535 debug: Project: D:\Workspace\Projects\Moolego\M2Game\Client
14:21:48.535 debug: Targets: editor,preview
14:21:48.537 debug: Incremental file seems great.
14:21:48.538 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
14:21:48.546 debug: Initializing target [Editor]
14:21:48.546 debug: Loading cache
14:21:48.561 debug: Loading cache costs 14.324800000000323ms.
14:21:48.561 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
14:21:48.562 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:21:48.562 debug: Initializing target [Preview]
14:21:48.563 debug: Loading cache
14:21:48.581 debug: Loading cache costs 18.66780000000017ms.
14:21:48.581 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
14:21:48.609 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tiled-map,tween,ui,video,websocket,webview,custom-pipeline
14:21:48.611 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "D:\\Workspace\\Projects\\Moolego\\M2Game\\Client\\assets"
  }
]
14:21:48.611 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/"
  }
}
14:21:48.612 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/"
  }
}
14:21:48.612 debug: Pulling asset-db.
14:21:48.648 debug: Fetch asset-db cost: 35.618499999999585ms.
14:21:48.649 debug: Build iteration starts.
Number of accumulated asset changes: 265
Feature changed: false
14:21:48.649 debug: Target(editor) build started.
14:21:48.652 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:07 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:05 GMT+0800 (中国标准时间)
14:21:48.652 debug: Inspect cce:/internal/x/cc
14:21:48.684 debug: transform url: 'cce:/internal/x/cc' costs: 31.50 ms
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
14:21:48.685 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
14:21:48.686 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Fri Jan 02 1970 00:22:22 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:06 GMT+0800 (中国标准时间)
14:21:48.686 debug: Inspect cce:/internal/x/prerequisite-imports
14:21:48.726 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 40.10 ms
14:21:48.727 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
14:21:48.728 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:21:48.728 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:21:48.729 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:21:48.729 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:21:48.729 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
14:21:48.730 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts.
14:21:48.730 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts.
14:21:48.730 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts.
14:21:48.731 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts.
14:21:48.731 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts.
14:21:48.731 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts.
14:21:48.731 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts.
14:21:48.731 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts.
14:21:48.732 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/utils.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/utils.ts.
14:21:48.732 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts.
14:21:48.732 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts.
14:21:48.732 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/BaseInfo.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/BaseInfo.ts.
14:21:48.733 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/DataEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/DataEvent.ts.
14:21:48.733 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/DataManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/DataManager.ts.
14:21:48.733 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/GameLevel.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/GameLevel.ts.
14:21:48.733 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/bag/Bag.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/bag/Bag.ts.
14:21:48.734 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/equip/Equip.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/equip/Equip.ts.
14:21:48.734 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/equip/EquipCombine.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/equip/EquipCombine.ts.
14:21:48.734 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/equip/EquipSlots.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/equip/EquipSlots.ts.
14:21:48.735 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/gm/GM.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/gm/GM.ts.
14:21:48.735 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameFunc.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameFunc.ts.
14:21:48.735 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:48.735 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Bullet.ts.
14:21:48.736 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts.
14:21:48.736 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Easing.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Easing.ts.
14:21:48.736 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts.
14:21:48.736 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventGroup.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventGroup.ts.
14:21:48.737 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts.
14:21:48.737 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/ObjectPool.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/ObjectPool.ts.
14:21:48.737 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts.
14:21:48.737 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterEventActions.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterEventActions.ts.
14:21:48.737 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/IEventAction.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/IEventAction.ts.
14:21:48.738 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/conditions/EmitterEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/conditions/EmitterEventConditions.ts.
14:21:48.738 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/conditions/IEventCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/conditions/IEventCondition.ts.
14:21:48.738 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts.
14:21:48.738 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCircleCollider.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCircleCollider.ts.
14:21:48.738 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts.
14:21:48.739 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FColliderManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FColliderManager.ts.
14:21:48.739 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FPolygonCollider.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FPolygonCollider.ts.
14:21:48.739 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/Intersection.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/Intersection.ts.
14:21:48.739 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/QuadTree.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/QuadTree.ts.
14:21:48.740 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:48.740 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts.
14:21:48.740 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts.
14:21:48.740 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BossData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BossData.ts.
14:21:48.741 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts.
14:21:48.741 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts.
14:21:48.741 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts.
14:21:48.741 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventConditionData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventConditionData.ts.
14:21:48.742 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/GameMapData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/GameMapData.ts.
14:21:48.742 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainData.ts.
14:21:48.742 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MapItemData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MapItemData.ts.
14:21:48.742 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/StageData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/StageData.ts.
14:21:48.742 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/TrackData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/TrackData.ts.
14:21:48.743 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/BulletData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/BulletData.ts.
14:21:48.743 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EmitterData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EmitterData.ts.
14:21:48.744 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EventActionType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EventActionType.ts.
14:21:48.744 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EventConditionType.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EventConditionType.ts.
14:21:48.744 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EventGroupData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/bullet/EventGroupData.ts.
14:21:48.744 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/level/LevelItem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/level/LevelItem.ts.
14:21:48.745 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/level/LevelItemEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/level/LevelItemEvent.ts.
14:21:48.745 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts.
14:21:48.745 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BossManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BossManager.ts.
14:21:48.745 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts.
14:21:48.745 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts.
14:21:48.745 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts.
14:21:48.746 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameResManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameResManager.ts.
14:21:48.746 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts.
14:21:48.746 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GlobalDataManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GlobalDataManager.ts.
14:21:48.746 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts.
14:21:48.746 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts.
14:21:48.746 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts.
14:21:48.747 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts.
14:21:48.747 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts.
14:21:48.747 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/StageManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/StageManager.ts.
14:21:48.747 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts.
14:21:48.747 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/DefaultMoveModifier.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/DefaultMoveModifier.ts.
14:21:48.747 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/IMovable.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/IMovable.ts.
14:21:48.748 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/Movable.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/Movable.ts.
14:21:48.748 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts.
14:21:48.748 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts.
14:21:48.748 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts.
14:21:48.748 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts.
14:21:48.749 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BlastComp.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BlastComp.ts.
14:21:48.749 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts.
14:21:48.749 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts.
14:21:48.749 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts.
14:21:48.749 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts.
14:21:48.750 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts.
14:21:48.750 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts.
14:21:48.750 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts.
14:21:48.750 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts.
14:21:48.750 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts.
14:21:48.750 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts.
14:21:48.751 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimCircleScreen.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimCircleScreen.ts.
14:21:48.751 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimSingleLineScreen.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimSingleLineScreen.ts.
14:21:48.751 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts.
14:21:48.751 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts.
14:21:48.751 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleZoomScreen.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleZoomScreen.ts.
14:21:48.752 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/LoftScreen.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/LoftScreen.ts.
14:21:48.752 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts.
14:21:48.752 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts.
14:21:48.752 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/EnemyEffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/EnemyEffectLayer.ts.
14:21:48.753 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts.
14:21:48.753 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelBaseUI.ts.
14:21:48.754 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelCondition.ts.
14:21:48.754 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelElemUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelElemUI.ts.
14:21:48.754 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelEventUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelEventUI.ts.
14:21:48.754 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelLayerUI.ts.
14:21:48.754 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelWaveUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/LevelWaveUI.ts.
14:21:48.755 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts.
14:21:48.755 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts.
14:21:48.755 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts.
14:21:48.755 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts.
14:21:48.755 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossHurt.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossHurt.ts.
14:21:48.756 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts.
14:21:48.756 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts.
14:21:48.756 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAnim.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAnim.ts.
14:21:48.756 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts.
14:21:48.756 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts.
14:21:48.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts.
14:21:48.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts.
14:21:48.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts.
14:21:48.757 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyComponent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyComponent.ts.
14:21:48.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts.
14:21:48.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts.
14:21:48.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts.
14:21:48.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts.
14:21:48.758 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts.
14:21:48.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts.
14:21:48.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts.
14:21:48.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Helper.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Helper.ts.
14:21:48.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:48.759 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/wave/Wave.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/wave/Wave.ts.
14:21:48.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Anim.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Anim.ts.
14:21:48.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Background.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Background.ts.
14:21:48.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Enemy.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Enemy.ts.
14:21:48.760 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/EnemyBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/EnemyBullet.ts.
14:21:48.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GameOver.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GameOver.ts.
14:21:48.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GamePersistNode.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GamePersistNode.ts.
14:21:48.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Global.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Global.ts.
14:21:48.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Goods.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Goods.ts.
14:21:48.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/MainGame.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/MainGame.ts.
14:21:48.761 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Menu.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Menu.ts.
14:21:48.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Player.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Player.ts.
14:21:48.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/PlayerBullet.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/PlayerBullet.ts.
14:21:48.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/AnimFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/AnimFactory.ts.
14:21:48.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyBulletFactory.ts.
14:21:48.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyFactory.ts.
14:21:48.762 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GameFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GameFactory.ts.
14:21:50.926 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GoodsFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GoodsFactory.ts.
14:21:50.926 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/PlayerBulletFactory.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/PlayerBulletFactory.ts.
14:21:50.926 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/EmitterArcGizmo.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/EmitterArcGizmo.ts.
14:21:50.926 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoDrawer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoDrawer.ts.
14:21:50.926 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoManager.ts.
14:21:50.926 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoUtils.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoUtils.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/index.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/index.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/Bootstrap.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/Bootstrap.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/WorldInitializeData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/WorldInitializeData.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Entity.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Entity.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EntityContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EntityContainer.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventAction.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventAction.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventCondition.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventGroup.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventGroup.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Messaging.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Messaging.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Object.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Object.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/System.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/System.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/SystemContainer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/SystemContainer.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/TypeID.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/TypeID.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/World.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/World.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/Level.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/Level.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelEventGroup.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelEventGroup.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelSystem.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/Background.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/Background.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/BackgroundLayer.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/BackgroundLayer.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundAction.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundAction.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundSpeedAction.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundSpeedAction.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LevelTimeEventCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LevelTimeEventCondition.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LogEventAction.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LogEventAction.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/player/PlayerSystem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/player/PlayerSystem.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/Weapon.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/Weapon.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/WeaponSlot.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/WeaponSlot.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts.
14:21:50.927 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MyApp.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MyApp.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Utils/Logger.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Utils/Logger.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/ResManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/ResManager.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/event/EventManager.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/event/EventManager.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts.
14:21:50.928 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/DevLoginUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/DevLoginUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/LoadingUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/LoadingUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/UIMgr.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/UIMgr.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/TopBlockInputUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/TopBlockInputUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelect.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelect.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelectItem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelectItem.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/ButtonPlus.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/ButtonPlus.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/DragButton.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/DragButton.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/dropdown/DropDown.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/dropdown/DropDown.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/List.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/List.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/ListItem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/ListItem.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/gm/GmButtonUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/gm/GmButtonUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/gm/GmUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/gm/GmUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BattleUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BattleUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BottomUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BottomUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BuidingUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BuidingUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BuildingInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BuildingInfoUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/MainEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/MainEvent.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/MapModeUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/MapModeUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/PlaneShowUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/PlaneShowUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/PopupUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/PopupUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/ShopUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/ShopUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/SkyIslandUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/SkyIslandUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TalentUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TalentUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TopUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TopUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/WheelSpinnerUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/WheelSpinnerUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/dialogue/DialogueUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/dialogue/DialogueUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/fight/RogueSelectIcon.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/fight/RogueSelectIcon.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/fight/RogueUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/fight/RogueUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendAddUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendAddUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendCellUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendCellUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendListUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendListUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendStrangerUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendStrangerUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/friend/FriendUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/mail/MailCellUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/mail/MailCellUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/mail/MailUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/mail/MailUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/pk/PKRewardIcon.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/pk/PKRewardIcon.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/pk/PKUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/pk/PKUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneCombineResultUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneCombineResultUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEquipInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEquipInfoUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEvent.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEvent.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneTypes.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneTypes.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneUI.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneUI.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagItem.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagItem.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/SortTypeDropdown.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/SortTypeDropdown.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/Tabs.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/Tabs.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/CombineDisplay.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/CombineDisplay.ts.
14:21:50.929 debug: Resolve file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts from cce:/internal/x/prerequisite-imports as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts.
14:21:50.929 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.929 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Sat Aug 30 2025 21:32:47 GMT+0800 (中国标准时间), Current mtime: Sun Aug 31 2025 14:21:42 GMT+0800 (中国标准时间)
14:21:50.929 debug: Inspect cce:/internal/code-quality/cr.mjs
14:21:50.929 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 7.20 ms
14:21:50.930 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:21:50.930 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:21:50.930 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
14:21:50.930 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
14:21:50.930 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:21:50.930 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
14:21:50.930 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
14:21:50.930 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.930 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:21:50.930 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:21:50.930 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
14:21:50.930 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
14:21:50.933 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
14:21:50.933 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
14:21:50.933 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
14:21:50.933 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
14:21:50.933 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
14:21:50.934 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ../../scripts/leveldata/leveldata from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts.
14:21:50.934 debug: Resolve ./LevelEditorLayerUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts.
14:21:50.934 debug: Resolve ./utils from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/utils.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./condition/newCondition from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts.
14:21:50.934 debug: Resolve ./trigger/newTrigger from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventCondtion from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
14:21:50.934 debug: Resolve ./LevelDataEventCondtionDelayDistance from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts.
14:21:50.934 debug: Resolve ./LevelDataEventCondtionDelayTime from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts.
14:21:50.934 debug: Resolve ./LevelDataEventCondtionWave from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventCondtion from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventCondtion from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventCondtion from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventTrigger from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts.
14:21:50.934 debug: Resolve ./LevelDataEventTriggerAudio from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts.
14:21:50.934 debug: Resolve ./LevelDataEventTriggerLog from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts.
14:21:50.934 debug: Resolve ./LevelDataEventTriggerWave from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventTrigger from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventTrigger from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve ./LevelDataEventTrigger from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts.
14:21:50.934 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve db://assets/scripts/leveldata/leveldata from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts.
14:21:50.934 debug: Resolve ./utils from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/utils.ts.
14:21:50.934 debug: Resolve ./LevelEditorWaveUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts.
14:21:50.934 debug: Resolve ./LevelEditorEventUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/utils.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/utils.ts as cce:/internal/x/cc.
14:21:50.934 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/utils.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve ./LevelEditorElemUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts.
14:21:50.938 debug: Resolve ./LevelEditorWaveParam from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts.
14:21:50.938 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve ./LevelEditorBaseUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts.
14:21:50.938 debug: Resolve ./LevelEditorLayerUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as cce:/internal/x/cc.
14:21:50.938 debug: Resolve db://assets/scripts/leveldata/trigger/LevelDataEventTrigger from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts.
14:21:50.938 debug: Resolve db://assets/scripts/leveldata/trigger/LevelDataEventTriggerLog from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts.
14:21:50.938 debug: Resolve db://assets/scripts/leveldata/trigger/newTrigger from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts.
14:21:50.938 debug: Resolve db://assets/scripts/leveldata/condition/LevelDataEventCondtion from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
14:21:50.938 debug: Resolve ./LevelEditorElemUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts.
14:21:50.938 debug: Resolve ./LevelEditorWaveParam from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts.
14:21:50.939 debug: Resolve ./LevelEditorCondition from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts.
14:21:50.939 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve db://assets/scripts/leveldata/condition/LevelDataEventCondtion from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts.
14:21:50.939 debug: Resolve db://assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts.
14:21:50.939 debug: Resolve db://assets/scripts/leveldata/condition/newCondition from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts.
14:21:50.939 debug: Resolve ./LevelEditorElemUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorCondition.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts.
14:21:50.939 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve ../../scripts/leveldata/leveldata from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts.
14:21:50.939 debug: Resolve ./LevelEditorBaseUI from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorUI.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve ./Game/GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.939 debug: Resolve ./Game/collider-system/FCollider from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ColliderTest.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts.
14:21:50.939 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve ../core/base/SingletonBase from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts.
14:21:50.939 debug: Resolve ./manager/GameDataManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts.
14:21:50.939 debug: Resolve ./manager/MainPlaneManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts.
14:21:50.939 debug: Resolve ./manager/BattleManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts.
14:21:50.939 debug: Resolve ./manager/BossManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BossManager.ts.
14:21:50.939 debug: Resolve ./manager/BulletManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts.
14:21:50.939 debug: Resolve ./manager/EnemyManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts.
14:21:50.939 debug: Resolve ./manager/GameRuleManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts.
14:21:50.939 debug: Resolve ./manager/HurtEffectManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts.
14:21:50.939 debug: Resolve ./manager/PlaneManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts.
14:21:50.939 debug: Resolve ./manager/StageManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/StageManager.ts.
14:21:50.939 debug: Resolve ./manager/GameResManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameResManager.ts.
14:21:50.939 debug: Resolve ./manager/SceneManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts.
14:21:50.939 debug: Resolve ./manager/WaveManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts.
14:21:50.939 debug: Resolve ./manager/PrefabManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts.
14:21:50.939 debug: Resolve ./collider-system/FColliderManager from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FColliderManager.ts.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve ../../core/base/SingletonBase from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts.
14:21:50.939 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as cce:/internal/x/cc.
14:21:50.939 debug: Resolve ../data/MainData from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainData.ts.
14:21:50.939 debug: Resolve ../../core/base/SingletonBase from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts.
14:21:50.939 debug: Resolve ../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.939 debug: Resolve ../ui/plane/mainPlane/MainPlane from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts.
14:21:50.939 debug: Resolve ../../MyApp from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MyApp.ts.
14:21:50.939 debug: Resolve ../ui/layer/BattleLayer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts.
14:21:50.941 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as cce:/internal/x/cc.
14:21:50.941 debug: Resolve ../const/GameResourceList from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts.
14:21:50.941 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainData.ts as cce:/internal/x/cc.
14:21:50.941 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve ../Plane from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts.
14:21:50.942 debug: Resolve ../../../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.942 debug: Resolve ../../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.942 debug: Resolve ../../../const/GameEnum from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts.
14:21:50.942 debug: Resolve ../../layer/BattleLayer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts.
14:21:50.942 debug: Resolve ./FireShells from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts.
14:21:50.942 debug: Resolve ../../bullet/Bullet from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts.
14:21:50.942 debug: Resolve ../enemy/EnemyEntity from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts.
14:21:50.942 debug: Resolve ../boss/BossUnit from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts.
14:21:50.942 debug: Resolve ../../layer/EffectLayer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts.
14:21:50.942 debug: Resolve ../../../collider-system/FBoxCollider from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts.
14:21:50.942 debug: Resolve ../../../collider-system/FCollider from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts.
14:21:50.942 debug: Resolve db://assets/scripts/MyApp from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MyApp.ts.
14:21:50.942 debug: Resolve ../../../const/GameResourceList from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve ../base/Entity from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve ../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve ../../../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.942 debug: Resolve ./MainPlane from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts.
14:21:50.942 debug: Resolve ../../base/Entity from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts.
14:21:50.942 debug: Resolve ../../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.942 debug: Resolve ../../bulletDanmu/CircleScreen from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts.
14:21:50.942 debug: Resolve ../../../const/GameEnum from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve ./BaseScreen from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts.
14:21:50.942 debug: Resolve ../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.942 debug: Resolve ../plane/boss/BossEntity from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve ../base/BaseComp from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts.
14:21:50.942 debug: Resolve ../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.942 debug: Resolve ../../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.942 debug: Resolve ../plane/enemy/EnemyBase from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts.
14:21:50.942 debug: Resolve ../plane/boss/BossBase from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts.
14:21:50.942 debug: Resolve ../layer/BattleLayer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts as cce:/internal/x/cc.
14:21:50.942 debug: Resolve ../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.942 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.942 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as cce:/internal/x/cc.
14:21:50.943 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as cce:/internal/x/cc.
14:21:50.943 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as cce:/internal/x/cc.
14:21:50.943 debug: Resolve ./EnemyEntity from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts.
14:21:50.943 debug: Resolve ../../../const/GameEnum from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts.
14:21:50.943 debug: Resolve ./EnemyEffectComp from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts.
14:21:50.944 debug: Resolve ./EnemyAttrComponent from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts.
14:21:50.944 debug: Resolve ../../../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.944 debug: Resolve ../../layer/BattleLayer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts.
14:21:50.944 debug: Resolve ../../../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.944 debug: Resolve ../../bullet/Bullet from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts.
14:21:50.944 debug: Resolve ../../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.944 debug: Resolve ../mainPlane/MainPlane from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts.
14:21:50.944 debug: Resolve ../../../collider-system/FCollider from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts.
14:21:50.944 debug: Resolve ../../../collider-system/FBoxCollider from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts.
14:21:50.944 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve ../../base/Entity from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve ../../../data/EnemyData from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts.
14:21:50.944 debug: Resolve ../../../const/GameEnum from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts.
14:21:50.944 debug: Resolve ../../../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.944 debug: Resolve ./EnemyAttrDoctorCom from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts.
14:21:50.944 debug: Resolve ./EnemyAttrShieldCom from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts.
14:21:50.944 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve ../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.944 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve ./EnemyAttrBaseCom from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts.
14:21:50.944 debug: Resolve ../../../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.944 debug: Resolve ../../../const/GameEnum from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts.
14:21:50.944 debug: Resolve ../../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.944 debug: Resolve ../../../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as cce:/internal/x/cc.
14:21:50.944 debug: Resolve ./EnemyAttrBaseCom from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts.
14:21:50.944 debug: Resolve ../../../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.944 debug: Resolve ../../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.944 debug: Resolve ../../layer/BattleLayer from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts.
14:21:50.944 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.944 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as cce:/internal/x/cc.
14:21:50.945 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as cce:/internal/x/cc.
14:21:50.945 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as cce:/internal/x/cc.
14:21:50.945 debug: Resolve ../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.945 debug: Resolve ../../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.945 debug: Resolve ../base/Entity from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts.
14:21:50.945 debug: Resolve ../base/AngleComp from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts.
14:21:50.945 debug: Resolve ./BulletFly from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts.
14:21:50.945 debug: Resolve ../../GameFunc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameFunc.ts.
14:21:50.945 debug: Resolve ../plane/mainPlane/MainPlane from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts.
14:21:50.945 debug: Resolve ../../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.945 debug: Resolve ./CircleZoomFly from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts.
14:21:50.945 debug: Resolve ../../collider-system/FBoxCollider from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts.
14:21:50.945 debug: Resolve ../../collider-system/FCollider from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts.
14:21:50.945 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.945 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts as cce:/internal/x/cc.
14:21:50.945 debug: Resolve ../../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.945 debug: Resolve ./BaseComp from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts.
14:21:50.945 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as cce:/internal/code-quality/cr.mjs.
14:21:50.945 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as cce:/internal/x/cc.
14:21:50.945 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as cce:/internal/x/cc.
14:21:50.945 debug: Resolve cc from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as cce:/internal/x/cc.
14:21:50.945 debug: Resolve ../../const/GameConst from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts.
14:21:50.945 debug: Resolve ../../GameIns from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts.
14:21:50.945 debug: Resolve ../../utils/Tools from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts.
14:21:50.945 debug: Resolve ../base/BaseComp from file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts as file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts.

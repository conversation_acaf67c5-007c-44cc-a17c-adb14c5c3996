import { _decorator, Label } from 'cc';
import { BaseUI, UILayer, UIOpt } from '../UIMgr';

const { ccclass, property } = _decorator;

@ccclass('PopupUI')
export class PopupUI extends BaseUI {

    @property(Label)
    label: Label | null = null;

    public static getUrl(): string { return "ui/main/PopupUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: true }
    }

    protected onLoad(): void {
    }

    async onShow(message: string): Promise<void> {
         this.label!.string = message;
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }

}

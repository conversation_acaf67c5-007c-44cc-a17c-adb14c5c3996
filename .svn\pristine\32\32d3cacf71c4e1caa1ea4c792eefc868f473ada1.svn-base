import { IEventCondition } from "./IEventCondition";
import { EventGroupContext } from "../EventGroup";
import { EventConditionData, eCompareOp, eConditionOp } from "../../data/bullet/EventGroupData";

export class EmitterEventConditionBase implements IEventCondition {
    readonly data: EventConditionData;

    constructor(data: EventConditionData) {
        this.data = data;
    }

    public evaluate(context: EventGroupContext): boolean {
        return true;
    }
}

// 发射器是否启用
export class EmitterEventCondition_Active extends EmitterEventConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        // Custom evaluation logic for active condition
        return context.emitter.isActive.value;
    }
}

// 发射器初始延迟时间
export class EmitterEventCondition_InitialDelay extends EmitterEventConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter.initialDelay.value === this.data.targetValue;
            case eCompareOp.NotEqual:
                return context.emitter.initialDelay.value !== this.data.targetValue;
            default:
                return false;
        }
    }
}

export class EmitterEventCondition_Prewarm extends EmitterEventConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter.isPreWarm.value === (this.data.targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter.isPreWarm.value !== (this.data.targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

// 发射器持续时间
export class EmitterEventCondition_Duration extends EmitterEventConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter.emitDuration.value === this.data.targetValue;
            case eCompareOp.NotEqual:
                return context.emitter.emitDuration.value !== this.data.targetValue;
            default:
                return false;
        }
    }
}

// 发射器已运行时间
export class EmitterEventCondition_ElapsedTime extends EmitterEventConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter.totalElapsedTime.value === this.data.targetValue;
            case eCompareOp.NotEqual:
                return context.emitter.totalElapsedTime.value !== this.data.targetValue;
            default:
                return false;
        }
    }
}
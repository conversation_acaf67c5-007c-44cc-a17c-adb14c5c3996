import { _decorator, Component, instantiate, Node, tween, UITransform, v3, Vec2, Vec3 } from 'cc';
import BossBase from './BossBase';
import AttackPoint from '../../base/AttackPoint';
import BossUnit from './BossUnit';
import TrackComponent from '../../base/TrackComponent';
import PfFrameAnim from '../../base/PfFrameAnim';
import GameEnum from '../../../const/GameEnum';
import { Tools } from '../../../utils/Tools';
import { GameIns } from '../../../GameIns';
import { TrackGroup } from '../../../data/EnemyWave';
import EffectLayer from '../../layer/EffectLayer';
import { GameConst } from '../../../const/GameConst';
import { BossData } from '../../../data/BossData';
const { ccclass, property } = _decorator;

@ccclass("BossEntity")
export default class BossEntity extends BossBase {
    _datas: BossData[] = [];
    _data: BossData | null = null;
    _atkPointsPool: AttackPoint[] = [];
    _attacks: number[] = [];
    _uiNode: Node | null = null;
    _topAnimNode: Node | null = null;
    _idleName: string = "idle1";
    _units: Map<number, BossUnit> = new Map();
    _colUnitsIndex: number = 0;
    _colUnits: BossUnit[] = [];
    _deadUnitIds: number[] = [];
    _atkUnits: BossUnit[] = [];
    _atkUnitSounds: Map<number, string> = new Map();
    _atkUnitSoundIds: Map<number, number> = new Map();
    _formIndex: number = -1;
    _formNum: number = 0;
    _nextForm: boolean = false;
    _prePosX: number = 0;
    _prePosY: number = 0;
    _posX: number = 0;
    _posY: number = 0;
    _trackCom: TrackComponent | null = null;
    _curTrackType: number = -1;
    _curTrack: any = null;
    _trackTime: number = 0;
    _trackOffX: number = 0;
    _trackOffY: number = 0;
    _moveToX: number = 0;
    _moveToY: number = 0;
    _moveSpeed: number = 0;
    _bArriveDes: boolean = false;
    _transFormMove: boolean = false;
    _nextWayPointTime: number = 0;
    _nextWayPointX: number = 0;
    _nextWayPointY: number = 0;
    _nextWayPointInterval: number = 0;
    _nextWaySpeed: number = 0;
    _shootAble: boolean = true;
    _atkActions: any[] = [];
    _nextAttackInterval: number = 0;
    _nextAttackTime: number = 0;
    _bOrderAttack: boolean = false;
    _orderIndex: number = 0;
    _attackID: number = 0;
    _atkPointDatas: any[] = [];
    _attackPoints: AttackPoint[] = [];
    _orderAtkArr: number[] = [];
    _action: number = -1;
    _bDamageable: boolean = false;
    _bAttackMove: boolean = false;
    _bFirstWayPoint: boolean = false;
    transformBattle: boolean = true;
    _bRemoveable: boolean = false;
    _shadow: any = null;
    wingmanPlanes: any[] = [];
    _cloakeAnim: PfFrameAnim | null = null;


    /**
     * 初始化 Boss 数据
     * @param datas Boss 数据数组
     */
    init(datas: BossData[]) {
        this._datas = datas;
        this._formNum = this._datas.length;
        this._bFirstWayPoint = true;
        this._initUI();
        this._initProperty();
        this._initTrack();
        this.setFormIndex(0);
        this.active = true;
    }

    /**
     * 设置影子
     * @param shadow 影子对象
     */
    setShadow(shadow: any) {
        this._shadow = shadow;
    }

    /**
     * 设置形态索引
     * @param index 形态索引
     */
    setFormIndex(index: number) {
        if (this._formIndex !== index) {
            this._formIndex = index;
            this._bOrderAttack = true;
            this._orderIndex = 0;
            this._data = this._datas[this._formIndex];
            this._idleName = `idle${this._formIndex + 1}`;
            this._collideAtk = this._data.collideAttack;

            if (index === 0) {
                this._initUnits();
                this.setAction(GameEnum.BossAction.Appear);
            }
            //  else {
            //     this._units.forEach((unit) => {
            //         unit.init(GameIns.bossManager.getUnitData(unit.id), this);
            //         unit.setCollideAtk(this._data.collideAttack);
            //         unit.setPropertyRate(this.propertyRate);
            //     });
            //     this.setAction(BossAction.Transform);
            // }

            this._orderAtkArr = [];
            for (let i = 0; i < this._data.attackActions.length; i++) {
                this._orderAtkArr.push(i);
            }

            this._atkPointDatas = [];
            for (const point of this._data.attackPoints) {
                const data = [point.bAvailable, point];
                this._atkPointDatas.push(data);
            }

            this._atkActions = [...this._data.attackActions];
            this._colUnitsIndex = 0;
            this._colUnits = [];
            this._deadUnitIds = [];
            this.unitArr = [];
        }
    }

    /**
     * 进入下一形态
     */
    enterNextForm() {
        if (this._formIndex < this._datas.length - 1) {
            this._formIndex++;
            this.setFormIndex(this._formIndex);
        }
    }

    /**
     * 设置 Boss 的行为
     * @param action 行为类型
     */
    setAction(action: number) {
        if (this._action !== action) {
            this._action = action;

            let BossAction = GameEnum.BossAction;
            switch (this._action) {
                case BossAction.Normal:
                    this._playSkel(this._idleName, true, () => {});
                    this.setDamangeable(true);
                    break;

                case BossAction.Appear:
                    this._playSkel(`enter${this._formIndex + 1}`, true, () => {});
                    this.setDamangeable(false);
                    this._startAppearTrack();
                    break;

                case BossAction.Transform:
                    this._playSkel(`ready${this._formIndex + 1}`, false, () => {
                        this.transformBattle && this.transformEnd();
                    });
                    this.setDamangeable(false);
                    break;

                case BossAction.AttackPrepare:
                    this._checkAtkAnim() || this.scheduleOnce(() => {
                        this.setAction(BossAction.AttackIng);
                    });
                    this.setDamangeable(true);
                    break;

                case BossAction.AttackIng:
                case BossAction.AttackOver:
                    this.setDamangeable(true);
                    break;

                case BossAction.Blast:
                    this.setDamangeable(false);
                    break;

                default:
                    this.setDamangeable(true);
            }
        }
    }

    //     // ...前面的代码...

    /**
     * 检查并生成 Boss 的僚机
     */
    checkBossWingman() {
        // if (this._data.enemyId > 0 && this._data.enemyPos.length > 0) {
        //     for (let i = 0; i < this._data.enemyPos.length; i++) {
        //         const wingman = EnemyManager.EnemyMgr.addPlane(this._data.enemyId);
        //         wingman.setExp(0);
        //         wingman.setScaleType(EnemyScale.None);
        //         wingman.attack = this._data.attack;
        //         wingman.node.position = this._data.enemyPos[i];

        //         const trackGroup = i % 2 === 0 ? this._data.enemyTrackGroup1 : this._data.enemyTrackGroup2;
        //         wingman.initTrack(trackGroup, [-1, 0, 0], wingman.node.position.x, wingman.node.position.y);
        //         wingman.startBattle();
        //         wingman.collideAble = false;

        //         this.wingmanPlanes.push(wingman);
        //     }
        // }
    }

    /**
     * 开始战斗
     */
    startBattle() {
        this.active = true;
        this._startNormalTrack();
        this.setAction(GameEnum.BossAction.Normal);
        this._checkNextCollideUnits();
        this.checkBossWingman();
    }

    /**
     * 检查下一个碰撞单元
     */
    _checkNextCollideUnits(): boolean {
        let hasAliveUnits = false;

        for (const unitId of this._data!.unitsOrder) {
            if (!Tools.arrContain(this._deadUnitIds, unitId)) {
                hasAliveUnits = true;
                break;
            }
        }

        if (hasAliveUnits) {
            if (this._colUnits.length === 0) {
                for (const unitId of this._data!.unitsOrder) {
                    const unit = this._units.get(unitId)!;
                    unit.setCollideAble(true);
                    this._colUnits.push(unit);
                    this.unitArr.push(unit);
                }
            }
        } else {
            this._colUnitsIndex++;
            if (this._colUnitsIndex >= this._data!.unitsOrder.length) {
                return false;
            }

            const nextUnits = this._data!.unitsOrder[this._colUnitsIndex];
            this._colUnits = [];
            this.unitArr = [];

            const unit = this._units.get(nextUnits)!;
            unit.setCollideAble(true);
            this._colUnits.push(unit);
            this.unitArr.push(unit);
        }

        return true;
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        if (this.active && !this.isDead && !this._nextForm) {
            this.wingmanPlanes.forEach((wingman) => {
                wingman.node.angle += this._data!.enemyRotate * deltaTime;
            });

            let BossAction = GameEnum.BossAction;
            switch (this._action) {
                case BossAction.Normal:
                    this._processNextWayPoint(deltaTime);
                    this._updateMove(deltaTime);
                    this._processNextAttack(deltaTime);
                    break;

                case BossAction.Appear:
                    this._updateMove(deltaTime);
                    if (this._bArriveDes) {
                        this.setAction(BossAction.Transform);
                    }
                    break;

                case BossAction.Transform:
                    if (this._transFormMove) {
                        this._updateMove(deltaTime);
                    }
                    break;

                case BossAction.AttackPrepare:
                    this._processNextWayPoint(deltaTime);
                    if (this._bAttackMove) {
                        this._updateMove(deltaTime);
                    }
                    break;

                case BossAction.AttackIng:
                    this._processNextWayPoint(deltaTime);
                    if (this._bAttackMove) {
                        this._updateMove(deltaTime);
                    }
                    this._udpateShoot(deltaTime);
                    break;

                case BossAction.AttackOver:
                    this._processNextWayPoint(deltaTime);
                    if (this._bAttackMove) {
                        this._updateMove(deltaTime);
                    }
                    this.setAction(BossAction.Normal);
                    break;

                case BossAction.Blast:
                    break;
            }

            this._units.forEach((unit) => {
                unit.updateGameLogic(deltaTime);
            });
        }
    }

    /**
     * Boss 死亡逻辑
     */
    toDie() {
        if (!this.isDead) {
            this.isDead = true;
            this.setAction(GameEnum.BossAction.Blast);
            this._playDieAnim();

            if (this._data!.id >= 250 && this._data!.id < 300) {
                let allBossesDead = true;
                for (const boss of GameIns.bossManager.bosses) {
                    if (boss !== this && !boss.isDead) {
                        allBossesDead = false;
                        break;
                    }
                }

                if (allBossesDead) {
                    this.checkLoot();
                }
            } else if (this._data!.nextBoss.length > 0 && GameIns.battleManager.isGameType(GameEnum.GameType.Boss)) {
                // Do nothing, next boss will be handled
            } else {
                this.checkLoot();
            }

            this.onDie();
        }
    }

    /**
     * 单元销毁逻辑
     * @param unit 被销毁的单元
     */
    unitDestroyed(unit: any) {
        this._deadUnitIds.push(unit.unitId);
        Tools.arrRemove(this._colUnits, unit);

        let allUnitsDead = true;
        this._units.forEach((unit) => {
            if (!unit.isDead) {
                allUnitsDead = false;
            }
        });

        for (let i = 0; i < this._atkActions.length; i++) {
            let hasActivePoints = false;
            for (const pointId of this._atkActions[i].atkPointId) {
                const pointData = this._atkPointDatas[pointId];
                if (pointData[0]) {
                    if (unit.unitId === pointData[1].atkUnitId) {
                        pointData[0] = false;
                    } else {
                        hasActivePoints = true;
                    }
                }
            }

            if (!hasActivePoints) {
                this._atkActions.splice(i, 1);
                i--;
            }
        }

        for (let i = 0; i < this._attackPoints.length; i++) {
            if (this._attackPoints[i].getAtkUnitId() === unit.unitId) {
                this._attackPoints.splice(i, 1);
                i--;
            }
        }

        const soundKey = this._atkUnitSounds.get(unit.unitId);
        if (soundKey) {
            this._atkUnitSounds.delete(unit.unitId);
        }

        const soundId = this._atkUnitSoundIds.get(unit.unitId);
        if (soundId !== null) {
            this._atkUnitSoundIds.delete(unit.unitId);
            // GameIns.default.audioManager.stopEffect(soundId);
        }

        this.setBodySkin(this._deadUnitIds.length);

        if (allUnitsDead) {
            this._formIndex++;
            if (this._checkNextForm()) {
                this._nextForm = true;
            } else {
                this._formIndex--;
                this.toDie();
            }
        } else {
            this._checkNextCollideUnits();
        }
    }

    /**
     * 单元销毁动画结束
     * @param unit 被销毁的单元
     */
    unitDestroyAnimEnd(unit: any) {
        if (this._nextForm) {
            this._nextForm = false;
            this.setFormIndex(this._formIndex);
        }
    }

    /**
     * 检查是否有下一形态
     */
    _checkNextForm(): boolean {
        return this._formIndex < this._datas.length;
    }

    /**
     * 初始化属性
     */
    _initProperty() {
        for (let i = 0; i < this._datas.length; i++) {
            const data = this._datas[i];
            this._attacks.push(data.attack);
        }
    }

    /**
     * 设置属性倍率
     * @param rates 属性倍率数组
     * @param updateHp 是否更新血量
     */
    setPropertyRate(rates: number[], updateHp: boolean = false) {
        super.setPropertyRate(rates);

        this.m_totalHp = 0;
        this.m_curHp = 0;

        if (rates.length > 1) {
            for (let i = 0; i < this._attacks.length; i++) {
                this._attacks[i] *= rates[1];
            }
        }

        if (rates.length > 2) {
            this._collideAtk *= rates[2];
        }

        this._units.forEach((unit) => {
            unit.setPropertyRate(this.propertyRate);
            this.m_totalHp += unit.maxHp;
            this.m_curHp += unit.maxHp;
        });

        if (this._data!.nextBoss.length > 0) {
            for (let i = 0; i < this._data!.nextBoss.length; i++) {
                const bossData = GameIns.bossManager.getBossDatas(this._data!.nextBoss[i])!;
                if (bossData.length > 0) {
                    for (const unitId of bossData[0].units) {
                        const unitData = GameIns.bossManager.getUnitData(unitId);
                        if (unitData) {
                            this.m_totalHp += unitData.hp * (this.propertyRate[0] || 1);
                            this.m_curHp += unitData.hp * (this.propertyRate[0] || 1);
                        }
                    }
                }
            }
        }

        if (!updateHp) {
            // BossBattleManager.setCurHp(this.m_curHp);
            // BossBattleManager.setTotalHp(this.m_totalHp);
        }
    }

    /**
     * 获取形态数量
     */
    get formNum(): number {
        return this._formNum;
    }

    /**
     * 获取当前形态索引
     */
    get formIndex(): number {
        return this._formIndex;
    }

    /**
     * 获取当前形态的攻击力
     */
    get attack(): number {
        return this._attacks[this._formIndex];
    }

    /**
     * 获取碰撞攻击力
     */
    getColliderAtk(): number {
        return this._collideAtk;
    }

    /**
     * 获取 X 坐标
     */
    get posX(): number {
        return this._posX;
    }

    /**
     * 获取 Y 坐标
     */
    get posY(): number {
        return this._posY;
    }

    /**
     * 是否可被攻击
     */
    isDamageable(): boolean {
        return this._bDamageable;
    }

    /**
     * 是否可移除
     */
    get removeAble(): boolean {
        return this._bRemoveable;
    }

    set removeAble(value: boolean) {
        this._bRemoveable = value;
    }

    /**
     * 获取所有碰撞单元
     */
    getAllColUnits(): any[] {
        return this._colUnits;
    }

    /**
     * 设置是否可被攻击
     * @param damageable 是否可被攻击
     */
    setDamangeable(damageable: boolean) {
        this._bDamageable = damageable;
        this._units.forEach((unit) => {
            if (!unit.isDead) {
                unit.damageable = damageable;
            }
        });
    }

    /**
     * 初始化 UI
     */
    _initUI() {
        this._uiNode = new Node("uiNode");
        this.node.addChild(this._uiNode);

        this._topAnimNode = new Node("topAnimNode");
        this.node.addChild(this._topAnimNode);
    }

    /**
     * 初始化单元
     */
    _initUnits() {
        Tools.removeChildByName(this._uiNode!, "units");

        const unitsNode = new Node("units");
        unitsNode.addComponent(UITransform);
        this._uiNode!.addChild(unitsNode);

        for (const unitId of this._data!.units) {
            const unitData = GameIns.bossManager.getUnitData(unitId);
            if (unitData) {
                const unitNode = new Node();
                unitNode.addComponent(UITransform);
                unitsNode.addChild(unitNode);

                const unit = unitNode.addComponent(BossUnit);
                unit.init(unitData, this);
                unit.setCollideAtk(this._data!.collideAttack);
                unit.setPropertyRate(this.propertyRate);

                this._units.set(unitData.uId, unit);
            }
        }

        for (let i = this._atkPointsPool.length - 1; i < this._atkPointDatas.length; i++) {
            const pointNode = new Node("pointNode_" + i);
            this.node.addChild(pointNode);
            pointNode.angle = -180;

            const attackPoint = pointNode.addComponent(AttackPoint);
            this._atkPointsPool.push(attackPoint);
        }
    }

    /**
     * 设置身体皮肤
     * @param skinIndex 皮肤索引
     */
    setBodySkin(skinIndex: number) {
        this._units.forEach((unit) => {
            if (unit.isBody()) {
                unit.setSkin(skinIndex);
            }
        });
    }

    /**
 * 初始化轨迹
 */
    _initTrack() {
        this._trackCom = Tools.addScript(this.node, TrackComponent);
        this._trackCom!.setTrackGroupStartCall(() => {});
        this._trackCom!.setTrackGroupOverCall(() => {
            if (this._action === GameEnum.BossAction.Appear) {
                this._trackCom!.setTrackAble(false);
                this.setAction(GameEnum.BossAction.Transform);
            }
        });
        this._trackCom!.setTrackOverCall(() => {});
        this._trackCom!.setTrackLeaveCall(() => {});
        this._trackCom!.setTrackStartCall((track : any) => {
            this.setTrackType(track);
        });
    }

    /**
     * 开始出现轨迹
     */
    _startAppearTrack() {
        const trackGroup = new TrackGroup();
        trackGroup.loopNum = 1;
        trackGroup.trackIDs = [this._data!.appearParam[2]];
        trackGroup.speeds = [this._data!.appearParam[3]];
        trackGroup.trackIntervals = [0];

        this._trackCom!.init(this, [trackGroup], [], this._data!.appearParam[0], this._data!.appearParam[1]);
        this._trackCom!.setTrackAble(true);
        this._trackCom!.startTrack();
    }

    /**
     * 开始正常轨迹
     */
    _startNormalTrack() {
        this._trackCom!.init(this, this._data!.trackGroups, [], this.node.x, this.node.y);
        this._trackCom!.setTrackAble(true);
        this._trackCom!.startTrack();
        this.setAction(GameEnum.BossAction.Normal);
    }

    /**
     * 设置轨迹类型
     * @param track 当前轨迹
     */
    setTrackType(track: any) {
        if (track) {
            switch (track.type) {
                case 4:
                case 5:
                    if (this._curTrackType !== 4 && this._curTrackType !== 5) {
                        this._trackCom!.setTrackAble(false);
                        this._shootAble = false;
                        this._colUnits.forEach((unit) => unit.setCollideAble(false));
                        this._playCloakeHideAnim(() => {
                            this._trackCom!.setTrackAble(true);
                        });
                    }
                    break;

                default:
                    if (this._curTrackType === 4 || this._curTrackType === 5) {
                        this._shootAble = false;
                        this._trackCom!.setTrackAble(false);
                        this._playCloakeShowAnim(() => {
                            this._shootAble = true;
                            this._trackCom!.setTrackAble(true);
                            this._colUnits.forEach((unit) => unit.setCollideAble(true));
                        });
                        this._playSkel("cloake", true, () => {});
                    }
            }
            this._curTrackType = track.type;
        }
    }

    /**
     * 移动到指定位置
     * @param x X 坐标
     * @param y Y 坐标
     * @param speed 移动速度
     * @param transformMove 是否为变形移动
     */
    moveToPos(x: number, y: number, speed: number, transformMove: boolean = false) {
        this._moveToX = x;
        this._moveToY = y;
        this._moveSpeed = speed;
        this._bArriveDes = false;
        this._transFormMove = transformMove;
    }

    /**
     * 设置位置
     * @param pos 位置
     * @param arrived 是否到达目标
     */
    setPosition(pos: Vec2 | Vec3, arrived: boolean = false) {
        this._posX = pos.x;
        this._posY = pos.y;
        this.setPos(this._posX, this._posY);
        this._bArriveDes = arrived;
    }

    /**
     * 设置位置
     * @param x X 坐标
     * @param y Y 坐标
     * @param update 是否更新
     */
    setPos(x: number, y: number, update: boolean = true) {
        super.setPos(x, y, update);
        this._posX = x;
        this._posY = y;
    }

    /**
     * 处理下一个路径点
     * @param deltaTime 每帧时间
     */
    _processNextWayPoint(deltaTime: number) {
        if (this._bArriveDes && this._data!.trackGroups.length === 0) {
            this._nextWayPointTime += deltaTime;
            if (this._nextWayPointTime > this._nextWayPointInterval) {
                this._nextWayPointInterval = Tools.getRandomInArray(this._data!.wayPointIntervals)!;
                this._nextWayPointTime = 0;

                if (this._bFirstWayPoint) {
                    this._bFirstWayPoint = false;
                } else {
                    const index = Tools.random_int(0, this._data!.wayPointXs.length - 1);
                    this._nextWayPointX = this._data!.wayPointXs[index];
                    this._nextWayPointY = this._data!.wayPointYs[index];
                    this._nextWaySpeed = Tools.getRandomInArray(this._data!.speeds)!;
                    this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);
                }
            }
        }
    }


    _updateMove(deltaTime: number) {
        if (this._action === GameEnum.BossAction.Appear || this._data!.trackGroups.length > 0) {
            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑
            this._trackCom!.updateGameLogic(deltaTime);
        } else if (!this._bArriveDes) {
            // 如果未到达目标位置，则更新移动逻辑
            this._prePosX = this._posX;
            this._prePosY = this._posY;

            const deltaX = this._moveToX - this._posX;
            const deltaY = this._moveToY - this._posY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            let moveX = 0;
            let moveY = 0;

            // 如果距离小于等于移动速度，则直接到达目标点
            if (distance <= this._moveSpeed) {
                moveX = deltaX;
                moveY = deltaY;
            }
            // 否则按比例移动
            else {
                moveX = this._moveSpeed * deltaX / distance;
                moveY = this._moveSpeed * deltaY / distance;
            }

            // 更新位置
            this._posX += moveX;
            this._posY += moveY;
            this.setPos(this._posX, this._posY);

            // 检查是否到达目的地（当移动量很小时认为已到达）
            this._bArriveDes = (Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5);
        }
    }

    /**
     * 处理下一次攻击
     * @param deltaTime 每帧时间
     */
    _processNextAttack(deltaTime: number) {
        if (this._shootAble && this._action === GameEnum.BossAction.Normal) {
            this._nextAttackTime += deltaTime;
            if (this._nextAttackTime > this._nextAttackInterval) {
                this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;
                this._nextAttackTime = 0;

                let attackAction = null;
                if (this._bOrderAttack) {
                    const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;
                    Tools.arrRemove(this._orderAtkArr, randomIndex);
                    attackAction = this._atkActions[randomIndex];
                    this._orderIndex++;
                    if (this._orderIndex > this._atkActions.length - 1) {
                        this._bOrderAttack = false;
                    }
                } else {
                    attackAction = Tools.getRandomInArray(this._atkActions);
                }

                if (attackAction) {
                    this._bAttackMove = attackAction.bAtkMove;
                    this._attackID = attackAction.atkActId;
                    this._attackPoints.splice(0);

                    for (const pointId of attackAction.atkPointId) {
                        const pointData = this._atkPointDatas[pointId];
                        if (pointData[0]) {
                            let attackPoint = this._atkPointsPool[pointId]
                            if (!attackPoint) {
                                const pointNode = new Node();
                                this.node.addChild(pointNode);
                                attackPoint = pointNode.addComponent(AttackPoint);
                                this._atkPointsPool.push(attackPoint);
                            }
                            attackPoint.initForBoss(pointData[1], this);
                            this._attackPoints.push(attackPoint);
                        }
                    }

                    if (this._attackPoints.length > 0) {
                        this.setAction(GameEnum.BossAction.AttackPrepare);
                    }
                }
            }
        }
    }

    /**
     * 更新射击逻辑
     * @param deltaTime 每帧时间
     */
    async _udpateShoot(deltaTime: number) {
        if (this._shootAble) {
            let allAttacksOver = true;

            for (const attackPoint of this._attackPoints) {
                await attackPoint.updateGameLogic(deltaTime);
                if (!attackPoint.isAttackOver()) {
                    allAttacksOver = false;
                }
            }

            if (allAttacksOver) {
                this.setAction(GameEnum.BossAction.AttackOver);
            }
        }
    }

    /**
     * 检查攻击动画
     */
    _checkAtkAnim(): boolean {
        let hasAnimation = false;

        // for (const attackPoint of this._attackPoints) {
        //     for (const anim of attackPoint.getAtkAnims()) {
        //         const unit = this._units.get(anim[0]);
        //         if (unit && !Tools.arrContain(this._deadUnitIds, unit.unitId)) {
        //             hasAnimation = true;
        //             unit.playSkel(anim[1], false, () => {
        //                 this.setAction(GameEnum.BossAction.AttackIng);
        //             });
        //         }
        //     }
        // }

        return hasAnimation;
    }

    /**
     * 播放骨骼动画
     * @param animName 动画名称
     * @param loop 是否循环
     * @param callback 动画结束回调
     * @param unitId 单元 ID
     */
    _playSkel(animName: string, loop: boolean, callback: Function, unitId: number = -1) {
        this._units.forEach((unit, id) => {
            if (unitId === -1 || id === unitId) {
                unit.playSkel(animName, loop, callback);
            }
        });
    }

    /**
     * 播放死亡动画
     */
    _playDieAnim() {
        if (this._data!.nextBoss.length > 0) {
            this._checkNextBoss();
        } else {
            this._bRemoveable = true;
        }
    }

    /**
     * 播放白屏死亡动画
     */
    playDieWhiteAnim() {
        this.scheduleOnce(() => {
            EffectLayer.me.showWhiteScreen(4 * GameConst.ActionFrameTime, 255);
            // this._uiNode.opacity = 0;
            this._units.forEach((unit) => {
                unit.hideSmoke();
            });
        }, 41 * GameConst.ActionFrameTime);
    }

    /**
     * 播放坠落动画
     */
    _playFallAnim() {
        const frameTime = GameConst.ActionFrameTime;

        // const fallSequence = sequence(
        //     moveTo(2 * frameTime, v2(-1, 6)),
        //     moveTo(frameTime, v2(3, -2))
        // );

        // this._uiNode.runAction(repeatForever(fallSequence));
        // this._uiNode.runAction(scaleTo(60 * frameTime, 0.5));
    }

    /**
     * 播放震动动画
     */
    _playShakeAnim() {
        const frameTime = GameConst.ActionFrameTime;

        tween(this._uiNode!)
            .to(2 * frameTime, { position: v3(-3, -2) })
            .to(2 * frameTime, { position: v3(11, -14), angle: 1 })
            .to(2 * frameTime, { position: v3(7, 4) })
            .to(2 * frameTime, { position: v3(20, -9), angle: 0 })
            .to(2 * frameTime, { position: v3(29, 7) })
            .to(frameTime, { position: v3(13, -5) })
            .to(frameTime, { position: v3(17, 2) })
            .to(frameTime, { position: v3(4, -6) })
            .to(frameTime, { position: v3(14, 4) })
            .to(frameTime, { position: v3(-1, -4) })
            .to(frameTime, { position: v3(5, 6) })
            .to(frameTime, { position: v3(-3, -5) })
            .to(frameTime, { position: v3(1, 3) })
            .to(frameTime, { position: v3(-7, -6) })
            .to(frameTime, { position: v3(0, 2) })
            .to(frameTime, { position: v3(-3, -4) })
            .delay(frameTime)
            .to(frameTime, { position: v3(0, 0) })
            .start();
    }

    /**
     * 播放坠落震动动画
     */
    _playFallShake() {
        const frameTime = GameConst.ActionFrameTime;

        // const fallShakeSequence = sequence(
        //     moveTo(2 * frameTime, v2(-1, 6)),
        //     moveTo(frameTime, v2(3, -2))
        // );

        // this._uiNode.runAction(repeatForever(fallShakeSequence));
    }

    /**
     * 播放隐身动画
     */
    _playCloakeAnim() {
        if (!this._cloakeAnim) {
            const animNode = instantiate(GameIns.gameResManager.frameAnim);
            this.node.addChild(animNode);

            this._cloakeAnim = animNode.getComponent(PfFrameAnim);
            this._cloakeAnim!.init(
                GameIns.enemyManager!.enemyAtlas!,
                "a_",
                12,
                GameConst.ActionFrameTime
            );
            animNode.active = false;
        }

        this._cloakeAnim!.node.setScale(1.3, 1.3);
        this._cloakeAnim!.node.active = true;
        this._cloakeAnim!.reset(1);
    }

    /**
     * 播放隐身消失动画
     * @param callback 动画结束回调
     */
    _playCloakeHideAnim(callback: Function) {
        const frameTime = GameConst.ActionFrameTime;

        // GameIns.audioManager.playEffect("cloake");
        this._playCloakeAnim();

        // tween(this.node)
        //     .to(5 * frameTime, { opacity: 90 })
        //     .to(2 * frameTime, { opacity: 0 })
        //     .call(() => {
        //         this._playSkel("cloake", true);
        //     })
        //     .to(6 * frameTime, { opacity: 255 })
        //     .call(() => {
        //         if (callback) callback();
        //     })
        //     .start();
    }

    /**
     * 播放隐身显现动画
     * @param callback 动画结束回调
     */
    _playCloakeShowAnim(callback: Function) {
        const frameTime = GameConst.ActionFrameTime;

        // tween(this.node)
        //     .to(4 * frameTime, { opacity: 102 })
        //     .to(2 * frameTime, { opacity: 255 })
        //     .to(4 * frameTime, { opacity: 102 })
        //     .to(2 * frameTime, { opacity: 255 })
        //     .to(3 * frameTime, { opacity: 102 })
        //     .to(frameTime, { opacity: 0 })
        //     .call(() => {
        //         this._playSkel(this._idleName, true);
        //         this._playCloakeAnim();
        //     })
        //     .to(7 * frameTime, { opacity: 255 })
        //     .call(() => {
        //         if (callback) callback();
        //     })
        //     .start();
    }
    /**
     * 检查并生成下一个 Boss
     */
    _checkNextBoss() {
        if (this._data!.id === 200) {
            this._playSkel("next", false, () => {
                for (const nextBossId of this._data!.nextBoss) {
                    const bossData = GameIns.bossManager!.getBossDatas(nextBossId)![0];
                    const boss = GameIns.bossManager.createBossById(nextBossId)!;
                    boss.setPosition(this.node.position, true);
                    boss.active = false;
                    boss.setPropertyRate(this.propertyRate, true);

                    tween(boss.node)
                        .to(1, { position: v3(bossData.appearParam[0], bossData.appearParam[1]) })
                        .call(() => {
                            boss.setPosition(boss.node.position, true);
                            boss.startBattle();
                        })
                        .start();
                }

                this.node.active = false;
                this._bRemoveable = true;
            });
        }
    }

    /**
     * 改变血量
     * @param delta 血量变化值
     */
    hpChange(delta: number) {
        this.m_curHp += delta;
        if (this.m_curHp < 0) {
            this.m_curHp = 0;
        }
        // BossBattleManager.hpChange(delta, this.node);
    }

    /**
     * 获取血量百分比
     */
    getHpPercent(): number {
        return this.m_curHp / this.m_totalHp;
    }
}
{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/UIMgr.ts"], "names": ["BaseUI", "UIManager", "BlockInputEvents", "<PERSON><PERSON>", "Component", "director", "instantiate", "Node", "Prefab", "resources", "tween", "UIOpacity", "UITransform", "v3", "Widget", "ButtonPlus", "<PERSON><PERSON><PERSON><PERSON>", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getUIOption", "isClickBgCloseUI", "isClickBgHideUI", "constructor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fadeIn", "PopUp", "opacityComp", "getComponent", "opacity", "Promise", "resolve", "to", "call", "start", "fadeOut", "node", "active", "uiMap", "Map", "layers", "_uiRoot", "initUIRoot", "canvas", "getScene", "getChildByName", "addComponent", "widget", "isAlignLeft", "isAlignRight", "left", "right", "isAlignTop", "isAlignBottom", "top", "bottom", "isAbsoluteLeft", "isAbsoluteRight", "isAbsoluteTop", "isAbsoluteBottom", "updateAlignment", "setPosition", "position", "uiTransform", "contentSize", "addPersistRootNode", "<PERSON><PERSON><PERSON><PERSON>", "initializeLayers", "layer", "Top", "nd", "set", "loadUI", "uiClass", "ui", "url", "has", "get", "console", "debug", "startTime", "Date", "now", "prefab", "reject", "load", "err", "asset", "Error", "addClickBgHandler", "cost", "error", "message", "openUI", "args", "onShow", "hideUI", "warn", "onHide", "closeUI", "onClose", "destroy", "delete", "blockInputEvents", "btn", "clickDefZoomScale", "addClick", "event", "Instance", "UIMgr", "window"], "mappings": ";;;2NAgDsBA,M,EA0FTC,S;;;;;;;;;;;;;;;;;;AAxIJC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAsBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,M,OAAAA,M;;AACpIC,MAAAA,U,iBAAAA,U;;;;;6EAHT;;;;;AAOA;yBACYC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;;AAYZ;AACA;AACA;AACA;;;AAwBA;wBACsBhB,M,GAAf,MAAeA,MAAf,SAA8BI,SAA9B,CAAwC;AAC3C;AACJ;AACA;AACA;AACwB,eAANa,MAAM,GAAW;AAAE,iBAAO,QAAP;AAAkB;AACnD;AACJ;AACA;AACA;;;AAC0B,eAARC,QAAQ,GAAY;AAAE,iBAAOF,OAAO,CAACG,UAAf;AAA2B;AAE/D;AACJ;AACA;AACA;;;AAC6B,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AACHC,YAAAA,gBAAgB,EAAE,KADf;AAEHC,YAAAA,eAAe,EAAE;AAFd,WAAP;AAIH,SArB0C,CAuB3C;;AAEA;AACJ;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACA;;;AAEI;AACJ;AACA;AACIC,QAAAA,WAAW,GAAG;AACV;AADU,eAtBPC,OAsBO;AAEb;AAED;AACJ;AACA;AACA;;;AACuB,cAANC,MAAM,GAAkB;AACjC;AACA,cAAI,KAAKD,OAAL,KAAiBR,OAAO,CAACU,KAA7B,EAAoC,OAFH,CAGjC;;AACA,gBAAMC,WAAW,GAAG,KAAKC,YAAL,CAAkBjB,SAAlB,CAApB;;AACA,cAAIgB,WAAJ,EAAiB;AACb;AACAA,YAAAA,WAAW,CAACE,OAAZ,GAAsB,CAAtB;AACA,mBAAO,IAAIC,OAAJ,CAAaC,OAAD,IAAa;AAC5B;AACArB,cAAAA,KAAK,CAACiB,WAAD,CAAL,CAAmBK,EAAnB,CAAsB,GAAtB,EAA2B;AAAEH,gBAAAA,OAAO,EAAE;AAAX,eAA3B,EAA6CI,IAA7C,CAAkD,MAAMF,OAAO,EAA/D,EAAmEG,KAAnE;AACH,aAHM,CAAP;AAIH;AACJ;AAED;AACJ;AACA;AACA;;;AACwB,cAAPC,OAAO,GAAkB;AAClC;AACA,cAAI,CAAC,KAAKC,IAAL,CAAUC,MAAX,IAAqB,KAAKb,OAAL,KAAiBR,OAAO,CAACU,KAAlD,EAAyD;AACrD;AACH,WAJiC,CAKlC;;;AACA,gBAAMC,WAAW,GAAG,KAAKC,YAAL,CAAkBjB,SAAlB,CAApB;;AACA,cAAIgB,WAAJ,EAAiB;AACb,mBAAO,IAAIG,OAAJ,CAAaC,OAAD,IAAa;AAC5B;AACArB,cAAAA,KAAK,CAACiB,WAAD,CAAL,CAAmBK,EAAnB,CAAsB,GAAtB,EAA2B;AAAEH,gBAAAA,OAAO,EAAE;AAAX,eAA3B,EAA2CI,IAA3C,CAAgD,MAAMF,OAAO,EAA7D,EAAiEG,KAAjE;AACH,aAHM,CAAP;AAIH;AACJ;;AAtF0C,O,GAyF/C;;;2BACajC,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAGnB;AAHmB,eAIXqC,KAJW,GAIkB,IAAIC,GAAJ,EAJlB;AAKnB;AALmB,eAMXC,MANW,GAMkB,IAAID,GAAJ,EANlB;AAOnB;AAPmB,eAQXE,OARW;AAAA;;AAUnB;AACJ;AACA;AACYC,QAAAA,UAAU,GAAS;AAAA;;AACvB;AACA,cAAIC,MAAM,GAAGtC,QAAQ,CAACuC,QAAT,GAAoBC,cAApB,CAAmC,QAAnC,CAAb,CAFuB,CAGvB;;AACA,eAAKJ,OAAL,GAAe,IAAIlC,IAAJ,CAAS,SAAT,CAAf,CAJuB,CAKvB;;AACA,eAAKkC,OAAL,CAAaK,YAAb,CAA0B3C,MAA1B,EANuB,CAOvB;;;AACA,gBAAM4C,MAAM,GAAG,KAAKN,OAAL,CAAaK,YAAb,CAA0BhC,MAA1B,CAAf,CARuB,CASvB;;;AACAiC,UAAAA,MAAM,CAACC,WAAP,GAAqB,IAArB;AACAD,UAAAA,MAAM,CAACE,YAAP,GAAsB,IAAtB;AACAF,UAAAA,MAAM,CAACG,IAAP,GAAc,CAAd,CAZuB,CAYH;;AACpBH,UAAAA,MAAM,CAACI,KAAP,GAAe,CAAf,CAbuB,CAaH;AAEpB;;AACAJ,UAAAA,MAAM,CAACK,UAAP,GAAoB,IAApB;AACAL,UAAAA,MAAM,CAACM,aAAP,GAAuB,IAAvB;AACAN,UAAAA,MAAM,CAACO,GAAP,GAAa,CAAb,CAlBuB,CAkBH;;AACpBP,UAAAA,MAAM,CAACQ,MAAP,GAAgB,CAAhB,CAnBuB,CAmBH;AAEpB;;AACAR,UAAAA,MAAM,CAACS,cAAP,GAAwB,IAAxB;AACAT,UAAAA,MAAM,CAACU,eAAP,GAAyB,IAAzB;AACAV,UAAAA,MAAM,CAACW,aAAP,GAAuB,IAAvB;AACAX,UAAAA,MAAM,CAACY,gBAAP,GAA0B,IAA1B;AAEAZ,UAAAA,MAAM,CAACa,eAAP,GA3BuB,CA2BG;AAC1B;;AACA,eAAKnB,OAAL,CAAaoB,WAAb,CAAyBlB,MAAM,CAACmB,QAAhC,EA7BuB,CA8BvB;;;AACA,gBAAMC,WAAW,GAAG,KAAKtB,OAAL,CAAab,YAAb,CAA0BhB,WAA1B,CAApB,CA/BuB,CAgCvB;;;AACAmD,UAAAA,WAAW,CAACC,WAAZ,GAA0BrB,MAAM,CAACf,YAAP,CAAoBhB,WAApB,EAAiCoD,WAA3D,CAjCuB,CAkCvB;;AACA3D,UAAAA,QAAQ,CAAC4D,kBAAT,CAA4B,KAAKxB,OAAjC,EAnCuB,CAoCvB;;AACA,gCAAApC,QAAQ,CAACuC,QAAT,kCAAqBsB,QAArB,CAA8B,KAAKzB,OAAnC;AACH;AAED;AACJ;AACA;;;AACW0B,QAAAA,gBAAgB,GAAS;AAC5B,cAAI,KAAK1B,OAAT,EAAkB,OADU,CAE5B;;AACA,eAAKC,UAAL,GAH4B,CAI5B;;AACA,eAAK,IAAI0B,KAAK,GAAGpD,OAAO,CAACG,UAAzB,EAAqCiD,KAAK,IAAIpD,OAAO,CAACqD,GAAtD,EAA2DD,KAAK,EAAhE,EAAoE;AAChE;AACA,kBAAME,EAAQ,GAAG,IAAI/D,IAAJ,CAASS,OAAO,CAACoD,KAAD,CAAhB,CAAjB;AACAE,YAAAA,EAAE,CAACxB,YAAH,CAAgBlC,WAAhB,EAA6BoD,WAA7B,GAA2C,KAAKvB,OAAL,CAAab,YAAb,CAA0BhB,WAA1B,EAAuCoD,WAAlF,CAHgE,CAIhE;;AACA,kBAAMjB,MAAM,GAAGuB,EAAE,CAACxB,YAAH,CAAgBhC,MAAhB,CAAf,CALgE,CAMhE;;AACAiC,YAAAA,MAAM,CAACC,WAAP,GAAqB,IAArB;AACAD,YAAAA,MAAM,CAACE,YAAP,GAAsB,IAAtB;AACAF,YAAAA,MAAM,CAACG,IAAP,GAAc,CAAd,CATgE,CAS5C;;AACpBH,YAAAA,MAAM,CAACI,KAAP,GAAe,CAAf,CAVgE,CAU5C;AAEpB;;AACAJ,YAAAA,MAAM,CAACK,UAAP,GAAoB,IAApB;AACAL,YAAAA,MAAM,CAACM,aAAP,GAAuB,IAAvB;AACAN,YAAAA,MAAM,CAACO,GAAP,GAAa,CAAb,CAfgE,CAe5C;;AACpBP,YAAAA,MAAM,CAACQ,MAAP,GAAgB,CAAhB,CAhBgE,CAgB5C;AAEpB;;AACAR,YAAAA,MAAM,CAACS,cAAP,GAAwB,IAAxB;AACAT,YAAAA,MAAM,CAACU,eAAP,GAAyB,IAAzB;AACAV,YAAAA,MAAM,CAACW,aAAP,GAAuB,IAAvB;AACAX,YAAAA,MAAM,CAACY,gBAAP,GAA0B,IAA1B;AAEAZ,YAAAA,MAAM,CAACa,eAAP,GAxBgE,CAwBtC;AAC1B;;AACA,iBAAKpB,MAAL,CAAY+B,GAAZ,CAAgBH,KAAhB,EAAuBE,EAAvB,EA1BgE,CA2BhE;;AACA,iBAAK7B,OAAL,CAAayB,QAAb,CAAsBI,EAAtB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACuB,cAANE,MAAM,CAAmBC,OAAnB,EAAoD;AACnE,cAAIC,EAAJ,CADmE,CAEnE;;AACA,gBAAMC,GAAG,GAAGF,OAAO,CAACxD,MAAR,EAAZ;;AAEA,cAAI,KAAKqB,KAAL,CAAWsC,GAAX,CAAeD,GAAf,CAAJ,EAAyB;AACrB;AACAD,YAAAA,EAAE,GAAG,KAAKpC,KAAL,CAAWuC,GAAX,CAAeF,GAAf,CAAL;AACAG,YAAAA,OAAO,CAACC,KAAR,CAAc,oBAAd,EAAoCN,OAAO,CAACxD,MAAR,EAApC,EAAsD,WAAtD;AACH,WAJD,MAIO;AACH,kBAAM+D,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB;;AACA,gBAAI;AACA;AACA,oBAAMC,MAAM,GAAG,MAAM,IAAIrD,OAAJ,CAAoB,CAACC,OAAD,EAAUqD,MAAV,KAAqB;AAC1D3E,gBAAAA,SAAS,CAAC4E,IAAV,CAAeV,GAAf,EAAoBnE,MAApB,EAA4B,CAAC8E,GAAD,EAAMC,KAAN,KAAgB;AACxC,sBAAID,GAAJ,EAAS;AACLF,oBAAAA,MAAM,CAACE,GAAD,CAAN;AACH,mBAFD,MAEO;AACHvD,oBAAAA,OAAO,CAACwD,KAAD,CAAP;AACH;AACJ,iBAND;AAOH,eARoB,CAArB,CAFA,CAYA;;AACA,oBAAMnD,IAAI,GAAG9B,WAAW,CAAC6E,MAAD,CAAxB,CAbA,CAcA;;AACAT,cAAAA,EAAE,GAAGtC,IAAI,CAACR,YAAL,CAAkB6C,OAAlB,CAAL;;AAEA,kBAAI,CAACC,EAAL,EAAS;AACL,sBAAM,IAAIc,KAAJ,CAAW,qCAAoCb,GAAI,EAAnD,CAAN;AACH,eAnBD,CAoBA;;;AACA,oBAAMP,KAAK,GAAG,KAAK5B,MAAL,CAAYqC,GAAZ,CAAgBJ,OAAO,CAACvD,QAAR,EAAhB,CAAd;;AACA,kBAAIkD,KAAJ,EAAW;AACPA,gBAAAA,KAAK,CAACF,QAAN,CAAe9B,IAAf;AACH,eAFD,MAEO;AACH,sBAAM,IAAIoD,KAAJ,CAAW,uBAAsBf,OAAO,CAACvD,QAAR,EAAmB,EAApD,CAAN;AACH,eA1BD,CA2BA;;;AACAkB,cAAAA,IAAI,CAACyB,WAAL,CAAiBhD,EAAE,EAAnB,EA5BA,CA6BA;;AACA6D,cAAAA,EAAE,CAAClD,OAAH,GAAaiD,OAAO,CAACvD,QAAR,EAAb,CA9BA,CA+BA;;AACA,mBAAKoB,KAAL,CAAWiC,GAAX,CAAeI,GAAf,EAAoBD,EAApB;AACAtC,cAAAA,IAAI,CAACC,MAAL,GAAc,KAAd,CAjCA,CAkCA;;AACA,mBAAKoD,iBAAL,CAAuBf,EAAvB,EAA2BD,OAA3B;AACA,oBAAMiB,IAAI,GAAGT,IAAI,CAACC,GAAL,KAAaF,SAA1B;AACAF,cAAAA,OAAO,CAACC,KAAR,CAAe,sBAAqBN,OAAO,CAACxD,MAAR,EAAiB,sBAAqByE,IAAK,IAA/E;AACH,aAtCD,CAsCE,OAAOC,KAAP,EAAc;AACZb,cAAAA,OAAO,CAACa,KAAR,CAAe,sBAAqBlB,OAAO,CAACxD,MAAR,EAAiB,iBAAgB0E,KAAK,CAACC,OAAQ,EAAnF;AACA,oBAAMD,KAAN;AACH;AACJ;;AACD,iBAAOjB,EAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWG,QAAAA,GAAG,CAAmBJ,OAAnB,EAA2C;AACjD,iBAAO,KAAKnC,KAAL,CAAWuC,GAAX,CAAeJ,OAAO,CAACxD,MAAR,EAAf,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACuB,cAAN4E,MAAM,CAAmBpB,OAAnB,EAAwC,GAAGqB,IAA3C,EAAuE;AACtF,gBAAMd,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB,CADsF,CAEtF;;AACA,cAAIR,EAAU,GAAG,MAAM,KAAKF,MAAL,CAAYC,OAAZ,CAAvB;AACAC,UAAAA,EAAE,CAACtC,IAAH,CAAQC,MAAR,GAAiB,IAAjB,CAJsF,CAKtF;;AACA,gBAAMqC,EAAE,CAACjD,MAAH,EAAN,CANsF,CAOtF;;AACA,gBAAMiD,EAAE,CAACqB,MAAH,CAAU,GAAGD,IAAb,CAAN;AACA,gBAAMJ,IAAI,GAAGT,IAAI,CAACC,GAAL,KAAaF,SAA1B;AACAF,UAAAA,OAAO,CAACC,KAAR,CAAe,sBAAqBN,OAAO,CAACxD,MAAR,EAAiB,SAAQyE,IAAK,IAAlE;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACuB,cAANM,MAAM,CAAmBvB,OAAnB,EAAwC,GAAGqB,IAA3C,EAAuE;AACtF;AACA,cAAIpB,EAAU,GAAG,KAAKpC,KAAL,CAAWuC,GAAX,CAAeJ,OAAO,CAACxD,MAAR,EAAf,CAAjB;;AACA,cAAI,CAACyD,EAAL,EAAS;AACLI,YAAAA,OAAO,CAACmB,IAAR,CAAa,0BAAb,EAAyCxB,OAAO,CAACxD,MAAR,EAAzC;AACA;AACH;;AACD,gBAAM+D,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB,CAPsF,CAQtF;;AACA,gBAAMR,EAAE,CAACvC,OAAH,EAAN,CATsF,CAUtF;;AACA,gBAAMuC,EAAE,CAACwB,MAAH,CAAU,GAAGJ,IAAb,CAAN,CAXsF,CAYtF;;AACApB,UAAAA,EAAE,CAACtC,IAAH,CAAQC,MAAR,GAAiB,KAAjB;AACA,gBAAMqD,IAAI,GAAGT,IAAI,CAACC,GAAL,KAAaF,SAA1B;AACAF,UAAAA,OAAO,CAACC,KAAR,CAAe,sBAAqBN,OAAO,CAACxD,MAAR,EAAiB,SAAQyE,IAAK,IAAlE;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACwB,cAAPS,OAAO,CAAmB1B,OAAnB,EAAwC,GAAGqB,IAA3C,EAAuE;AACvF;AACA,cAAIpB,EAAU,GAAG,KAAKpC,KAAL,CAAWuC,GAAX,CAAeJ,OAAO,CAACxD,MAAR,EAAf,CAAjB;;AACA,cAAI,CAACyD,EAAL,EAAS;AACLI,YAAAA,OAAO,CAACmB,IAAR,CAAa,qCAAb,EAAoDxB,OAAO,CAACxD,MAAR,EAApD;AACA;AACH;;AACD,gBAAM+D,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB,CAPuF,CAQvF;;AACA,gBAAMR,EAAE,CAACvC,OAAH,EAAN,CATuF,CAUvF;;AACA,gBAAMuC,EAAE,CAACwB,MAAH,CAAU,GAAGJ,IAAb,CAAN,CAXuF,CAYvF;;AACA,gBAAMpB,EAAE,CAAC0B,OAAH,CAAW,GAAGN,IAAd,CAAN,CAbuF,CAcvF;;AACApB,UAAAA,EAAE,CAACtC,IAAH,CAAQC,MAAR,GAAiB,KAAjB,CAfuF,CAgBvF;;AACAqC,UAAAA,EAAE,CAACtC,IAAH,CAAQiE,OAAR,GAjBuF,CAkBvF;;AACA,eAAK/D,KAAL,CAAWgE,MAAX,CAAkB7B,OAAO,CAACxD,MAAR,EAAlB;AACA,gBAAMyE,IAAI,GAAGT,IAAI,CAACC,GAAL,KAAaF,SAA1B;AACAF,UAAAA,OAAO,CAACC,KAAR,CAAe,uBAAsBN,OAAO,CAACxD,MAAR,EAAiB,SAAQyE,IAAK,IAAnE;AACH;;AAEOD,QAAAA,iBAAiB,CAAmBf,EAAnB,EAA+BD,OAA/B,EAAoD;AACzE,cAAI,CAACA,OAAO,CAACrD,WAAR,GAAsBE,eAAvB,IACA,CAACmD,OAAO,CAACrD,WAAR,GAAsBC,gBAD3B,EAC6C;AACzC;AACH;;AACD,cAAIkF,gBAAgB,GAAG7B,EAAE,CAAC9C,YAAH,CAAgB1B,gBAAhB,CAAvB;;AACA,cAAI,CAACqG,gBAAL,EAAuB;AACnBA,YAAAA,gBAAgB,GAAG7B,EAAE,CAACtC,IAAH,CAAQU,YAAR,CAAqB5C,gBAArB,CAAnB;AACH;;AACD,cAAIsG,GAAG,GAAG9B,EAAE,CAACtC,IAAH,CAAQR,YAAR;AAAA;AAAA,uCAAV;;AACA,cAAI,CAAC4E,GAAL,EAAU;AACNA,YAAAA,GAAG,GAAG9B,EAAE,CAACtC,IAAH,CAAQU,YAAR;AAAA;AAAA,yCAAN;AACH;;AACD0D,UAAAA,GAAG,CAACC,iBAAJ,GAAwB,KAAxB;AACAD,UAAAA,GAAG,CAACE,QAAJ,CAAcC,KAAD,IAAuB;AAChC,gBAAIlC,OAAO,CAACrD,WAAR,GAAsBE,eAA1B,EAA2C;AACvC,mBAAK0E,MAAL,CAAYvB,OAAZ;AACH,aAFD,MAEO,IAAIA,OAAO,CAACrD,WAAR,GAAsBC,gBAA1B,EAA4C;AAC/C,mBAAK8E,OAAL,CAAa1B,OAAb;AACH;AACJ,WAND,EAMGC,EANH;AAOH;;AAvQkB,O,GA0QvB;;;gBA1QazE,S;AACT;AADSA,MAAAA,S,CAEc2G,Q,GAAsB,IAAI3G,OAAJ,E;;uBAyQpC4G,K,GAAQ5G,SAAS,CAAC2G,Q,GAC/B;;;AACAE,MAAAA,MAAM,CAAC,OAAD,CAAN,GAAkBD,KAAlB", "sourcesContent": ["// ui.ts\n\nimport { BlockInputEvents, Canvas, Component, director, EventTouch, instantiate, Node, Prefab, resources, tween, UIOpacity, UITransform, v3, Widget } from \"cc\";\nimport { ButtonPlus } from \"./common/components/button/ButtonPlus\";\n\n\n\n// UI 层级枚举，定义不同的 UI 显示层级\nexport enum UILayer {\n    Background,  // 背景层，位于最底层\n    Default,     // 默认层，普通 UI 显示层\n    PopUp,       // 弹出层，用于显示弹窗等\n    Top,         // 顶层，显示在最上方\n}\n\nexport interface UIOpt {\n    isClickBgCloseUI?: boolean,\n    isClickBgHideUI?: boolean,\n}\n\n/**\n * UI 类的构造函数接口，定义了 UI 类需要实现的静态方法\n * @template T - 继承自 BaseUI 的类\n */\nexport interface UIClass<T extends BaseUI> {\n    /**\n     * 创建该 UI 类的新实例\n     */\n    new(): T;\n    /**\n     * 获取该 UI 类对应的资源路径\n     * @returns {string} 资源路径\n     */\n    getUrl(): string;\n    /**\n     * 获取该 UI 类对应的显示层级\n     * @returns {UILayer} 显示层级\n     */\n    getLayer(): UILayer;\n\n    /**\n     * 获取该 UI 类对应的选项\n     * @returns {UIOpt} 选项\n     */\n    getUIOption(): UIOpt;\n}\n\n// 抽象的基础 UI 类，所有具体的 UI 类都应继承自此类\nexport abstract class BaseUI extends Component {\n    /**\n     * 获取该 UI 类对应的资源路径，默认返回 \"BaseUI\"\n     * @returns {string} 资源路径\n     */\n    public static getUrl(): string { return \"BaseUI\"; }\n    /**\n     * 获取该 UI 类对应的显示层级，默认返回背景层\n     * @returns {UILayer} 显示层级\n     */\n    public static getLayer(): UILayer { return UILayer.Background }\n\n    /**\n     * 获取该 UI 类对应的选项\n     * @returns {UIOpt} 选项\n     */\n    public static getUIOption(): UIOpt {\n        return {\n            isClickBgCloseUI: false,\n            isClickBgHideUI: false,\n        }\n    }\n\n    // 当前 UI 实例所在的显示层级\n    public uiLayer: UILayer;\n    /**\n     * 显示 UI 的方法，需要子类实现\n     * @param {...any[]} args - 传递给显示方法的参数\n     * @returns {Promise<void>} 显示完成的 Promise\n     */\n    public abstract onShow(...args: any[]): Promise<void>;\n    /**\n     * 隐藏 UI 的方法，需要子类实现\n     * @param {...any[]} args - 传递给隐藏方法的参数\n     * @returns {Promise<void>} 隐藏完成的 Promise\n     */\n    public abstract onHide(...args: any[]): Promise<void>;\n    /**\n     * 关闭 UI 的方法，需要子类实现\n     * @param {...any[]} args - 传递给关闭方法的参数\n     * @returns {Promise<void>} 关闭完成的 Promise\n     */\n    public abstract onClose(...args: any[]): Promise<void>;\n    /**\n     * 构造函数，调用父类的构造函数\n     */\n    constructor() {\n        super();\n    }\n\n    /**\n     * 显示时的动画效果，仅对弹出层生效\n     * @returns {Promise<void>} 动画完成的 Promise\n     */\n    public async fadeIn(): Promise<void> {\n        // 非弹出层不执行动画\n        if (this.uiLayer !== UILayer.PopUp) return\n        // 获取 UI 透明度组件\n        const opacityComp = this.getComponent(UIOpacity);\n        if (opacityComp) {\n            // 设置初始透明度为 0\n            opacityComp.opacity = 0;\n            return new Promise((resolve) => {\n                // 使用 tween 动画将透明度在 0.5 秒内渐变为 255\n                tween(opacityComp).to(0.5, { opacity: 255 }).call(() => resolve()).start()\n            })\n        }\n    }\n\n    /**\n     * 隐藏时的动画效果，仅对弹出层且激活状态的节点生效\n     * @returns {Promise<void>} 动画完成的 Promise\n     */\n    public async fadeOut(): Promise<void> {\n        // 节点未激活或非弹出层不执行动画\n        if (!this.node.active || this.uiLayer !== UILayer.PopUp) {\n            return\n        }\n        // 获取 UI 透明度组件\n        const opacityComp = this.getComponent(UIOpacity);\n        if (opacityComp) {\n            return new Promise((resolve) => {\n                // 使用 tween 动画将透明度在 0.5 秒内渐变为 0\n                tween(opacityComp).to(0.5, { opacity: 0 }).call(() => resolve()).start()\n            })\n        }\n    }\n}\n\n// UI 管理器类，负责 UI 的加载、显示、隐藏和关闭等操作\nexport class UIManager {\n    // 单例实例\n    public static readonly Instance: UIManager = new UIManager();\n    // 存储已打开的 UI 实例，键为 UI 类对应的资源路径\n    private uiMap: Map<string, BaseUI> = new Map();\n    // UI 层级节点，键为 UI 层级，值为对应层级的节点\n    private layers: Map<UILayer, Node> = new Map();\n    // UI 根节点\n    private _uiRoot: Node;\n\n    /**\n     * 初始化 UI 根节点\n     */\n    private initUIRoot(): void {\n        // 获取场景中的 Canvas 节点\n        let canvas = director.getScene().getChildByName(\"Canvas\");\n        // 创建 UI 根节点\n        this._uiRoot = new Node('UI_Root');\n        // 为 UI 根节点添加 Canvas 组件\n        this._uiRoot.addComponent(Canvas);\n        // 为节点添加 Widget 组件\n        const widget = this._uiRoot.addComponent(Widget);\n        // 宽度拉伸\n        widget.isAlignLeft = true;\n        widget.isAlignRight = true;\n        widget.left = 0;    // 左边距 0\n        widget.right = 0;   // 右边距 0\n\n        // 高度拉伸\n        widget.isAlignTop = true;\n        widget.isAlignBottom = true;\n        widget.top = 0;     // 上边距 0\n        widget.bottom = 0;  // 下边距 0\n\n        // 统一使用像素单位\n        widget.isAbsoluteLeft = true;\n        widget.isAbsoluteRight = true;\n        widget.isAbsoluteTop = true;\n        widget.isAbsoluteBottom = true;\n\n        widget.updateAlignment(); // 节点将铺满父容器\n        // 设置 UI 根节点的位置与 Canvas 节点相同\n        this._uiRoot.setPosition(canvas.position)\n        // 获取 UI 根节点的 UI 变换组件\n        const uiTransform = this._uiRoot.getComponent(UITransform);\n        // 设置 UI 根节点的内容大小与 Canvas 节点相同\n        uiTransform.contentSize = canvas.getComponent(UITransform).contentSize;\n        // 将 UI 根节点设置为持久化节点\n        director.addPersistRootNode(this._uiRoot);\n        // 将 UI 根节点添加到场景中\n        director.getScene()?.addChild(this._uiRoot);\n    }\n\n    /**\n     * 初始化各个 UI 层级节点\n     */\n    public initializeLayers(): void {\n        if (this._uiRoot) return\n        // 初始化 UI 根节点\n        this.initUIRoot();\n        // 遍历所有 UI 层级\n        for (let layer = UILayer.Background; layer <= UILayer.Top; layer++) {\n            // 创建对应层级的节点\n            const nd: Node = new Node(UILayer[layer]);\n            nd.addComponent(UITransform).contentSize = this._uiRoot.getComponent(UITransform).contentSize;\n            // 为节点添加 Widget 组件\n            const widget = nd.addComponent(Widget);\n            // 宽度拉伸\n            widget.isAlignLeft = true;\n            widget.isAlignRight = true;\n            widget.left = 0;    // 左边距 0\n            widget.right = 0;   // 右边距 0\n\n            // 高度拉伸\n            widget.isAlignTop = true;\n            widget.isAlignBottom = true;\n            widget.top = 0;     // 上边距 0\n            widget.bottom = 0;  // 下边距 0\n\n            // 统一使用像素单位\n            widget.isAbsoluteLeft = true;\n            widget.isAbsoluteRight = true;\n            widget.isAbsoluteTop = true;\n            widget.isAbsoluteBottom = true;\n\n            widget.updateAlignment(); // 节点将铺满父容器\n            // 将节点存储到层级映射中\n            this.layers.set(layer, nd);\n            // 将节点添加到 UI 根节点下\n            this._uiRoot.addChild(nd);\n        }\n    }\n\n    /**\n     * 加载指定 UI 类的实例\n     * @template T - 继承自 BaseUI 的类\n     * @param {UIClass<T>} uiClass - UI 类\n     * @returns {Promise<T>} 加载完成的 UI 实例\n     */\n    public async loadUI<T extends BaseUI>(uiClass: UIClass<T>): Promise<T> {\n        let ui: BaseUI;\n        // 获取 UI 类对应的资源路径\n        const url = uiClass.getUrl();\n\n        if (this.uiMap.has(url)) {\n            // 如果 UI 实例已存在，从缓存中获取并激活节点\n            ui = this.uiMap.get(url);\n            console.debug(\"[UIManager] loadUI\", uiClass.getUrl(), \"use cache\");\n        } else {\n            const startTime = Date.now();\n            try {\n                // 使用 Cocos 内置的 resources.load 加载预制体\n                const prefab = await new Promise<Prefab>((resolve, reject) => {\n                    resources.load(url, Prefab, (err, asset) => {\n                        if (err) {\n                            reject(err);\n                        } else {\n                            resolve(asset as Prefab);\n                        }\n                    });\n                });\n\n                // 实例化预制体\n                const node = instantiate(prefab);\n                // 获取 UI 组件\n                ui = node.getComponent(uiClass) as BaseUI;\n\n                if (!ui) {\n                    throw new Error(`UI component not found on prefab: ${url}`);\n                }\n                // 添加到对应的层级\n                const layer = this.layers.get(uiClass.getLayer());\n                if (layer) {\n                    layer.addChild(node);\n                } else {\n                    throw new Error(`UI layer not found: ${uiClass.getLayer()}`);\n                }\n                // 设置节点位置\n                node.setPosition(v3());\n                // 设置 UI 实例的显示层级\n                ui.uiLayer = uiClass.getLayer();\n                // 将 UI 实例存储到 uiMap 中\n                this.uiMap.set(url, ui);\n                node.active = false;\n                // 自动创建背景节点\n                this.addClickBgHandler(ui, uiClass);\n                const cost = Date.now() - startTime;\n                console.debug(`[UIManager] loadUI ${uiClass.getUrl()} load success cost ${cost}ms`);\n            } catch (error) {\n                console.error(`[UIManager] loadUI ${uiClass.getUrl()} load failed: ${error.message}`);\n                throw error;\n            }\n        }\n        return ui as T;\n    }\n\n    /**\n     * 获取指定 UI 类的实例\n     * @template T - 继承自 BaseUI 的类\n     * @param {UIClass<T>} uiClass - UI 类\n     * @returns {T} UI 实例\n     */\n    public get<T extends BaseUI>(uiClass: UIClass<T>): T {\n        return this.uiMap.get(uiClass.getUrl()) as T;\n    }\n\n    /**\n     * 打开指定 UI 类的实例，支持传递参数\n     * @template T - 继承自 BaseUI 的类\n     * @param {UIClass<T>} uiClass - UI 类\n     * @param {...any[]} args - 传递给显示方法的参数\n     * @returns {Promise<void>} 打开完成的 Promise\n     */\n    public async openUI<T extends BaseUI>(uiClass: UIClass<T>, ...args: any[]): Promise<void> {\n        const startTime = Date.now();\n        // 加载 UI 实例\n        let ui: BaseUI = await this.loadUI(uiClass);\n        ui.node.active = true;\n        // 执行显示动画\n        await ui.fadeIn();\n        // 调用子类的显示方法\n        await ui.onShow(...args);\n        const cost = Date.now() - startTime;\n        console.debug(`[UIManager] openUI ${uiClass.getUrl()} cost ${cost}ms`);\n    }\n\n    /**\n     * 隐藏指定 UI 类的实例，支持传递参数，但不关闭\n     * @template T - 继承自 BaseUI 的类\n     * @param {UIClass<T>} uiClass - UI 类\n     * @param {...any[]} args - 传递给隐藏方法的参数\n     * @returns {Promise<void>} 隐藏完成的 Promise\n     */\n    public async hideUI<T extends BaseUI>(uiClass: UIClass<T>, ...args: any[]): Promise<void> {\n        // 从 uiMap 中获取 UI 实例\n        let ui: BaseUI = this.uiMap.get(uiClass.getUrl());\n        if (!ui) {\n            console.warn(\"[hideUI] uiMap not found\", uiClass.getUrl());\n            return;\n        }\n        const startTime = Date.now();\n        // 执行隐藏动画\n        await ui.fadeOut();\n        // 调用子类的隐藏方法\n        await ui.onHide(...args);\n        // 隐藏节点\n        ui.node.active = false;\n        const cost = Date.now() - startTime;\n        console.debug(`[UIManager] hideUI ${uiClass.getUrl()} cost ${cost}ms`);\n    }\n\n    /**\n     * 关闭指定 UI 类的实例，支持传递参数\n     * @template T - 继承自 BaseUI 的类\n     * @param {UIClass<T>} uiClass - UI 类\n     * @param {...any[]} args - 传递给关闭方法的参数\n     * @returns {Promise<void>} 关闭完成的 Promise\n     */\n    public async closeUI<T extends BaseUI>(uiClass: UIClass<T>, ...args: any[]): Promise<void> {\n        // 从 uiMap 中获取 UI 实例\n        let ui: BaseUI = this.uiMap.get(uiClass.getUrl());\n        if (!ui) {\n            console.warn(\"[UIManager] closeUI uiMap not found\", uiClass.getUrl());\n            return;\n        }\n        const startTime = Date.now();\n        // 执行隐藏动画\n        await ui.fadeOut();\n        // 调用子类的隐藏方法\n        await ui.onHide(...args);\n        // 调用子类的关闭方法\n        await ui.onClose(...args);\n        // 隐藏节点\n        ui.node.active = false;\n        // 销毁节点\n        ui.node.destroy();\n        // 从 uiMap 中删除该 UI 实例\n        this.uiMap.delete(uiClass.getUrl());\n        const cost = Date.now() - startTime;\n        console.debug(`[UIManager] closeUI ${uiClass.getUrl()} cost ${cost}ms`);\n    }\n\n    private addClickBgHandler<T extends BaseUI>(ui: BaseUI, uiClass: UIClass<T>) {\n        if (!uiClass.getUIOption().isClickBgHideUI &&\n            !uiClass.getUIOption().isClickBgCloseUI) {\n            return;\n        }\n        let blockInputEvents = ui.getComponent(BlockInputEvents);\n        if (!blockInputEvents) {\n            blockInputEvents = ui.node.addComponent(BlockInputEvents);\n        }\n        let btn = ui.node.getComponent(ButtonPlus);\n        if (!btn) {\n            btn = ui.node.addComponent(ButtonPlus);\n        }\n        btn.clickDefZoomScale = false;\n        btn.addClick((event: EventTouch) => {\n            if (uiClass.getUIOption().isClickBgHideUI) {\n                this.hideUI(uiClass)\n            } else if (uiClass.getUIOption().isClickBgCloseUI) {\n                this.closeUI(uiClass)\n            }\n        }, ui)\n    }\n}\n\n// 导出 UIManager 实例\nexport const UIMgr = UIManager.Instance;\n// 将 UIMgr 挂载到 window 对象上，方便全局访问\nwindow[\"UIMgr\"] = UIMgr;"]}
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, warn, SingletonBase, GameConst, EnemyWave, GameIns, Tools, BossBase, MyApp, WaveManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyWave(extras) {
    _reporterNs.report("EnemyWave", "../data/EnemyWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossBase(extras) {
    _reporterNs.report("BossBase", "../ui/plane/boss/BossBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStageData(extras) {
    _reporterNs.report("StageData", "../data/StageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "../ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "../wave/Wave", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      warn = _cc.warn;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      EnemyWave = _unresolved_4.EnemyWave;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      Tools = _unresolved_6.Tools;
    }, function (_unresolved_7) {
      BossBase = _unresolved_7.default;
    }, function (_unresolved_8) {
      MyApp = _unresolved_8.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a93d1u5aJFFpIGyblYeA0H7", "WaveManager", undefined);

      __checkObsolete__(['warn']);

      _export("default", WaveManager = class WaveManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        get enemyCreateAble() {
          return this._bEnemyCreateAble;
        }

        set enemyCreateAble(value) {
          this._bEnemyCreateAble = value;
        }

        constructor() {
          super(); // this.initConfig();

          this._waveNorDatasMap = new Map();
          // 波次配表数据
          this._enemyOver = false;
          //是否加载所有敌机
          this._bEnemyCreateAble = false;
          //是否可以创建敌机
          this._bEnemyNorCreateAble = false;
          //是否可以创建普通敌机
          this._enemyActions = null;
          //小阶段所有数据列表
          this._enemyActionIndex = 0;
          //当前小阶段索引
          this._enemyCreateTime = 0;
          //当前小阶段时间
          this._curEnemyAction = null;
          //当前波次数据
          this._waveCreateTime = 0;
          this._waveIndexOver = [];
          this._waveIndex = 0;
          //当前波次的索引
          this._waveArr = [];
          //当前波次的所有敌机数据
          this._waveNumArr = [];
          //当前波次已创建的敌机数量
          this._waveTimeArr = [];
          //当前波次计时
          this._waveActionArr = [];
          //boss
          this._bossCreateDelay = 0;
          this._bossCreateTime = 0;
          this._bossToAddArr = [];
          this._bShowBossWarning = false;
        }

        initConfig() {
          var waveDatas = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbWave.getDataList();

          for (var waveData of waveDatas) {
            var wave = new (_crd && EnemyWave === void 0 ? (_reportPossibleCrUseOfEnemyWave({
              error: Error()
            }), EnemyWave) : EnemyWave)();
            wave.loadJson(waveData);
            var group = this._waveNorDatasMap.get(wave.enemyGroupID) || [];
            group.push(wave);

            this._waveNorDatasMap.set(wave.enemyGroupID, group);
          }
        }

        reset() {
          this._enemyOver = false;
          this._enemyActions = [];
          this._enemyActionIndex = 0;
          this._enemyCreateTime = 0;
          this._bEnemyCreateAble = false;
          this._bEnemyNorCreateAble = false;
          this._waveIndex = 0;
          this._waveCreateTime = 0;

          this._waveIndexOver.splice(0);

          this._curEnemyAction = null;
          this._waveArr = [];
          this._waveActionArr = [];
          this._waveNumArr = [];
          this._waveTimeArr = [];
          this._bShowBossWarning = false;
          this._bossCreateTime = 0;
        }

        setEnemyActions(actions) {
          this._enemyActions = actions;
        }

        gameStart() {
          this._bEnemyCreateAble = true;
          this._bEnemyNorCreateAble = true;
          this._waveArr = [];
          this._waveActionArr = [];
          this._waveNumArr = [];
          this._waveTimeArr = [];
        }

        getNorWaveDatas(groupID) {
          return this._waveNorDatasMap.get(groupID);
        }

        addWaveByLevel(wave, posX, posY) {
          var enemyWave = (_crd && EnemyWave === void 0 ? (_reportPossibleCrUseOfEnemyWave({
            error: Error()
          }), EnemyWave) : EnemyWave).fromLevelWave(wave, posX, posY);

          this._waveArr.push(enemyWave);

          this._waveNumArr.push(0);

          this._waveTimeArr.push(0);

          this._waveActionArr.push(this._curEnemyAction);

          var group = this._waveNorDatasMap.get(enemyWave.enemyGroupID);

          if (group == null) {
            this._waveNorDatasMap.set(enemyWave.enemyGroupID, [enemyWave]);
          } else {
            group.push(enemyWave);
          }
        }

        updateGameLogic(deltaTime) {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this._updateCurAction(deltaTime);

            yield _this._updateEnemy(deltaTime);

            _this._updateBoss(deltaTime);
          })();
        }
        /**
         * 更新当前敌人行为
         * @param deltaTime 每帧的时间增量
         */


        _updateCurAction(deltaTime) {
          if (!this._enemyOver) {
            var _this$_enemyActions;

            if (this._enemyActionIndex >= (((_this$_enemyActions = this._enemyActions) == null ? void 0 : _this$_enemyActions.length) || 0)) {
              this._enemyOver = true;
              warn("enemy over");
            } else if (this.enemyCreateAble && !this._curEnemyAction) {
              var action = this._enemyActions[this._enemyActionIndex];

              switch (action.type) {
                case 0:
                  this._enemyCreateTime += deltaTime;

                  if (this._enemyCreateTime >= action.enemyNorInterval || this._waveArr.length === 0 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).enemyManager.getNormalPlaneCount() === 0) {
                    this._curEnemyAction = action;
                  }

                  break;

                default:
                  if (action.type >= 100) {
                    console.warn("Boss stage", action.type, action.enemyNorIDs[0]);
                    this._bossCreateDelay = action.enemyNorInterval;
                    (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                      error: Error()
                    }), GameIns) : GameIns).bossManager.loadBossRes(action.type, action.enemyNorIDs[0]);

                    this._bossToAddArr.push(action);

                    this._enemyActionIndex++;
                  }

              }
            }
          }
        }
        /**
         * 更新敌人逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateEnemy(deltaTime) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            yield _this2._updateEnemyCreate(deltaTime);

            if (_this2._curEnemyAction) {
              if (!_this2._updateNorEnemys(deltaTime)) {
                _this2._curEnemyAction = null;
                _this2._enemyActionIndex++;
                _this2._enemyCreateTime = 0;
              }
            }
          })();
        }
        /**
         * 更新敌人生成逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateEnemyCreate(deltaTime) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            for (var i = 0; i < _this3._waveArr.length; i++) {
              var wave = _this3._waveArr[i];
              _this3._waveTimeArr[i] += deltaTime;
              var currentEnemyCount = _this3._waveNumArr[i];
              var posX = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).EnemyPos.x;
              var posY = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).EnemyPos.y;

              if (wave.bSetStartPos) {
                posX += wave.startPosX;
                posY += wave.startPosY;
              }

              var expPerEnemy = Math.floor(wave.exp / wave.enemyNum);

              for (var j = currentEnemyCount; j < wave.enemyNum; j++) {
                if (wave.enemyInterval * (j + 1) < _this3._waveTimeArr[i]) {
                  _this3._waveNumArr[i]++;
                  var enemy = void 0;
                  var enemyPosX = posX + wave.posDX * (j + 1);
                  var enemyPosY = posY + wave.posDY * (j + 1);

                  switch (wave.type) {
                    case 0:
                      enemy = yield (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                        error: Error()
                      }), GameIns) : GameIns).enemyManager.addPlane(wave.enemyID);

                      if (enemy) {
                        if (j < wave.firstShootDelay.length) {
                          enemy.setFirstShootDelay(wave.firstShootDelay[j]);
                        }

                        enemy.setStandByTime(0);
                        enemy.setExp(expPerEnemy);
                        enemy.initPropertyRate(_this3._waveActionArr[i].enemyNorRate);
                        enemy.initTrack(wave.trackGroups, wave.liveParam, enemyPosX, enemyPosY, wave.rotateSpeed); // if (
                        //     wave.normalLoot &&
                        //     Tools.arrContain(wave.normalLoot.enemys, j + 1)
                        // ) {
                        //     enemy.addLoot(
                        //         GameIns.lootManager.getLootData(wave.normalLoot.lootId)
                        //     );
                        // }
                      }

                      break;
                  }
                }
              }

              if (wave.enemyNum <= _this3._waveNumArr[i]) {
                _this3._waveArr.splice(i, 1);

                _this3._waveNumArr.splice(i, 1);

                _this3._waveTimeArr.splice(i, 1);

                _this3._waveActionArr.splice(i, 1);

                i--;
              }
            }
          })();
        }
        /**
         * 更新普通敌人生成逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateNorEnemys(deltaTime) {
          if (this._bEnemyNorCreateAble) {
            if (this._waveIndex >= this._curEnemyAction.enemyNorIDs.length) {
              this._waveIndex = 0;
              return false;
            }

            var waveID = this._curEnemyAction.enemyNorIDs[this._waveIndex];
            this._waveCreateTime += deltaTime;
            var waveDatas = this.getNorWaveDatas(waveID);

            if (!waveDatas) {
              return false;
            }

            console.log("ybgg waveID:" + waveID + " waveDatas length:" + waveDatas.length);

            for (var i = 0; i < waveDatas.length; i++) {
              var wave = waveDatas[i];

              if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).arrContain(this._waveIndexOver, i) && this._waveCreateTime >= wave.groupInterval) {
                this._waveArr.push(wave);

                this._waveNumArr.push(0);

                this._waveTimeArr.push(0);

                this._waveActionArr.push(this._curEnemyAction);

                this._waveIndexOver.push(i);
              }
            }

            if (this._waveIndexOver.length >= waveDatas.length) {
              this._waveIndexOver.splice(0);

              this._waveCreateTime = 0;
              this._waveIndex++;
            }
          }

          return true;
        }
        /**
         * 更新 Boss 生成逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateBoss(deltaTime) {
          if (this._bossToAddArr.length > 0 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.isEnemyOver() && (this._bShowBossWarning || (this._bShowBossWarning = true, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.bossWillEnter()))) {
            this._bossCreateTime += deltaTime;

            if (this._bossCreateTime > this._bossCreateDelay && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.bossResFinish) {
              var bossData = this._bossToAddArr[0];
              var boss = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.addBoss(bossData.type, bossData.enemyNorIDs[0]);

              if (boss instanceof (_crd && BossBase === void 0 ? (_reportPossibleCrUseOfBossBase({
                error: Error()
              }), BossBase) : BossBase)) {
                // if (GameIns.battleManager.isGameType(GameEnum.GameType.Boss)) {
                //     boss.setPropertyRate(BossBattleManager.getPropertyRate());
                // } else {
                boss.setPropertyRate(bossData.enemyNorRate); // }

                boss.setTip((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).stageManager.getBossTips());
              }

              this._bossToAddArr.splice(0, 1);
            }
          }
        }
        /**
         * 检查敌人是否全部结束
         * @returns 是否所有敌人都已结束
         */


        isEnemyOver() {
          return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=306f4ef5168c37dfbe31ffc70272141c32a88206.js.map
import { LevelDataEventTrigger, LevelDataEventTriggerType } from "./LevelDataEventTrigger";

import { LevelDataEventTriggerAudio } from "./LevelDataEventTriggerAudio";
import { LevelDataEventTriggerLog } from "./LevelDataEventTriggerLog";
import { LevelDataEventTriggerWave } from "./LevelDataEventTriggerWave";

export function newTrigger(obj: {_type: LevelDataEventTriggerType}): LevelDataEventTrigger {
    var trigger: LevelDataEventTrigger;
    switch(obj._type) {
        case LevelDataEventTriggerType.Log:
            trigger = Object.assign(new LevelDataEventTriggerLog(), obj)
            break
        case LevelDataEventTriggerType.Audio:
            trigger = Object.assign(new LevelDataEventTriggerAudio(), obj)
            break
        case LevelDataEventTriggerType.Wave:
            trigger = Object.assign(new LevelDataEventTriggerWave(), obj)
            break
    }
    trigger.fromJSON(obj)
    return trigger
}
declare class GizmoConfig {
    /**
     *  3D gizmo可见性（TRS,Rect,Terrain gizmo始终可见） https://github.com/cocos/3d-tasks/issues/17333#issuecomment-1658000197
     */
    static toolsVisibility3d: boolean;
    /**
     * 是否使用3D Icon
     */
    static isIconGizmo3D: boolean;
    /**
     * Gizmo Icon 的大小
     */
    static iconGizmoSize: number;
}
export default GizmoConfig;
//# sourceMappingURL=gizmo-config.d.ts.map
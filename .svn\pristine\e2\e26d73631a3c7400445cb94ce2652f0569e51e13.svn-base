import { _decorator, Component, Sprite, Vec2, macro, Color, Size, Tween, UITransform, tween, UIOpacity, SpriteFrame, SpriteAtlas } from 'cc';
import { GameConst } from '../../const/GameConst';

const { ccclass, property } = _decorator;

@ccclass('PfFrameAnim')
export default class PfFrameAnim extends Component {
    @property(Sprite)
    sprite: Sprite | null = null;

    atlas: SpriteAtlas | null = null; // 图集
    imageSrc = ''; // 图片前缀
    count = 0; // 帧数
    duration = 0; // 每帧持续时间
    imageArr: SpriteFrame[] = []; // 存储帧的数组
    playTimes = 0; // 当前播放次数
    maxPlayTimes = macro.REPEAT_FOREVER; // 最大播放次数
    time = 0; // 当前帧时间
    index = 0; // 当前帧索引
    willStop = false; // 是否即将停止
    selfUpdate = true; // 是否自动更新
    call: (() => void) | null = null;
    stopCall: (() => void) | null = null;
    resetCall: (() => void) | null = null;
    sWidth = -1; // 自定义宽度
    sHeight = -1; // 自定义高度


    /**
     * 初始化帧动画
     * @param {SpriteAtlas} atlas 图集
     * @param {string} imageSrc 图片前缀
     * @param {number} count 帧数
     * @param {number} duration 每帧持续时间
     * @param {Function} call 播放完成回调
     * @param {Function} stopCall 停止回调
     * @param {Function} resetCall 重置回调
     */
    init(atlas: SpriteAtlas, 
        imageSrc: string, 
        count: number, 
        duration: number, 
        call: (() => void) | null = null,
        stopCall: (() => void) | null = null,
        resetCall: (() => void) | null = null
    ) {
        this.node.getComponent(UIOpacity)!.opacity = 0;
        this.atlas = atlas;
        this.imageSrc = imageSrc;
        this.count = count;
        this.duration = duration;
        this.call = call;
        this.resetCall = resetCall;
        this.stopCall = stopCall;

        for (let i = 0; i < count; i++) {
            this.imageArr.push(this.atlas!.getSpriteFrame(imageSrc + i)!);
        }

        if (!this.sprite) {
            this.sprite = this.node.addComponent(Sprite)!;
            this.sprite!.sizeMode = Sprite.SizeMode.RAW;
            this.sprite!.trim = false;
        }
    }

    /**
     * 设置混合模式
     */
    dstBlendFactor() {
        // this.sprite.dstBlendFactor = macro.ONE;
    }

    /**
     * 设置大小
     * @param {Size} size 大小
     * @param {number} mode 模式（1: 自定义大小，2: 切片模式）
     */
    setSize(size: Size, mode: number = 1) {
        if (mode === 1) {
            this.sprite!.sizeMode = Sprite.SizeMode.CUSTOM;
            this.node!.getComponent(UITransform)!.setContentSize(size);
        } else if (mode === 2) {
            this.imageArr.forEach((frame) => {
                frame.insetBottom = 20;
                frame.insetTop = 20;
                frame.insetLeft = 20;
                frame.insetRight = 20;
            });
            this.sprite!.type = Sprite.Type.SLICED;
            this.sprite!.sizeMode = Sprite.SizeMode.CUSTOM;
            this.node!.getComponent(UITransform)!.setContentSize(size);
        }
    }

    /**
     * 设置颜色
     * @param {Color} color 颜色
     */
    setColor(color: Color) {
        this.sprite!.color = color;
    }

    /**
     * 重置动画
     * @param {number} maxPlayTimes 最大播放次数
     */
    reset(maxPlayTimes = 0) {
        this.playTimes = 0;
        this.maxPlayTimes = maxPlayTimes > 0 ? maxPlayTimes : macro.REPEAT_FOREVER;
        this.time = 0;
        this.index = 0;
        this.sprite!.spriteFrame = this.imageArr[0];
        this.node.getComponent(UIOpacity)!.opacity = 255;
        if (this.resetCall) {
            this.resetCall();
        }
        this.willStop = false;
    }

    /**
     * 停止动画
     * @param {boolean} fadeOut 是否渐隐停止
     */
    stop(fadeOut = false) {
        if (fadeOut) {
            Tween.stopAllByTarget(this.node);
            tween(this.node!.getComponent(UIOpacity)!)
                .to(4 * GameConst.ActionFrameTime, { opacity: 0 })
                .start();
        } else {
            this.node!.getComponent(UIOpacity)!.opacity = 0;
        }
        if (this.stopCall) {
            this.stopCall();
        }
        this.willStop = false;
    }

    /**
     * 设置即将停止
     */
    setWillStop() {
        this.willStop = true;
    }

    /**
     * 检查是否正在播放
     * @returns {boolean} 是否正在播放
     */
    isPlaying() {
        return this.node!.getComponent(UIOpacity)!.opacity > 100;
    }

    /**
     * 更新动画逻辑
     * @param {number} dt 时间增量
     */
    updateGameLogic(dt: number) {
        if (this.node!.getComponent(UIOpacity)!.opacity > 0) {
            this.time += dt;
            if (this.time >= this.duration) {
                this.index++;
                if (this.index >= this.count) {
                    this.playTimes++;
                    this.index = 0;
                    if (this.call) {
                        this.call();
                    }
                    if (this.willStop) {
                        this.stop();
                    }
                    if (this.playTimes >= this.maxPlayTimes) {
                        this.stop();
                    }
                } else {
                    this.time = 0;
                    this.sprite!.spriteFrame = this.imageArr[this.index];
                    if (this.sWidth >= 0) {
                        this.sprite!.node!.getComponent(UITransform)!.width = this.sWidth;
                    }
                    if (this.sHeight >= 0) {
                        this.sprite!.node!.getComponent(UITransform)!.height = this.sHeight;
                    }
                }
            }
        }
    }

    /**
     * 每帧更新
     * @param {number} dt 时间增量
     */
    update(dt: number) {
        if (GameConst.GameAble) {
            this.updateGameLogic(dt);
        }
    }
}
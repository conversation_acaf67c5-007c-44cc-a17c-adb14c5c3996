'use strict';

import { existsSync } from 'fs';
import { join } from 'path';

type Selector<$> = { $: Record<keyof $, any | null> }

export const template = `
<ui-prop type="dump" class="bulletID"></ui-prop>
<ui-prop type="dump" class="emitterData"></ui-prop>
<ui-prop type="dump" class="bulletData"></ui-prop>
<div class="event-group-section">
    <ui-label value="Event Groups"></ui-label>
    <div class="event-group-list"></div>
    <ui-button class="add-event-group">Add Event Group</ui-button>
</div>
`;

export const $ = {
    bulletID: '.bulletID',
    emitterData: '.emitterData',
    bulletData: '.bulletData',
    eventGroupList: '.event-group-list',
    addEventGroupBtn: '.add-event-group',
};

// Define your bullet data source
const BULLET_DATA = [
    { id: 1, name: "普通子弹", description: "基础攻击子弹" },
    { id: 2, name: "穿透子弹", description: "可穿透敌人的子弹" },
    { id: 3, name: "爆炸子弹", description: "爆炸范围伤害" },
    { id: 4, name: "追踪子弹", description: "自动追踪目标" },
    // Add more bullet types as needed
];

type EmitterPanel = Selector<typeof $> & {
    dump: any;
    eventGroupElements: HTMLElement[];
    renderEventGroupList(): void;
};

// Helper function to check if EventGroupData file exists
function checkEventGroupFileExists(eventGroupName: string): boolean {
    if (!eventGroupName) return false;

    const projectPath = Editor.Project.path;
    const emitterPath = join(projectPath, 'assets', 'resources', 'Game', 'emitter', 'events', 'Emitter', `${eventGroupName}.json`);
    const bulletPath = join(projectPath, 'assets', 'resources', 'Game', 'emitter', 'events', 'Bullet', `${eventGroupName}.json`);

    return existsSync(emitterPath) || existsSync(bulletPath);
}

// Helper function to create event group item element
function createEventGroupItem(this: EmitterPanel, eventGroupName: string, index: number): HTMLElement {
    const container = document.createElement('div');
    container.className = 'event-group-item';
    container.style.cssText = `
        display: flex;
        align-items: center;
        margin: 2px 0;
        padding: 4px;
        border: 1px solid ${checkEventGroupFileExists(eventGroupName) ? '#555' : '#ff4444'};
        border-radius: 3px;
        background: #2a2a2a;
    `;

    // Input field for event group name
    const input = document.createElement('input');
    input.type = 'text';
    input.value = eventGroupName;
    input.style.cssText = `
        flex: 1;
        margin-right: 4px;
        padding: 2px 4px;
        background: #1a1a1a;
        border: 1px solid #555;
        color: #fff;
        border-radius: 2px;
    `;

    input.addEventListener('input', () => {
        const newValue = input.value;
        // Update the dump data
        if (this.dump && this.dump.value.emitterData && this.dump.value.emitterData.value.eventGroupData) {
            this.dump.value.emitterData.value.eventGroupData.value[index] = newValue;
            // Update border color based on file existence
            container.style.borderColor = checkEventGroupFileExists(newValue) ? '#555' : '#ff4444';
            // Commit the change
            this.dump.value.emitterData.value.eventGroupData.dispatch('change');
        }
    });

    // Open button
    const openBtn = document.createElement('button');
    openBtn.textContent = '📂';
    openBtn.title = 'Open in Event Editor';
    openBtn.style.cssText = `
        margin-right: 4px;
        padding: 2px 6px;
        background: #4a90e2;
        border: none;
        color: white;
        border-radius: 2px;
        cursor: pointer;
        font-size: 12px;
    `;

    openBtn.addEventListener('click', () => {
        // Send message to open event editor panel with this event group
        Editor.Message.send('bullet_editor', 'open-event-editor', eventGroupName);
    });

    // Remove button
    const removeBtn = document.createElement('button');
    removeBtn.textContent = '✕';
    removeBtn.title = 'Remove Event Group';
    removeBtn.style.cssText = `
        padding: 2px 6px;
        background: #e74c3c;
        border: none;
        color: white;
        border-radius: 2px;
        cursor: pointer;
        font-size: 12px;
    `;

    removeBtn.addEventListener('click', () => {
        if (this.dump && this.dump.value.emitterData && this.dump.value.emitterData.value.eventGroupData) {
            // Remove from array
            this.dump.value.emitterData.value.eventGroupData.value.splice(index, 1);
            // Commit the change
            this.dump.value.emitterData.value.eventGroupData.dispatch('change');
            // Re-render the list
            renderEventGroupList.call(this);
        }
    });

    container.appendChild(input);
    container.appendChild(openBtn);
    container.appendChild(removeBtn);

    return container;
}

export function update(this: EmitterPanel, dump: any) {
    this.dump = dump;
    this.eventGroupElements = [];

    // Render other emitter properties
    this.$.bulletID.render(dump.value.bulletID);
    this.$.emitterData.render(dump.value.emitterData);
    this.$.bulletData.render(dump.value.bulletData);

    // Render event group list
    renderEventGroupList.call(this);
}

export function ready(this: EmitterPanel) {
    // Setup add event group button
    if (this.$.addEventGroupBtn) {
        this.$.addEventGroupBtn.addEventListener('click', () => {
            if (this.dump && this.dump.value.emitterData && this.dump.value.emitterData.value.eventGroupData) {
                // Add new empty event group
                this.dump.value.emitterData.value.eventGroupData.value.push('');
                // Commit the change
                this.dump.value.emitterData.value.eventGroupData.dispatch('change');
                // Re-render the list
                renderEventGroupList.call(this);
            }
        });
    }
}

// Add method to render event group list
function renderEventGroupList(this: EmitterPanel) {
    if (!this.$.eventGroupList) return;

    // Clear existing elements
    this.$.eventGroupList.innerHTML = '';
    this.eventGroupElements = [];

    // Get event group data
    const eventGroupData = this.dump?.value?.emitterData?.value?.eventGroupData?.value;
    if (!eventGroupData || !Array.isArray(eventGroupData)) return;

    // Create elements for each event group
    eventGroupData.forEach((eventGroupName: string, index: number) => {
        const element = createEventGroupItem.call(this, eventGroupName, index);
        this.eventGroupElements.push(element);
        this.$.eventGroupList.appendChild(element);
    });
}
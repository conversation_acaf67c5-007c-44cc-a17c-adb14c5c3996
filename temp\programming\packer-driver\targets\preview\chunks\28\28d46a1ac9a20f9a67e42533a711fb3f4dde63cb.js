System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, JsonAsset, resources, view, GameIns, GameConst, GameEnum, MyApp, LevelBaseUI, LevelData, _dec, _class, _class2, _crd, ccclass, property, RAND_STRATEGY, PRELOAD_STATE, PRELOAD_LEVEL_COUNT, GameMapRun;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfbuiltin(extras) {
    _reporterNs.report("builtin", "../../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfChapter(extras) {
    _reporterNs.report("Chapter", "../../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelBaseUI(extras) {
    _reporterNs.report("LevelBaseUI", "./LevelBaseUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "../../../leveldata/leveldata", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      JsonAsset = _cc.JsonAsset;
      resources = _cc.resources;
      view = _cc.view;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.default;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      LevelBaseUI = _unresolved_6.LevelBaseUI;
    }, function (_unresolved_7) {
      LevelData = _unresolved_7.LevelData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "879aeDtOuZIk7CiSj2Twdv6", "GameMapRun", undefined);

      __checkObsolete__(['_decorator', 'Component', 'JsonAsset', 'resources', 'view']); // import ExchangeMap from '../base/ExchangeMap';


      ({
        ccclass,
        property
      } = _decorator);

      RAND_STRATEGY = /*#__PURE__*/function (RAND_STRATEGY) {
        RAND_STRATEGY[RAND_STRATEGY["WEIGHT_PRUE"] = 1] = "WEIGHT_PRUE";
        RAND_STRATEGY[RAND_STRATEGY["WEIGHT_NO_REPEAT"] = 2] = "WEIGHT_NO_REPEAT";
        RAND_STRATEGY[RAND_STRATEGY["ORDER"] = 3] = "ORDER";
        return RAND_STRATEGY;
      }(RAND_STRATEGY || {});

      PRELOAD_STATE = /*#__PURE__*/function (PRELOAD_STATE) {
        PRELOAD_STATE[PRELOAD_STATE["NONE"] = 0] = "NONE";
        PRELOAD_STATE[PRELOAD_STATE["LOADING"] = 1] = "LOADING";
        PRELOAD_STATE[PRELOAD_STATE["LOADED"] = 2] = "LOADED";
        return PRELOAD_STATE;
      }(PRELOAD_STATE || {});

      PRELOAD_LEVEL_COUNT = 1;

      /*
       * @description 加载策略：
       * 根据当前移动到的关卡，提前加载下一关的关卡（目前只提前加载的关卡数定为1）
      */
      _export("default", GameMapRun = (_dec = ccclass('GameMapRun'), _dec(_class = (_class2 = class GameMapRun extends Component {
        constructor() {
          super(...arguments);
          this._levelList = [];
          this._chapterData = undefined;
          this._lastSelectedId = -1;
          this._initOver = false;
          this._preloadState = PRELOAD_STATE.NONE;
          this._levelLoadIndex = 0;
          // 当前关卡加载索引
          this._levelIndex = 0;
          // 当前关卡索引（实时移动到的）
          this._levelUIInfoList = [];
          //已经加载的关卡的基本信息
          this._levelTotalDuration = 0;
          // 关卡总持续时间
          this._levelTotalHeight = 0;
          // 关卡总高度
          this._levelDistance = 0;
          // 当前关卡移动的距离
          this._levelDuration = 0;
          // 当前关卡的持续时间
          this._levelSpeed = 0;
        }

        // 当前关卡的移动速度
        get MapSpeed() {
          return this._levelSpeed;
        }

        get ViewTop() {
          return view.getVisibleSize().height * -0.5;
        }

        onLoad() {
          GameMapRun.instance = this;
        } // 根据策略随机出关卡列表


        _initLevelList(chapterID) {
          this._chapterData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbChapter.get(70002);
          /*(chapterID)*/

          ;

          if (this._chapterData == null) {
            console.log('GameMapRun', " chapterData is null");
            return;
          } // 随机出关卡组


          var levelGroupList = this._randomSelection(this._chapterData.strategyList, this._chapterData.levelGroupCount, this._chapterData.strategy);

          if (levelGroupList.length === 0) {
            console.log('GameMapRun', " levelGroupList is null");
            return;
          } // 随机出关卡


          this._levelList = [];

          for (var levelGroupID of levelGroupList) {
            var levelGroupData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbLevelGroup.get(levelGroupID);

            if (levelGroupData == null) {
              console.log('GameMapRun', " levelGroupData is null");
              continue;
            }

            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));

            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));
          }

          console.log('GameMapRun', ' _levelList ', this._levelList);
        }

        initData(chapterID) {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.reset();

            _this._initLevelList(chapterID);

            yield _this._loadNextLevelPrefab(true);

            _this._initCurLevelData();

            var levelBaseUI = _this.node.getComponent(_crd && LevelBaseUI === void 0 ? (_reportPossibleCrUseOfLevelBaseUI({
              error: Error()
            }), LevelBaseUI) : LevelBaseUI);

            if (levelBaseUI) {
              yield new Promise(resolve => {
                // 确保 LevelBaseUI 的初始化完成（例如背景加载）
                var check = () => {
                  if (levelBaseUI.backgroundLayer.backgrounds.length > 0) {
                    resolve();
                  } else {
                    setTimeout(check, 100); // 轮询检查
                  }
                };

                check();
              });
            }

            _this._initOver = true;
            _this._preloadState = PRELOAD_STATE.LOADED;
          })();
        }

        initBattle() {}

        update(deltaTime) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          }

          if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667;
          }

          var gameState = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState;

          if (gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Sortie && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Ready && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.WillOver && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Idle && gameState !== (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over) {
            return;
          }

          if (!this._initOver) {
            return;
          }

          this._tick(deltaTime);

          this._checkNextLevelLoading();
        }

        clear() {// 清理加载的资源
        }

        mapEndChange() {// // 处理地图结束切换
          // ExchangeMap.me.endChange();
        }

        reset() {
          // 重置地图数据
          this.node.removeAllChildren();
          this._initOver = false;
          this._levelLoadIndex = -1;
          this._levelIndex = 0;
          this._levelDistance = 0;
          this._levelTotalDuration = 0;
          this._levelTotalHeight = 0;
          this._levelDuration = 0;
          this._levelList = [];
          this._levelUIInfoList = [];
          this._chapterData = undefined;
          this._lastSelectedId = -1;
        }
        /**
         * 策略：
         * 1.严格按权重比例随机选择元素
         * 2.严格按权重比例随机选择元素，不重复
         * 3.按顺序选择元素
         * @param STList 带权重的元素数组
         * @param count 需要选择的元素数量
         * @returns 选中元素的ID数组
         */


        _randomSelection(STList, count, strategy) {
          if (STList.length === 0 || count <= 0) return [];
          var results = [];

          if (strategy === RAND_STRATEGY.WEIGHT_PRUE) {
            // 计算总权重
            var totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0); // 如果所有权重都为0，则转为均匀随机

            if (totalWeight === 0) {
              for (var i = 0; i < count; i++) {
                var randomIndex = Math.floor(Math.random() * STList.length);
                results.push(STList[randomIndex].ID);
              }

              return results;
            } // 严格按权重比例随机选择


            for (var _i = 0; _i < count; _i++) {
              // 生成[0, totalWeight)区间的随机数
              var randomValue = Math.random() * totalWeight; // 遍历查找随机数对应的元素

              var cumulativeWeight = 0;

              for (var item of STList) {
                cumulativeWeight += item.Weight;

                if (randomValue < cumulativeWeight) {
                  results.push(item.ID);
                  break;
                }
              }
            }
          } else if (strategy === RAND_STRATEGY.WEIGHT_NO_REPEAT) {
            // 计算总权重
            var _totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0); // 如果所有权重都为0，则转为均匀随机


            if (_totalWeight === 0) {
              for (var _i2 = 0; _i2 < count; _i2++) {
                var _randomIndex = Math.floor(Math.random() * STList.length); // 避免重复选择相同的ID


                if (_i2 > 0 && STList[_randomIndex].ID === results[_i2 - 1]) {
                  // 如果与上一次选择的相同，选择下一个（循环）
                  _randomIndex = (_randomIndex + 1) % STList.length;
                }

                results.push(STList[_randomIndex].ID);
              }

              return results;
            } // 创建副本以避免修改原始数据


            var tempList = [...STList];

            for (var _i3 = 0; _i3 < count; _i3++) {
              // 如果上一次选择的ID存在，且它在当前列表中，调整其位置
              if (this._lastSelectedId !== -1) {
                var lastSelectedIndex = tempList.findIndex(item => item.ID === this._lastSelectedId);

                if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {
                  // 将上一次选择的ID与下一个元素交换位置
                  [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] = [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];
                }
              } // 生成[0, totalWeight)区间的随机数


              var _randomValue = Math.random() * _totalWeight; // 遍历查找随机数对应的元素


              var _cumulativeWeight = 0;
              var selectedIndex = -1;

              for (var j = 0; j < tempList.length; j++) {
                _cumulativeWeight += tempList[j].Weight;

                if (_randomValue < _cumulativeWeight) {
                  selectedIndex = j;
                  break;
                }
              } // 如果未找到有效索引，选择最后一个元素


              if (selectedIndex === -1) {
                selectedIndex = tempList.length - 1;
              } // 获取选中的ID


              var selectedId = tempList[selectedIndex].ID;
              results.push(selectedId); // 更新上一次选择的ID

              this._lastSelectedId = selectedId;
            }
          } else if (strategy === RAND_STRATEGY.ORDER) {
            // 按顺序选择元素，遇到ID为0时从数组开头重新开始
            var currentIndex = 0;

            for (var _i4 = 0; _i4 < count; _i4++) {
              // 如果当前元素的ID为0，则重置到数组开头
              if (STList[currentIndex].ID === 0) {
                currentIndex = 0; // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素

                while (currentIndex < STList.length && STList[currentIndex].ID === 0) {
                  currentIndex++;
                } // 如果所有元素ID都为0，则无法选择，跳出循环


                if (currentIndex >= STList.length) {
                  break;
                }
              } // 选择当前元素


              results.push(STList[currentIndex].ID); // 移动到下一个元素

              currentIndex++; // 如果到达数组末尾，回到开头

              if (currentIndex >= STList.length) {
                currentIndex = 0;
              }
            }
          }

          return results;
        }

        _loadNextLevelPrefab(bFristLevel) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (bFristLevel === void 0) {
              bFristLevel = false;
            }

            if (_this2._levelIndex >= _this2._chapterData.levelCount || _this2._levelIndex >= _this2._levelList.length) {
              console.log('GameMapRun', ' no level to load');
              return;
            } // 计算实际需要加载的关卡数量


            var remainingLevels = _this2._chapterData.levelCount - (_this2._levelLoadIndex + PRELOAD_LEVEL_COUNT);
            console.log('GameMapRun', ' levelCount:', _this2._chapterData.levelCount, 'this._levelLoadIndex:', _this2._levelLoadIndex, 'remainingLevels:', remainingLevels);
            var levelsToLoad = Math.min(PRELOAD_LEVEL_COUNT, remainingLevels);

            if (levelsToLoad <= 0) {
              console.log('GameMapRun', ' no level to load');
              return;
            }

            _this2._levelLoadIndex += levelsToLoad;
            var loadPromises = [];

            var _loop = function* _loop() {
              var levelID = _this2._levelList[_this2._levelLoadIndex + i];
              console.log('GameMapRun', ' ----- this._levelLoadIndex:', _this2._levelLoadIndex);
              var levelConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).lubanTables.TbLevel.get(levelID);

              if (levelConfig == null) {
                console.log('GameMapRun', ' level data not found', levelID);
                return {
                  v: void 0
                };
              }

              var prefabName = "Game/level/" + levelConfig.prefab;
              var loadPromise = new Promise(resolve => {
                resources.load(prefabName, JsonAsset, /*#__PURE__*/_asyncToGenerator(function* (err, prefab) {
                  if (err) {
                    console.error('GameMapRun', '加载关卡预制体失败:', err);
                    _this2._levelLoadIndex -= levelsToLoad;
                    resolve();
                    return;
                  }

                  var levelBaseUI = _this2.node.getComponent(_crd && LevelBaseUI === void 0 ? (_reportPossibleCrUseOfLevelBaseUI({
                    error: Error()
                  }), LevelBaseUI) : LevelBaseUI); //const nodeLayer = new Node(`chapter${this._chapterData.id}`);


                  if (levelBaseUI == null) {
                    levelBaseUI = _this2.node.addComponent(_crd && LevelBaseUI === void 0 ? (_reportPossibleCrUseOfLevelBaseUI({
                      error: Error()
                    }), LevelBaseUI) : LevelBaseUI);
                  }

                  var levelInfo = {
                    levelID: levelID,
                    levelCount: _this2._chapterData.levelCount,
                    levelIndex: _this2._levelLoadIndex
                  };
                  var levelData = (_crd && LevelData === void 0 ? (_reportPossibleCrUseOfLevelData({
                    error: Error()
                  }), LevelData) : LevelData).fromJSON(prefab == null ? void 0 : prefab.json);

                  if (bFristLevel) {
                    yield levelBaseUI.levelPrefab(levelData, levelInfo, bFristLevel);
                  } else {
                    levelBaseUI.levelPrefab(levelData, levelInfo, bFristLevel);
                  } //this.node.addChild(nodeLayer);


                  var levelBaseUIInfo = _this2._initLevelInfo(levelID, levelData.totalTime, levelData.backgroundLayer.speed);

                  _this2._levelUIInfoList.push(levelBaseUIInfo);

                  console.log('GameMapRun', '加载关卡:', levelID, prefabName);
                  resolve();
                }));
              });
              loadPromises.push(loadPromise);
            },
                _ret;

            for (var i = 1; i <= levelsToLoad; i++) {
              _ret = yield* _loop();
              if (_ret) return _ret.v;
            }

            if (bFristLevel) {
              yield Promise.all(loadPromises); // 首次加载需要等待

              _this2._preloadState = PRELOAD_STATE.LOADED;
            } else {
              yield Promise.all(loadPromises).then(() => {
                _this2._preloadState = PRELOAD_STATE.LOADED;
                console.log('GameMapRun', '关卡预加载完成');
              }).catch(err => {
                console.error('后台预加载失败:', err);
              });
            }
          })();
        }

        _checkNextLevelLoading() {
          if (this._preloadState === PRELOAD_STATE.LOADED && this._levelIndex + PRELOAD_LEVEL_COUNT > this._levelLoadIndex) {
            console.log('GameMapRun', ' 开始加载到关卡:', this._levelIndex + PRELOAD_LEVEL_COUNT, 'PRELOAD_STATE:', this._preloadState, 'this._levelLoadIndex:', this._levelLoadIndex);
            this._preloadState = PRELOAD_STATE.LOADING;

            this._loadNextLevelPrefab().catch(err => {
              console.error('GameMapRun', ' Background loading failed:', err);
            });
          }
        }

        _initCurLevelData() {
          if (this._levelIndex >= this._chapterData.levelCount) {
            console.error('GameMapRun', ' no level to init');
            return;
          }

          var levelBaseUI = this.node.getComponent(_crd && LevelBaseUI === void 0 ? (_reportPossibleCrUseOfLevelBaseUI({
            error: Error()
          }), LevelBaseUI) : LevelBaseUI);
          var levelBase = this._levelUIInfoList[this._levelIndex];
          this._levelDistance = 0;
          this._levelTotalHeight = levelBaseUI.getLevelTotalHeightByIndex(this._levelIndex);
          levelBaseUI.switchLevel(levelBase.speed, levelBase.totalTime, this._levelIndex);
          this._levelSpeed = levelBase.speed;
          this._levelDuration = 0;
        }

        _tick(deltaTime) {
          if (this._levelIndex >= this._chapterData.levelCount) {
            console.error('GameMapRun', ' no level to tick');
            return;
          }

          if (this._levelTotalHeight <= 0) {
            return;
          }

          this._levelDuration += deltaTime;
          this._levelTotalDuration += deltaTime;
          this._levelDistance += this.MapSpeed * deltaTime;
          var levelBaseUI = this.node.getComponent(_crd && LevelBaseUI === void 0 ? (_reportPossibleCrUseOfLevelBaseUI({
            error: Error()
          }), LevelBaseUI) : LevelBaseUI);

          if (levelBaseUI != null) {
            //console.log('GameMapRun',' tick level', levelData.levelID, this._levelDuration);
            levelBaseUI.tick(deltaTime);
            var targetDistance = this._levelTotalHeight;

            if (this._levelIndex >= this._chapterData.levelCount - 1) {
              targetDistance -= view.getVisibleSize().height;
            }

            if (this._levelDistance >= targetDistance) {
              this._levelIndex++;
              console.log('GameMapRun', '关卡完成:', this._levelIndex - 1, 'levelDistance:', this._levelDistance, 'levelTotalHeight:', this._levelTotalHeight);

              this._initCurLevelData();
            }
          }
        }

        _initLevelInfo(levelID, time, speed) {
          return {
            levelID: levelID,
            totalTime: time,
            speed: speed
          };
        }

      }, _class2.instance = null, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=28d46a1ac9a20f9a67e42533a711fb3f4dde63cb.js.map
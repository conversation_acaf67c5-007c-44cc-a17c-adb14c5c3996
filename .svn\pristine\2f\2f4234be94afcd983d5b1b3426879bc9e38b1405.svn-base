'use strict';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const path_1 = __importDefault(require("path"));
exports.template = `
<ui-prop type="dump" class="progress"></ui-prop>
<ui-prop type="dump" class="levelPrefab"></ui-prop>
<ui-prop type="dump" class="save"></ui-prop>
<ui-prop type="dump" class="play"></ui-prop>
<ui-prop type="dump" class="levelPrefabUUID"></ui-prop>
<ui-prop >
    <ui-button class="btn-save">保存(ctrl+t)</ui-button>
    <ui-button class="btn-new">新建关卡</ui-button>
    <ui-button class="btn-play">播放</ui-button>
</ui-prop>
`;
exports.$ = {
    progress: '.progress',
    levelPrefab: '.levelPrefab',
    save: '.save',
    levelPrefabUUID: '.levelPrefabUUID',
    btnSave: '.btn-save',
    btnNew: '.btn-new',
    btnPlay: '.btn-play',
    play: '.play',
};
function update(dump) {
    // 使用 ui-porp 自动渲染，设置 prop 的 type 为 dump
    // render 传入一个 dump 数据，能够自动渲染出对应的界面
    // 自动渲染的界面修改后，能够自动提交数据
    this.dump = dump;
    this.$.progress.render(dump.value.progress);
    this.$.levelPrefab.render(dump.value.levelPrefab); // 这个是 levelPrefab 的 uuid，需要转成 JsonAsset 才能用
    this.$.save.dump = dump.value.save;
    this.$.play.dump = dump.value.play;
    this.$.levelPrefabUUID.dump = dump.value.levelPrefabUUID;
    // this.$.save.render(dump.value.save);
}
function ready() {
    this.$.btnSave.addEventListener('confirm', () => {
        console.log("panel save level");
        // @ts-ignore
        this.dump.value.save.value = true;
        this.$.save.dispatch('change-dump');
    });
    this.$.btnNew.addEventListener('confirm', async () => {
        console.log("panel new level:", Editor.Project.path);
        // Editor.Panel.open('level-editor.newlevel')
        const dirPath = path_1.default.join(Editor.Project.path, "assets", "resources", "Game", "level");
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Level', extensions: ['json'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path_1.default.relative(dirPath, retData.filePath);
        console.log("panel new level name:", name);
        const filePath = `db://assets/resources/Game/level/${name}`;
        var createRsp = await Editor.Message.request('asset-db', 'create-asset', filePath, "{}");
        console.log("panel new level create asset rsp:", createRsp);
        // @ts-ignore
        this.dump.value.levelPrefabUUID.value = createRsp === null || createRsp === void 0 ? void 0 : createRsp.uuid;
        this.$.levelPrefabUUID.dispatch('change-dump');
        // Editor.Message.send('level-editor', 'new-level', name)
    });
    this.$.btnPlay.addEventListener('confirm', () => {
        console.log("panel play level");
        // @ts-ignore
        this.dump.value.play.value = true;
        this.$.play.dispatch('change-dump');
    });
}
//# sourceMappingURL=data:application/json;base64,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
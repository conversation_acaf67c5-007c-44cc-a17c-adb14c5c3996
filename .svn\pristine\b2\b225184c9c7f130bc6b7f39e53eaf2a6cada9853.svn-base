'use strict';

import path from "path";

type Selector<$> = { $: Record<keyof $, any | null> }

export const template = `
<ui-prop type="dump" class="wavePrefab"></ui-prop>
<ui-prop type="dump" class="planeID"></ui-prop>
<ui-prop type="dump" class="params"></ui-prop>
<ui-prop type="dump" class="conditions"></ui-prop>
<ui-section>
    <ui-prop slot="header">
        <ui-label slot="label">Conditions</ui-label>
        <ui-num-input slot="content" class="conditionCount" min="0" max="10" step="1"> </ui-num-input>
    </ui-prop>
</ui-section>
`;


export const $ = {
    wavePrefab: '.wavePrefab',
    planeID: '.planeID',
    params: '.params',
    conditions: '.conditions',
    conditionCount: '.conditionCount'
};

// @ts-ignore
export function update(this: Selector<typeof $> & typeof methods, dump: any) {
    // 使用 ui-porp 自动渲染，设置 prop 的 type 为 dump
    // render 传入一个 dump 数据，能够自动渲染出对应的界面
    // 自动渲染的界面修改后，能够自动提交数据
    this.dump = dump
    this.$.wavePrefab.render(dump.value.wavePrefab);
    this.$.planeID.render(dump.value.planeID);
    this.$.params.render(dump.value.params);
    this.$.conditions.render(dump.value.conditions);
}

// @ts-ignore
export function ready(this: Selector<typeof $> & typeof methods) {
}

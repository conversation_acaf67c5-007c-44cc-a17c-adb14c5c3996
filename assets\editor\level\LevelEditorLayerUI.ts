import { _decorator, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LevelDataEvent, LevelDataLayer, LevelDataWave } from 'db://assets/scripts/leveldata/leveldata';

import { LevelEditorUtils } from './utils';
import { LevelEditorWaveUI } from './LevelEditorWaveUI';
import { LevelEditorEventUI } from './LevelEditorEventUI';

const TerrainsNodeName = "terrains";
const DynamicNodeName = "dynamic";
const WaveNodeName = "waves";
const EventNodeName = "events"

@ccclass('LevelEditorLayerUI')
@executeInEditMode()
export class LevelEditorLayerUI extends Component {
    public terrainsNode: Node|null = null;
    public dynamicNode: Node|null = null;
    public wavesNode: Node|null = null;
    public eventsNode: Node|null = null;

    onLoad(): void {
        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);
        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);
        this.wavesNode = LevelEditorUtils.getOrAddNode(this.node, WaveNodeName);
        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);
    }

    public initByLevelData(data: LevelDataLayer):void {
        console.log("LevelEditorLayerUI initByLevelData")
        if (!data) {
            return;
        }
        data.terrains?.forEach((terrain) => {
            assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load terrain prefab err", err);
                    return
                } 
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
                this.terrainsNode!.addChild(terrainNode);                
            });
        });
        data.waves?.forEach((wave)=>{
            var node = new Node();
            var waveUIComp = node.addComponent(LevelEditorWaveUI);
            waveUIComp.initByLevelData(wave);
            this.wavesNode!.addChild(node);
        })
        data.events?.forEach((event)=>{
            var node = new Node();
            var eventUIComp = node.addComponent(LevelEditorEventUI);
            eventUIComp.initByLevelData(event);
            this.eventsNode!.addChild(node);
        })
    }

    public fillLevelData(data: LevelDataLayer):void {
        console.log("LevelEditorLayerUI fillLevelData")
        data.terrains = []
        this.terrainsNode!.children.forEach((terrainNode) => {
            data.terrains.push({
                // @ts-ignore
                uuid: terrainNode._prefab.asset._uuid,
                position: new Vec2(terrainNode.position.x, terrainNode.position.y),
                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),
                rotation: terrainNode.rotation.z
            })
        })
        data.waves = []
        this.wavesNode!.children.forEach((waveNode) => {
            var wave = new LevelDataWave()
            var waveUIComp = waveNode.getComponent(LevelEditorWaveUI);
            waveUIComp!.fillLevelData(wave)
            data.waves.push(wave)
        })
        data.events = []
        this.eventsNode!.children.forEach((eventNode) => {
            var event = new LevelDataEvent()
            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);
            eventUIComp!.fillLevelData(event)
            data.events.push(event)
        })
    }

    public tick(progress: number, totalTime:number, speed:number):void {
        this.node.setPosition(0, - progress * totalTime * speed, 0);
    }
}
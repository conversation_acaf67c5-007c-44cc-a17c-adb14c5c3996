    import { join } from 'path';

    // @ts-ignore
    module.paths.push(join(Editor.App.path, 'node_modules'));

    export function load() {};
    export function unload() {};

    export const methods = {
      saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if(levelEditorUI){
            levelEditorUI.save = true;
        }
      }
    };
{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts"], "names": ["_decorator", "Entity", "ccclass", "property", "EnemyEntity", "_type", "_isDead", "_active", "_removeAble", "_attack", "_collideAtk", "_sceneLayer", "reset", "type", "value", "attack", "setCollideAtk", "getColliderAtk", "collideAtk", "isDead", "removeAble", "active", "sceneLayer", "hurt", "damage", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,M;;;;;;;;;OAGD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,W,WADpBF,OAAO,CAAC,aAAD,C,gBAAR,MACqBE,WADrB;AAAA;AAAA,4BACgD;AAAA;AAAA;AAAA,eAE5CC,KAF4C,GAEpC,CAFoC;AAEjC;AAFiC,eAG5CC,OAH4C,GAGlC,KAHkC;AAG3B;AAH2B,eAI5CC,OAJ4C,GAIlC,KAJkC;AAI3B;AAJ2B,eAK5CC,WAL4C,GAK9B,KAL8B;AAKvB;AALuB,eAM5CC,OAN4C,GAMlC,CANkC;AAM/B;AAN+B,eAO5CC,WAP4C,GAO9B,CAP8B;AAO3B;AAP2B,eAQ5CC,WAR4C,GAQ9B,CAAC,CAR6B;AAAA;;AAQ1B;;AAGlB;AACJ;AACA;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKN,OAAL,GAAe,KAAf;AACA,eAAKC,OAAL,GAAe,KAAf;AACA,eAAKC,WAAL,GAAmB,KAAnB;AACA,eAAKG,WAAL,GAAmB,CAAC,CAApB;AACH;AAED;AACJ;AACA;AACA;;;AACY,YAAJE,IAAI,GAAG;AACP,iBAAO,KAAKR,KAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACY,YAAJQ,IAAI,CAACC,KAAD,EAAQ;AACZ,eAAKT,KAAL,GAAaS,KAAb;AACH;AAED;AACJ;AACA;AACA;;;AACc,YAANC,MAAM,GAAG;AACT,iBAAO,KAAKN,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACc,YAANM,MAAM,CAACD,KAAD,EAAQ;AACd,eAAKL,OAAL,GAAeK,KAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,aAAa,CAACF,KAAD,EAAgB;AACzB,eAAKJ,WAAL,GAAmBI,KAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,cAAc,GAAG;AACb,iBAAO,KAAKP,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACkB,YAAVQ,UAAU,GAAG;AACb,iBAAO,KAAKR,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACkB,YAAVQ,UAAU,CAACJ,KAAD,EAAQ;AAClB,eAAKJ,WAAL,GAAmBI,KAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACc,YAANK,MAAM,GAAG;AACT,iBAAO,KAAKb,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACc,YAANa,MAAM,CAACL,KAAD,EAAQ;AACd,eAAKR,OAAL,GAAeQ,KAAf;AACH;AAED;AACJ;AACA;AACA;;;AACkB,YAAVM,UAAU,GAAG;AACb,iBAAO,KAAKZ,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACkB,YAAVY,UAAU,CAACN,KAAD,EAAQ;AAClB,eAAKN,WAAL,GAAmBM,KAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACc,YAANO,MAAM,GAAG;AACT,iBAAO,KAAKd,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACc,YAANc,MAAM,CAACP,KAAD,EAAQ;AACd,eAAKP,OAAL,GAAeO,KAAf;AACH;AAED;AACJ;AACA;AACA;;;AACkB,YAAVQ,UAAU,GAAG;AACb,iBAAO,KAAKX,WAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACkB,YAAVW,UAAU,CAACR,KAAD,EAAQ;AAClB,eAAKH,WAAL,GAAmBG,KAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACIS,QAAAA,IAAI,CAACC,MAAD,EAAiB,CAAE;AAEvB;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG,CAAE;;AA9J4B,O", "sourcesContent": ["import { _decorator, instantiate } from \"cc\";\r\nimport Entity from \"../../base/Entity\";\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyEntity')\r\nexport default class EnemyEntity extends Entity {\r\n\r\n    _type = 0; // 敌人类型\r\n    _isDead = false; // 是否死亡\r\n    _active = false; // 是否激活\r\n    _removeAble = false; // 是否可移除\r\n    _attack = 0; // 攻击力\r\n    _collideAtk = 0; // 碰撞攻击力\r\n    _sceneLayer = -1; // 场景层\r\n\r\n\r\n    /**\r\n     * 重置敌人状态\r\n     */\r\n    reset() {\r\n        this._isDead = false;\r\n        this._active = false;\r\n        this._removeAble = false;\r\n        this._sceneLayer = -1;\r\n    }\r\n\r\n    /**\r\n     * 获取敌人类型\r\n     * @returns {number} 敌人类型\r\n     */\r\n    get type() {\r\n        return this._type;\r\n    }\r\n\r\n    /**\r\n     * 设置敌人类型\r\n     * @param {number} value 敌人类型\r\n     */\r\n    set type(value) {\r\n        this._type = value;\r\n    }\r\n\r\n    /**\r\n     * 获取攻击力\r\n     * @returns {number} 攻击力\r\n     */\r\n    get attack() {\r\n        return this._attack;\r\n    }\r\n\r\n    /**\r\n     * 设置攻击力\r\n     * @param {number} value 攻击力\r\n     */\r\n    set attack(value) {\r\n        this._attack = value;\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞攻击力\r\n     * @param {number} value 碰撞攻击力\r\n     */\r\n    setCollideAtk(value: number) {\r\n        this._collideAtk = value;\r\n    }\r\n\r\n    /**\r\n     * 获取碰撞攻击力\r\n     * @returns {number} 碰撞攻击力\r\n     */\r\n    getColliderAtk() {\r\n        return this._collideAtk;\r\n    }\r\n\r\n    /**\r\n     * 获取碰撞攻击力\r\n     * @returns {number} 碰撞攻击力\r\n     */\r\n    get collideAtk() {\r\n        return this._collideAtk;\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞攻击力\r\n     * @param {number} value 碰撞攻击力\r\n     */\r\n    set collideAtk(value) {\r\n        this._collideAtk = value;\r\n    }\r\n\r\n    /**\r\n     * 获取是否死亡\r\n     * @returns {boolean} 是否死亡\r\n     */\r\n    get isDead() {\r\n        return this._isDead;\r\n    }\r\n\r\n    /**\r\n     * 设置是否死亡\r\n     * @param {boolean} value 是否死亡\r\n     */\r\n    set isDead(value) {\r\n        this._isDead = value;\r\n    }\r\n\r\n    /**\r\n     * 获取是否可移除\r\n     * @returns {boolean} 是否可移除\r\n     */\r\n    get removeAble() {\r\n        return this._removeAble;\r\n    }\r\n\r\n    /**\r\n     * 设置是否可移除\r\n     * @param {boolean} value 是否可移除\r\n     */\r\n    set removeAble(value) {\r\n        this._removeAble = value;\r\n    }\r\n\r\n    /**\r\n     * 获取是否激活\r\n     * @returns {boolean} 是否激活\r\n     */\r\n    get active() {\r\n        return this._active;\r\n    }\r\n\r\n    /**\r\n     * 设置是否激活\r\n     * @param {boolean} value 是否激活\r\n     */\r\n    set active(value) {\r\n        this._active = value;\r\n    }\r\n\r\n    /**\r\n     * 获取场景层\r\n     * @returns {number} 场景层\r\n     */\r\n    get sceneLayer() {\r\n        return this._sceneLayer;\r\n    }\r\n\r\n    /**\r\n     * 设置场景层\r\n     * @param {number} value 场景层\r\n     */\r\n    set sceneLayer(value) {\r\n        this._sceneLayer = value;\r\n    }\r\n\r\n    /**\r\n     * 处理敌人受到的伤害\r\n     * @param {number} damage 伤害值\r\n     */\r\n    hurt(damage: number) {}\r\n\r\n    /**\r\n     * 敌人即将销毁时的回调\r\n     */\r\n    willDestroy() {}\r\n\r\n}"]}
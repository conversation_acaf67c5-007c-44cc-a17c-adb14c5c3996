import { _decorator, Enum, CCFloat } from 'cc';
const { ccclass, property} = _decorator;

import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from 'db://assets/scripts/leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionDelayTime } from 'db://assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime';
import { LevelDataEventCondtionDelayDistance } from 'db://assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance';
import { LevelDataEventCondtionWave } from 'db://assets/scripts/leveldata/condition/LevelDataEventCondtionWave';
import { newCondition } from 'db://assets/scripts/leveldata/condition/newCondition';

import { LevelEditorElemUI } from './LevelEditorElemUI';

@ccclass('LevelEditorCondition')
export class LevelEditorCondition {
    public _index = 0;
    public data : LevelDataEventCondtion = new LevelDataEventCondtionDelayTime(LevelDataEventCondtionComb.And);

    @property({
        type:Enum(LevelDataEventCondtionComb),
        visible() {
            // @ts-ignore
            return this._index != 0;
        }
    })
    public get comb(): LevelDataEventCondtionComb {
        return this.data.comb;
    }
    public set comb(value: LevelDataEventCondtionComb) {
        this.data.comb = value;
    }

    @property({
        type:Enum(LevelDataEventCondtionType),
    })
    public get type(): LevelDataEventCondtionType {
        return this.data._type;
    }
    public set type(value: LevelDataEventCondtionType) {
        if (this.data._type != value) {
            this.data = newCondition({comb: this.data.comb, _type: value});
        }
    }

    @property({
        type :CCFloat,
        visible () {
            return this.type == LevelDataEventCondtionType.DelayTime ;
        }
    })
    public get delayTime(): number {
        return (this.data as LevelDataEventCondtionDelayTime).time;
    }
    public set delayTime(value: number) {
        (this.data as LevelDataEventCondtionDelayTime).time = value;
    }

    @property({
        type :CCFloat,
        visible () {
            return this.type == LevelDataEventCondtionType.DelayDistance;
        }
    })
    public get delayDistance(): number {
        return (this.data as LevelDataEventCondtionDelayDistance).distance;
    }
    public set delayDistance(value: number) {
        (this.data as LevelDataEventCondtionDelayDistance).distance = value;
    }

    public _targetElem: LevelEditorElemUI | null = null;
    @property({
        type: LevelEditorElemUI,
        visible () {
            return this.type == LevelDataEventCondtionType.Wave;
        }
    })
    public get targetElem(): LevelEditorElemUI | null {
         return this._targetElem;
    }
    public set targetElem(value: LevelEditorElemUI | null) { 
        this._targetElem = value;
        (this.data as LevelDataEventCondtionWave).targetElemID = value?.elemID ?? "";
    }
}
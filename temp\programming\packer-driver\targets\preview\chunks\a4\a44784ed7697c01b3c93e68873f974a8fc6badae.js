System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, SingletonBase, GameConst, GameEnum, GameIns, GameMapRun, UIMgr, LoadingUI, BattleManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMapRun(extras) {
    _reporterNs.report("GameMapRun", "../ui/map/GameMapRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoadingUI(extras) {
    _reporterNs.report("LoadingUI", "../../ui/LoadingUI", _context.meta, extras);
  }

  _export("BattleManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      GameMapRun = _unresolved_6.default;
    }, function (_unresolved_7) {
      UIMgr = _unresolved_7.UIMgr;
    }, function (_unresolved_8) {
      LoadingUI = _unresolved_8.LoadingUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "64d867wWTtD/LPANmmLR3y9", "BattleManager", undefined);

      _export("BattleManager", BattleManager = class BattleManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super(...arguments);
          this._percent = 0;
          this.gameType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameType.Common;
          this.initBattleEnd = false;
          this.gameStart = false;
          this.animSpeed = 1;
          this._gameTime = 0;
          this.mainStage = 0;
          this.subStage = 0;
          this._loadFinish = false;
          this._loadTotal = 0;
          this._loadCount = 0;
        }

        mainReset() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.mainReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.mainReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainReset();
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.reset();
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.reset();
        }

        subReset() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.subReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.subReset();
        }
        /**
         * 检查所有资源是否加载完成
         */


        checkLoadFinish() {
          this._loadCount++;
          var loadingUI = (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).get(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
            error: Error()
          }), LoadingUI) : LoadingUI);
          loadingUI.updateProgress(this._loadCount / this._loadTotal);

          if (this._loadCount >= this._loadTotal) {
            this.initBattle();
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
              error: Error()
            }), LoadingUI) : LoadingUI);
          }
        }

        addLoadCount(count) {
          this._loadTotal += count;
        }

        startLoading(mainStage, subStage) {
          if (subStage === void 0) {
            subStage = 1;
          }

          this.mainStage = mainStage;
          this.subStage = subStage;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameSortie();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameResManager.preload();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.preload();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.preLoad(mainStage); //子弹资源

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.preLoad(); //伤害特效资源

          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.initData(mainStage); //地图背景初始化

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.preLoad(mainStage); //敌人资源

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.preLoad(); //boss资源
        }

        initBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).stageManager.initBattle(this.mainStage, this.subStage);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.initBattle(this.mainStage, this.subStage);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.initBattle();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.planeIn();
        }

        onPlaneIn() {
          this.initBattleEnd = true;
          this.beginBattle();
        }

        beginBattle() {
          if (this.initBattleEnd && !this.gameStart) {
            this.gameStart = true;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).stageManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameRuleManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainPlane.begine(true);
          }
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 每帧的时间间隔
         */


        update(dt) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          }

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isGameOver()) {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).planeManager) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).planeManager.enemyTarget = null;
            }

            return;
          }

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isInBattle() || (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isGameWillOver()) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).planeManager.update(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameRuleManager.updateGameLogic(dt);
            this._gameTime += dt;
          } else if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).planeManager) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).planeManager.enemyTarget = null;
          }
        }
        /**
         * 战斗失败逻辑
         */


        battleDie() {
          return _asyncToGenerator(function* () {
            // GameFunc.addDialog(ReplayUI.default);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameRuleManager.gamePause();
          })();
        } //     /**
        //      * 战斗复活逻辑
        //      */
        //     relifeBattle() {
        //         GameIns.eventManager.emit(GameEvent.MainRelife);
        //         GameIns.gameRuleManager.gameResume();
        //     }

        /**
         * 战斗失败结算
         */


        battleFail() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMainUI.showGameResult(false);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.hpBar.active = false;
          this.endBattle();
        }
        /**
         * 战斗胜利逻辑
         */


        battleSucc() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.hpBar.active = false;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.stopFire();
          this.endBattle();

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).stageManager.checkStage(this.mainStage, this.subStage + 1)) {
            this.startNextBattle();
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMainUI.showGameResult(true);
          }
        }
        /**
        * 继续下一场战斗
        */


        startNextBattle() {
          this.subReset();
          this.subStage += 1;
          this.initBattle();
        }
        /**
         * 结束战斗
         */


        endBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameOver();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.removeAll(false, true);
          this.gameStart = false;
          this.initBattleEnd = false;
        }
        /**
         * Boss切换完成
         * @param {string} bossName Boss名称
         */


        bossChangeFinish(bossName) {// const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
          // if (bossEnterDialog) {
          //     bossEnterDialog.node.active = true;
          //     GameIns.mainPlaneManager.moveAble = false;
          //     bossEnterDialog.showTips(bossName);
          // }
        }

        bossWillEnter() {//        GameIns.mainPlaneManager.fireEnable = false;
          //        GameIns.mainPlaneManager.moveAble = false;
          //         WinePlaneManager.default.me.pauseBattle();
          //         const inGameUI = GameIns.uiManager.getDialog(InGameUI.default);
          //         if (inGameUI) {
          //             inGameUI.hideUI();
          //         }
          //         const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
          //         if (bossEnterDialog) {
          //             if (!bossEnterDialog.node.parent) {
          //                 GameIns.uiManager.addDialog(BossEnterDialog.default, bossEnterDialog);
          //             }
          //             bossEnterDialog.node.active = true;
          //             bossEnterDialog.play();
          //         }
          //         GameIns.audioManager.playbg("bg_3");
        }
        /**
         * 开始Boss战斗
         */


        bossFightStart() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable = true;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.moveAble = true;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.bossFightStart();
        }
        /**
         * 获取屏幕比例
         * @returns {number} 屏幕比例
         */


        getRatio() {
          return 0.666667; // 固定比例值
        }

        isGameType(gameType) {
          return this.gameType == gameType;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a44784ed7697c01b3c93e68873f974a8fc6badae.js.map
import { LevelDataEventTrigger, LevelDataEventTriggerType } from "./LevelDataEventTrigger";

export class LevelDataEventTriggerWave extends LevelDataEventTrigger {
    public waveUUID: string = "";
    public planeID: number = -1;
    public params: Map<string, number> = new Map<string, number>();
    constructor() {
        super(LevelDataEventTriggerType.Wave);
    }
    public fromJSON(obj: any): void {
        super.fromJSON(obj);
        this.params = new Map(Object.entries(obj.params));
    }
}


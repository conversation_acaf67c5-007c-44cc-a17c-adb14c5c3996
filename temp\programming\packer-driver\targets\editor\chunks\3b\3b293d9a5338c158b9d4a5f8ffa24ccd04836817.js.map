{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterEventActions.ts"], "names": ["EmitterEventActionBase", "EmitterEventAction_Active", "EmitterEventAction_InitialDelay", "EmitterEventAction_Duration", "EmitterEventAction_ElapsedTime", "eEmitterProp", "Easing", "constructor", "data", "_isCompleted", "_elapsedTime", "_startValue", "isCompleted", "canLerp", "onLoad", "context", "onExecute", "dt", "duration", "executeInternal", "targetValue", "lerp<PERSON><PERSON>ue", "startValue", "lerp", "easing", "Math", "min", "value", "emitter", "setProperty", "IsActive", "initialDelay", "InitialDelay", "emitDuration", "EmitDuration", "totalElapsedTime", "TotalElapsedTime"], "mappings": ";;;oDAMaA,sB,EA+CAC,yB,EAiBAC,+B,EAYAC,2B,EAYAC,8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA7FJC,MAAAA,Y,iBAAAA,Y;;AACSC,MAAAA,M,iBAAAA,M;;;;;;;wCAILN,sB,GAAN,MAAMA,sBAAN,CAAqD;AAOxDO,QAAAA,WAAW,CAACC,IAAD,EAAwB;AAAA,eAN1BA,IAM0B;AAAA,eAJzBC,YAIyB,GAJD,KAIC;AAAA,eAHzBC,YAGyB,GAHF,CAGE;AAAA,eAFzBC,WAEyB,GAFH,CAEG;AAC/B,eAAKH,IAAL,GAAYA,IAAZ;AACH;;AAEDI,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKH,YAAZ;AACH;;AAEDI,QAAAA,OAAO,GAAY;AACf,iBAAO,IAAP;AACH;;AAEDC,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,eAAKN,YAAL,GAAoB,KAApB;AACA,eAAKC,YAAL,GAAoB,CAApB,CAFqC,CAGrC;;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACH;;AAEDK,QAAAA,SAAS,CAACD,OAAD,EAA6BE,EAA7B,EAA+C;AACpD,eAAKP,YAAL,IAAqBO,EAArB;;AACA,cAAI,KAAKP,YAAL,IAAqB,KAAKF,IAAL,CAAUU,QAAnC,EAA6C;AACzC,iBAAKC,eAAL,CAAqBJ,OAArB,EAA8B,KAAKP,IAAL,CAAUY,WAAxC;AACA,iBAAKX,YAAL,GAAoB,IAApB;AACH,WAHD,MAIK,IAAI,KAAKI,OAAL,EAAJ,EAAoB;AACrB,iBAAKM,eAAL,CAAqBJ,OAArB,EAA8B,KAAKM,SAAL,CAAe,KAAKV,WAApB,EAAiC,KAAKH,IAAL,CAAUY,WAA3C,CAA9B;AACH;AACJ;;AAEDC,QAAAA,SAAS,CAACC,UAAD,EAAqBF,WAArB,EAAkD;AACvD,iBAAO;AAAA;AAAA,gCAAOG,IAAP,CAAY,KAAKf,IAAL,CAAUgB,MAAtB,EAA8BF,UAA9B,EAA0CF,WAA1C,EAAuDK,IAAI,CAACC,GAAL,CAAS,GAAT,EAAc,KAAKhB,YAAL,GAAoB,KAAKF,IAAL,CAAUU,QAA5C,CAAvD,CAAP;AACH;;AAESC,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD,CACvE;AACH;;AA3CuD,O,GA8C5D;;;2CACa1B,yB,GAAN,MAAMA,yBAAN,SAAwCD,sBAAxC,CAA+D;AAClEa,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH,SAHiE,CAKlE;AACA;AACA;AACA;;;AAEUM,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvE;AACAZ,UAAAA,OAAO,CAACa,OAAR,CAAgBC,WAAhB,CAAqC;AAAA;AAAA,4CAAaC,QAAlD,EAA4DH,KAAK,KAAK,CAAtE;AACH;;AAbiE,O,GAgBtE;;;iDACazB,+B,GAAN,MAAMA,+BAAN,SAA8CF,sBAA9C,CAAqE;AACxEc,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,gBAAMD,MAAN,CAAaC,OAAb;AACA,eAAKJ,WAAL,GAAmBI,OAAO,CAACa,OAAR,CAAgBG,YAAhB,CAA6BJ,KAAhD;AACH;;AAESR,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,OAAR,CAAgBC,WAAhB,CAAoC;AAAA;AAAA,4CAAaG,YAAjD,EAA+DL,KAA/D;AACH;;AARuE,O,GAW5E;;;6CACaxB,2B,GAAN,MAAMA,2BAAN,SAA0CH,sBAA1C,CAAiE;AACpEc,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,gBAAMD,MAAN,CAAaC,OAAb;AACA,eAAKJ,WAAL,GAAmBI,OAAO,CAACa,OAAR,CAAgBK,YAAhB,CAA6BN,KAAhD;AACH;;AAESR,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,OAAR,CAAgBC,WAAhB,CAAoC;AAAA;AAAA,4CAAaK,YAAjD,EAA+DP,KAA/D;AACH;;AARmE,O,GAWxE;;;gDACavB,8B,GAAN,MAAMA,8BAAN,SAA6CJ,sBAA7C,CAAoE;AACvEc,QAAAA,MAAM,CAACC,OAAD,EAAmC;AACrC,gBAAMD,MAAN,CAAaC,OAAb;AACA,eAAKJ,WAAL,GAAmBI,OAAO,CAACa,OAAR,CAAgBO,gBAAhB,CAAiCR,KAApD;AACH;;AAESR,QAAAA,eAAe,CAACJ,OAAD,EAA6BY,KAA7B,EAAkD;AACvEZ,UAAAA,OAAO,CAACa,OAAR,CAAgBC,WAAhB,CAAoC;AAAA;AAAA,4CAAaO,gBAAjD,EAAmET,KAAnE;AACH;;AARsE,O", "sourcesContent": ["import { IEventAction } from \"./IEventAction\";\r\nimport { eEmitterProp } from \"../Emitter\";\r\nimport { eEasing, Easing} from \"../Easing\";\r\nimport { EventGroupContext } from \"../EventGroup\";\r\nimport { EventActionData } from \"../../data/bullet/EventGroupData\";\r\n\r\nexport class EmitterEventActionBase implements IEventAction {\r\n    readonly data: EventActionData;\r\n\r\n    protected _isCompleted: boolean = false;\r\n    protected _elapsedTime: number = 0;\r\n    protected _startValue: number = 0;\r\n\r\n    constructor(data: EventActionData) {\r\n        this.data = data;\r\n    }\r\n\r\n    isCompleted(): boolean {\r\n        return this._isCompleted;\r\n    }\r\n\r\n    canLerp(): boolean {\r\n        return true;\r\n    }\r\n\r\n    onLoad(context: EventGroupContext): void {\r\n        this._isCompleted = false;\r\n        this._elapsedTime = 0;\r\n        // override this to get the correct start value\r\n        this._startValue = 0;\r\n    }\r\n\r\n    onExecute(context: EventGroupContext, dt: number): void {\r\n        this._elapsedTime += dt;\r\n        if (this._elapsedTime >= this.data.duration) {\r\n            this.executeInternal(context, this.data.targetValue);\r\n            this._isCompleted = true;\r\n        }\r\n        else if (this.canLerp()) {\r\n            this.executeInternal(context, this.lerpValue(this._startValue, this.data.targetValue));\r\n        }\r\n    }\r\n\r\n    lerpValue(startValue: number, targetValue: number): number {\r\n        return Easing.lerp(this.data.easing, startValue, targetValue, Math.min(1.0, this._elapsedTime / this.data.duration));\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // Default implementation does nothing\r\n    }\r\n}\r\n\r\n// 修改发射器启用状态\r\nexport class EmitterEventAction_Active extends EmitterEventActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n\r\n    // onLoad(context: EventGroupContext): void {\r\n    //     super.onLoad(context);\r\n    //     this._startValue = context.emitter.isActive.value ? 1 : 0;\r\n    // }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        // context.emitter.isActive = this.data.targetValue;\r\n        context.emitter.setProperty<boolean>(eEmitterProp.IsActive, value === 1);\r\n    }\r\n}\r\n\r\n// 修改发射器初始延迟时间\r\nexport class EmitterEventAction_InitialDelay extends EmitterEventActionBase {\r\n    onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._startValue = context.emitter.initialDelay.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.emitter.setProperty<number>(eEmitterProp.InitialDelay, value);\r\n    }\r\n}\r\n\r\n// 修改发射器持续时间\r\nexport class EmitterEventAction_Duration extends EmitterEventActionBase {\r\n    onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._startValue = context.emitter.emitDuration.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.emitter.setProperty<number>(eEmitterProp.EmitDuration, value);\r\n    }\r\n}\r\n\r\n// 修改发射器已运行时间\r\nexport class EmitterEventAction_ElapsedTime extends EmitterEventActionBase {\r\n    onLoad(context: EventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._startValue = context.emitter.totalElapsedTime.value;\r\n    }\r\n\r\n    protected executeInternal(context: EventGroupContext, value: number): void {\r\n        context.emitter.setProperty<number>(eEmitterProp.TotalElapsedTime, value);\r\n    }\r\n}\r\n\r\n"]}
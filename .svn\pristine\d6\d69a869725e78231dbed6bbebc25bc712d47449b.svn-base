import { _decorator, Component, Tween,Node, Animation } from 'cc';
import { GameConst } from '../../../const/GameConst';
const { ccclass, property } = _decorator;

@ccclass('EnemyAnim')
export default class EnemyAnim extends Component {
    @property(Animation)
    anim: Animation | null = null;

    private _animCallMap: Map<string, Function> = new Map();
    private _animEventCallMap: Map<string, Function> = new Map();
    private _tailFireArr: Node[] = [];
    private _curAnim: string = "";

    /**
     * 加载时的初始化
     */
    onLoad(): void {
        // this.anim.on("finished", this.onFinished, this);
    }

    /**
     * 初始化动画
     * @param tailFireData 尾焰数据
     */
    init(tailFireData: any[]): void {
        this._initTailFire(tailFireData);
    }

    /**
     * 初始化尾焰动画
     * @param tailFireData 尾焰数据
     */
    private _initTailFire(tailFireData: any[]): void {
        const frameTime = GameConst.ActionFrameTime;
        let fireIndex = 0;
        let dataIndex = 0;

        while (true) {
            const fireNode = this.node.getChildByName(`fire${fireIndex}`);
            if (!fireNode) break;

            for (let i = 0; i < fireNode.children.length; i++) {
                const child = fireNode.children[i];
                if (child.name !== "icon") {
                    Tween.stopAllByTarget(child);
                    const fireData = tailFireData[dataIndex++];
                    if (fireData) {
                        // const scaleSequence = sequence(
                        //     scaleTo(frameTime * fireData[5], fireData[6]),
                        //     scaleTo(frameTime * fireData[5], 1)
                        // );
                        // child.runAction(repeatForever(scaleSequence));
                    }
                }
            }
            fireIndex++;
        }

        for (let i = fireIndex; i < this._tailFireArr.length; i++) {
            const tailFire = this._tailFireArr[i];
            Tween.stopAllByTarget(tailFire);
            tailFire.active = false;
        }
    }

    /**
     * 动画播放完成的回调
     * @param event 动画事件
     */
    private onFinished(event: any): void {
        this._animCallMap.forEach((callback, animName) => {
            if (this._curAnim === animName && callback) {
                callback();
            }
        });
    }

    /**
     * 射击动画事件
     */
    onShoot(): void {
        const callback = this._animEventCallMap.get("shoot");
        if (callback) {
            callback();
        }
    }

    /**
     * 护盾动画事件
     */
    onShield(): void {
        const callback = this._animEventCallMap.get("shield");
        if (callback) {
            callback();
        }
    }

    /**
     * 播放动画
     * @param animName 动画名称
     * @param callback 动画完成后的回调
     */
    playAnim(animName: string, callback?: Function): void {
        // this._curAnim = animName;
        // this.anim.play();
        // this._animCallMap.set(animName, callback);
        callback?.();
    }

    /**
     * 设置动画事件回调
     * @param eventName 动画事件名称
     * @param callback 回调函数
     */
    setAnimEventCall(eventName: string, callback: Function): void {
        this._animEventCallMap.set(eventName, callback);
    }

    /**
     * 暂停动画
     */
    pauseAnim(): void {
        this.anim!.pause();
    }

    /**
     * 恢复动画
     */
    resumeAnim(): void {
        // this.anim.resume();
    }

    /**
     * 停止动画
     */
    stopAnim(): void {
        // this.anim.stop();
    }
}
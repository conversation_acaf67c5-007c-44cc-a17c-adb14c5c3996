System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, TypeID, TypeIDUtils, SystemContainer, _crd;

  function _reportPossibleCrUseOfSystem(extras) {
    _reporterNs.report("System", "./System", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWorld(extras) {
    _reporterNs.report("World", "./World", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTypeID(extras) {
    _reporterNs.report("TypeID", "./TypeID", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTypeIDUtils(extras) {
    _reporterNs.report("TypeIDUtils", "./TypeID", _context.meta, extras);
  }

  _export("SystemContainer", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      TypeID = _unresolved_2.TypeID;
      TypeIDUtils = _unresolved_2.TypeIDUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "237ca+9Uc5IWLGcoFi0jJ0M", "SystemContainer", undefined);

      /**
       * Type definition for system update functions
       */

      /**
       * SystemContainer manages a collection of game systems
       * Similar to MyApp's ManagerPool but specifically for game world systems
       */
      _export("SystemContainer", SystemContainer = class SystemContainer {
        constructor() {
          this._world = null;
          this._systems = [];
          this._systemRegistry = new (_crd && TypeIDUtils === void 0 ? (_reportPossibleCrUseOfTypeIDUtils({
            error: Error()
          }), TypeIDUtils) : TypeIDUtils).TypedRegistry();
          this._updateContainer = [];
          this._lateUpdateContainer = [];
          this._isInitialized = false;
        }

        /**
         * Register a system to the container
         * @param system The system to register
         * @returns true if registration was successful, false if system already exists
         */
        registerSystem(system) {
          const systemName = system.getTypeName();
          const typeId = system.getTypeId();

          if (this._systemRegistry.has(system.constructor)) {
            console.warn(`SystemContainer: System ${systemName} (TypeID: ${typeId}) is already registered`);
            return false;
          }

          this._systems.push(system);

          this._systemRegistry.register(system); // If container is already initialized, initialize the new system immediately


          if (this._isInitialized) {
            this._initializeSystem(system);
          }

          console.log(`SystemContainer: Registered system ${systemName} (TypeID: ${typeId})`);
          return true;
        }
        /**
         * Unregister a system from the container
         * @param systemConstructor The constructor of the system to unregister
         * @returns true if unregistration was successful, false if system not found
         */


        unregisterSystem(systemConstructor) {
          const system = this._systemRegistry.get(systemConstructor);

          if (!system) {
            const typeName = (_crd && TypeID === void 0 ? (_reportPossibleCrUseOfTypeID({
              error: Error()
            }), TypeID) : TypeID).getTypeName((_crd && TypeID === void 0 ? (_reportPossibleCrUseOfTypeID({
              error: Error()
            }), TypeID) : TypeID).get(systemConstructor));
            console.warn(`SystemContainer: System ${typeName} not found`);
            return false;
          }

          const systemName = system.getTypeName(); // Cleanup the system

          system.unInit(); // Remove from collections

          const index = this._systems.indexOf(system);

          if (index >= 0) {
            this._systems.splice(index, 1);

            this._updateContainer.splice(index, 1);

            this._lateUpdateContainer.splice(index, 1);
          }

          this._systemRegistry.remove(systemConstructor);

          console.log(`SystemContainer: Unregistered system ${systemName}`);
          return true;
        }
        /**
         * Get a system by type
         * @param systemConstructor The constructor of the system to get
         * @returns The system instance or null if not found
         */


        getSystem(systemConstructor) {
          return this._systemRegistry.get(systemConstructor);
        }
        /**
         * Check if a system is registered
         * @param systemConstructor The constructor of the system to check
         * @returns true if the system is registered
         */


        hasSystem(systemConstructor) {
          return this._systemRegistry.has(systemConstructor);
        }
        /**
         * Get all registered systems
         * @returns Array of all registered systems
         */


        getAllSystems() {
          return [...this._systems];
        }
        /**
         * Get the number of registered systems
         * @returns The number of registered systems
         */


        getSystemCount() {
          return this._systems.length;
        }
        /**
         * Initialize all registered systems
         */


        init(world) {
          if (this._isInitialized) {
            console.warn("SystemContainer: Already initialized");
            return;
          }

          console.log(`SystemContainer: Initializing ${this._systems.length} systems`);
          this._world = world; // Clear update containers

          this._updateContainer.length = 0;
          this._lateUpdateContainer.length = 0; // Initialize all systems

          this._systems.forEach(system => {
            this._initializeSystem(system);
          });

          this._isInitialized = true;
          console.log("SystemContainer: Initialization complete");
        }
        /**
         * Cleanup all systems
         */


        unInit() {
          if (!this._isInitialized) {
            return;
          }

          console.log("SystemContainer: Cleaning up systems"); // Cleanup all systems in reverse order

          for (let i = this._systems.length - 1; i >= 0; i--) {
            this._systems[i].unInit();
          } // Clear all containers


          this._systems.length = 0;

          this._systemRegistry.clear();

          this._updateContainer.length = 0;
          this._lateUpdateContainer.length = 0;
          this._isInitialized = false;
          console.log("SystemContainer: Cleanup complete");
        }
        /**
         * Update all systems
         * @param deltaTime Time elapsed since last frame in seconds
         */


        update(deltaTime) {
          if (!this._isInitialized) {
            return;
          }

          for (let i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
          }
        }
        /**
         * Late update all systems
         * @param deltaTime Time elapsed since last frame in seconds
         */


        lateUpdate(deltaTime) {
          if (!this._isInitialized) {
            return;
          }

          for (let i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i](deltaTime);
          }
        }
        /**
         * Enable or disable a specific system
         * @param systemConstructor The constructor of the system to enable/disable
         * @param enabled Whether to enable or disable the system
         * @returns true if the operation was successful
         */


        setSystemEnabled(systemConstructor, enabled) {
          const system = this._systemRegistry.get(systemConstructor);

          if (!system) {
            const typeName = (_crd && TypeID === void 0 ? (_reportPossibleCrUseOfTypeID({
              error: Error()
            }), TypeID) : TypeID).getTypeName((_crd && TypeID === void 0 ? (_reportPossibleCrUseOfTypeID({
              error: Error()
            }), TypeID) : TypeID).get(systemConstructor));
            console.warn(`SystemContainer: System ${typeName} not found`);
            return false;
          }

          const systemName = system.getTypeName();
          system.setEnabled(enabled);
          console.log(`SystemContainer: System ${systemName} ${enabled ? 'enabled' : 'disabled'}`);
          return true;
        }
        /**
         * Check if the container is initialized
         */


        isInitialized() {
          return this._isInitialized;
        }
        /**
         * Initialize a single system and add it to update containers
         * @param system The system to initialize
         */


        _initializeSystem(system) {
          try {
            system.init(this._world);

            this._updateContainer.push(system.update.bind(system));

            this._lateUpdateContainer.push(system.lateUpdate.bind(system));

            console.log(`SystemContainer: Initialized system ${system.getTypeName()}`);
          } catch (error) {
            console.error(`SystemContainer: Failed to initialize system ${system.getTypeName()}:`, error);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=08f65b095758fc1a3bf9826c6dca96774b9daa74.js.map
import { _decorator, Component, Node, Sprite, tween, Color, Enum, Vec2, sp, v2, v3, UITransform, Tween, size } from 'cc';
import Bullet from '../../bullet/Bullet';
import { MainPlane } from '../mainPlane/MainPlane';
import GameEnum from '../../../const/GameEnum';
import BossHurt from './BossHurt';
import { Tools } from '../../../utils/Tools';
import { GameIns } from '../../../GameIns';
import { GameConst } from '../../../const/GameConst';
import EnemyEffectLayer from '../../layer/EnemyEffectLayer';
import FBoxCollider from '../../../collider-system/FBoxCollider';
import FCollider, { ColliderGroupType } from '../../../collider-system/FCollider';
const { ccclass, property } = _decorator;

@ccclass('BossUnit')
export default class BossUnit extends BossHurt {
    static UnitZIndex = Enum({
        SmokeBottom: -10,
        Skel: -9,
        SmokeTop: -8,
    });

    _data: any = null;
    _bossPlane: any = null;
    _collideComp: FBoxCollider | null = null;
    _unitType: number = 0;
    _curHp: number = 0;
    _maxHp: number = 0;
    _hpBar: Sprite | null = null;
    _hpWhite: Sprite | null = null;
    _hpWhiteTween: any = null;
    defence: number = 0;
    _hpStage: number = 0;
    _hpStageIndex: number = 0;
    _action: number = GameEnum.BossAction.Normal;
    _damaged: boolean = false;
    _damageable: boolean = false;
    _bSmoking: boolean = false;
    _whiteNode: Node | null = null;
    _winkCount: number = 0;
    _bWinkWhite: boolean = false;
    _winkAct: any = null;
    _skel: any = null;
    _curAnim: string = '';
    _skelCallMap: Map<string, Function> = new Map();
    _skelEventCallMap: Map<string, Function> = new Map();
    _smokeSkelPool: any[] = [];
    _smokePosArr: Vec2[] = [];
    _smokeSkelArr: any[] = [];
    _smokeBoneArr: string[] = [];

    onLoad(): void {
        // 初始化逻辑
    }

    init(data: any, bossPlane: any): void {
        this.reset();
        this.type = GameEnum.EnemyType.BossUnit;
        this._data = data;
        this._bossPlane = bossPlane;

        this.initData();
        this._initUI();
        this._refreshHpBar();
        this.setSkin(0);

        if (this._curHp > 0) {
            this._unitType = 1;
            this.isDead = false;
            this._initCollide();
            this._checkHpStage();
        } else {
            this._unitType = 0;
            this.isDead = true;
        }
    }

    reset(): void {
        this._curAnim = '';
        this._hpStage = 0;
        this._hpStageIndex = 0;
        this._action = GameEnum.BossAction.Normal;
        this.removeSmoke();
    }

    initData(): void {
        this._curHp = this._data.hp;
        this._maxHp = this._curHp;
    }

    _initCollide(): void {
        
        const colliderData = this._data.collider;
        this._collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this._collideComp!.init(this,size(colliderData.width, colliderData.height)); // 初始化碰撞组件
        this._collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;
        this._collideComp!.isEnable = false;
    }

    _initUI(): void {
        this.node.setPosition(this._data.pos.x, this._data.pos.y);

        switch (this._data.type) {
            case 0:
                if (!this._skel) {
                    const skelNode = new Node("skelNode");
                    this.node.addChild(skelNode);
                    // this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel)
                    this._skel = skelNode.addComponent(sp.Skeleton);
                    this._skel.skeletonData = GameIns.bossManager.skelDataMap.get(this._data.anim);
                    this._skel.premultipliedAlpha = false;
                    this._bossPlane.addSpine(this._skel);

                    this._data.mixArr.forEach((mix: [string, string]) => {
                        try {
                            this._skel.setMix(mix[0], mix[1], 0.5);
                        } catch (error) {
                            Tools.log('Boss unit mix error:', mix);
                        }
                    });

                    this._skel.setCompleteListener((trackEntry: any) => {
                        const animationName = trackEntry.animation ? trackEntry.animation.name : '';
                        this._skelCallMap.forEach((callback, key) => {
                            if (animationName === key && callback) {
                                callback();
                            }
                        });
                    });

                    this._skel.setEventListener((trackEntry: any, event: any) => {
                        const eventName = event.data.name;
                        this._skelEventCallMap.forEach((callback, key) => {
                            if (eventName === key && callback) {
                                callback();
                            }
                        });
                    });
                }

                // this._winkAct = new Tween()
                //     .to(0, { color: new Color(this._data.hurtColor.getR(), this._data.hurtColor.getG(), this._data.hurtColor.getB()) })
                //     .to(0.1, { color: Color.WHITE });
                break;

            case 1:
                const skelNode = new Node();
                skelNode.addComponent(UITransform);
                this.node.addChild(skelNode);
                this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel)
                // this._winkAct = tween()
                //     .to(0, { opacity: 180 })
                //     .to(3 * GameConst.ActionFrameTime, { opacity: 0 });
                break;
        }

        if (this._data.hpParam[0] !== 0) {
            let hpNode:Node = this._bossPlane.node.getChildByName(`blood${this._data.uId}`);
            if (!hpNode) {
                hpNode = new Node();
                hpNode.addComponent(UITransform);
                hpNode.name = `blood${this._data.uId}`;
                hpNode.parent = this._bossPlane.node;
                hpNode.setSiblingIndex(10);

                GameIns.bossManager.setBossFrame(hpNode.addComponent(Sprite), 'hp_0');

                const whiteNode = new Node("whiteNode");
                whiteNode.addComponent(UITransform);
                hpNode.addChild(whiteNode);
                this._hpWhite = whiteNode.addComponent(Sprite);
                GameIns.bossManager.setBossFrame(this._hpWhite, 'hp_2');
                this._hpWhite.type = Sprite.Type.FILLED;
                this._hpWhite.fillType = Sprite.FillType.HORIZONTAL;
                this._hpWhite.fillRange = 1;

                const barNode = new Node("barNode");
                barNode.addComponent(UITransform);
                hpNode.addChild(barNode);
                this._hpBar = barNode.addComponent(Sprite);
                GameIns.bossManager.setBossFrame(this._hpBar, 'hp_1');
                this._hpBar.type = Sprite.Type.FILLED;
                this._hpBar.fillType = Sprite.FillType.HORIZONTAL;
                this._hpBar.fillRange = 1;
            }

            let posX = this._data.hpParam[1] + this.node.position.x;
            let posY  = this._data.hpParam[2] + this.node.position.y;
            let scaleX = this._data.hpParam[3];
            hpNode.setPosition(posX, posY);
            hpNode.setScale(scaleX*hpNode.getScale().x,hpNode.getScale().y)
            hpNode.active = false;
        }
    }

    updateGameLogic(deltaTime: number): void {
        this._smokeSkelArr.forEach((smoke, index) => {
            const boneName = this._smokeBoneArr[index];
            const bone = this._skel.findBone(boneName);
            if (bone) {
                let pos = this._smokePosArr[index].add(new Vec2(bone.worldX, bone.worldY));
                smoke.node.setPosition(pos.x, pos.y);
            }
        });

        if (!this.isDead) {
            if (this._bWinkWhite) {
                this._winkCount++;
                if (this._winkCount > 8) {
                    this._winkCount = 0;
                    this._bWinkWhite = false;
                }
            }

            this.m_comps.forEach((comp) => {
                comp.update(deltaTime);
            });
        }
    }
    onCollide(collider: FCollider): void {
        if (!this.isDead && this.damageable) {
            if (collider.entity instanceof Bullet) {
                let damage = collider.entity.getAttack(this);
                try {
                    GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(),damage);
                } catch (error) {
                    console.error(error);
                }
                damage = Math.max(damage / 10, damage - this.defence);
                this.hurt(damage);
            } else if (collider.entity instanceof MainPlane) {
                // Handle collision with the main plane
            }
        }
    }

    hurt(damage: number): boolean {
        if (this.isDead || !this.damageable) {
            return false;
        }
        this.hpChange(-damage);
        this._checkHp();
        if (!this.isDead) {
            this._winkWhite();
        }
        return true;
    }

    hpChange(amount: number): void {
        let change = amount;
        let newHp = this._curHp + amount;

        if (newHp < 0) {
            change = -this._curHp;
        }

        this._curHp = newHp;
        if (this._curHp < 0) {
            this._curHp = 0;
        }

        if (this._bossPlane) {
            this._bossPlane.hpChange(change);
        }

        this._refreshHpBar();
    }

    _checkHp(): void {
        if (this._curHp <= this._hpStage) {
            this._damaged = true;
            this._playStageAnim(this._hpStageIndex + 1);
            this._hpStageIndex++;
            this._checkHpStage();

            if (this._hpStage < 0) {
                this._die();
            }
        }
    }

    _checkHpStage(): void {
        if (this._hpStageIndex < this._data.hpStage.length) {
            this._hpStage = this._data.hpStage[this._hpStageIndex];
        } else {
            this._hpStage = -1;
        }
    }

    _die(): void {
        this.isDead = true;
        try {
            if (this._hpBar) {
                this._hpBar.node.parent!.active = false;
            }
        } catch (error) {
            console.error(error);
        }

        if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();
            this._hpWhiteTween = null;
        }

        if (this._collideComp) {
            this._collideComp.isEnable = false;
        }

        this._playDieAnim();
        if (this._bossPlane) {
            this._bossPlane.unitDestroyed(this);
        }
    }

    _refreshHpBar(): void {
        if (this._hpBar) {
            if (this._hpWhiteTween) {
                this._hpWhiteTween.stop();
                this._hpWhiteTween = null;
            }

            this._hpBar.fillRange = this._curHp / this._maxHp;

            const fillDifference = Math.abs(this._hpWhite!.fillRange - this._hpBar.fillRange);
            this._hpWhiteTween = new Tween(this._hpWhite)
                .to(fillDifference, { fillRange: this._hpBar.fillRange })
                .call(() => {
                    this._hpWhiteTween = null;
                })
                .start();
        }
    }

    playSkel(animationName: string, loop: boolean, callback: Function = () => {}): boolean {
        if (!this._skel || this._curAnim === animationName) {
            return false;
        }
        this._curAnim = animationName;
        this._skelCallMap.set(animationName, callback);
        this._skel.setAnimation(0, animationName, loop);
        return true;
    }

    setEventCallback(eventName: string, callback: Function): void {
        this._skelEventCallMap.set(eventName, callback);
    }

    setSkin(skinIndex: number): void {
        if (this._skel) {
            this._skel.setSkin(`s${this._bossPlane.formIndex + 1}_${skinIndex}`);
        }
    }

    setAction(action: number): void {
        if (!this.isDead) {
            this._action = action;
        }
    }

    _winkWhite(): void {
        // if (!this._bWinkWhite && this._action < 2) { // Assuming 2 is the threshold for attack actions
        //     this._bWinkWhite = true;
        //     if (this._skel && this._skel.node) {
        //         this._winkAct.clone(this._skel.node).start();
        //     }
        // }
    }

    _playShakeAnim(): void {
        const actionFrameTime = 0.016; // Assuming 60 FPS
        tween(this.node)
            .to(actionFrameTime, { position: v3(11, -16), angle: -1 })
            .to(2 * actionFrameTime, { position: v3(7, 2), angle: 1 })
            .to(2 * actionFrameTime, { position: v3(20, -11), angle: 0 })
            .to(2 * actionFrameTime, { position: v3(28, 5) })
            .to(2 * actionFrameTime, { position: v3(13, -7) })
            .to(actionFrameTime, { position: v3(17, 1) })
            .to(actionFrameTime, { position: v3(4, -8) })
            .to(actionFrameTime, { position: v3(14, 2) })
            .to(actionFrameTime, { position: v3(-1, -6) })
            .to(actionFrameTime, { position: v3(5, 4) })
            .to(actionFrameTime, { position: v3(-3, -7) })
            .to(actionFrameTime, { position: v3(2, 1) })
            .to(actionFrameTime, { position: v3(0, 0) })
            .start();
    }

    hideSmoke(): void {
        for (const smoke of this._smokeSkelArr) {
            tween(smoke.node)
                .to(5 * 0.016, { opacity: 0 }) // Assuming 60 FPS
                .start();
        }
    }

    removeSmoke(): void {
        for (const smoke of this._smokeSkelArr) {
            smoke.node.active = false;
            this._smokeSkelPool.push(smoke);
            this._bossPlane.removeSpine(smoke);
        }
        this._smokeSkelArr.length = 0;
        this._smokePosArr.length = 0;
        this._smokeBoneArr.length = 0;
    }

    get id(): number {
        return this._data.id;
    }

    get unitId(): number {
        return this._data.uId;
    }

    isBody(): boolean {
        return this._unitType === 0;
    }

    getUnitType(): number {
        return this._data.type;
    }

    setPropertyRate(rates: number[]): void {
        if (rates.length > 2) {
            this._curHp *= rates[0];
            this._maxHp = this._curHp;
            this.attack *= rates[1];
            this._collideAtk *= rates[2];
        }
    }

    get damageable(): boolean {
        return this._damageable;
    }

    set damageable(value: boolean) {
        this._damageable = value;
    }

    get bossEntity(): any {
        return this._bossPlane;
    }

    get curHp(): number {
        return this._curHp;
    }

    get maxHp(): number {
        return this._maxHp;
    }
    _playStageAnim(stageIndex: number): void {
        this._onStageAnimEnd()
        this.setSkin(stageIndex);
    }

    _onStageAnimEnd(): void {
        try {
            if (this._bossPlane) {
                this._bossPlane.unitDestroyAnimEnd(this);
            }
            if (this.isDead) {
                this.playSkel(`idle${this._bossPlane.formIndex + 1}`, true);
            }
        } catch (error) {
            console.error(error);
        }
    }

    _getSmokeAnim(): any {
        let smokeAnim = this._smokeSkelPool.pop();
        if (!smokeAnim) {
            const smokeNode = new Node();
            smokeNode.addComponent(UITransform);
            this.node.addChild(smokeNode);
            smokeAnim = smokeNode.addComponent(sp.Skeleton);
            smokeAnim.skeletonData = GameIns.bossManager.smokeSkelData;
            smokeAnim.premultipliedAlpha = false;
            this._smokeSkelPool.push(smokeAnim);
        }
        smokeAnim.node.active = true;
        return smokeAnim;
    }

    _playDieAnim(): void {
        if (this._data.id >= 200 && this._data.id < 250) {
            return;
        }
        this._skel.setAnimation(0, `shake${this._bossPlane.formIndex + 1}`, false);
    }

    setCollideAble(isEnabled: boolean) {
        if (!this.isBody() && this._collideComp) {
            this.active = isEnabled;
            this._collideComp.isEnable = isEnabled;
    
            try {
                if (this._hpBar) {
                    this._hpBar!.node!.parent!.active = isEnabled;
                }
            } catch (error) {
                console.error("Error setting collide ability:", error);
            }
        }
    }
    
    getCollideAble(): boolean {
        return this._collideComp ? this._collideComp.isEnable : false;
    }
}
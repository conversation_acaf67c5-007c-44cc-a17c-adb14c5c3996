{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/CombineDisplay.ts"], "names": ["_decorator", "Component", "Label", "Node", "csproto", "DataMgr", "EventMgr", "MyApp", "logError", "ButtonPlus", "TopBlockInputUI", "UIMgr", "PlaneCombineResultUI", "PlaneUIEvent", "TabStatus", "ccclass", "property", "CombineDisplay", "_tips", "onLoad", "on", "TabChange", "onTabChange", "BagItemClick", "onBagItemClick", "bottomGrid", "getComponentsInChildren", "for<PERSON>ach", "btn", "addClick", "onBottomBtnClick", "combineOnceBtn", "onCombineOnceClick", "combineAllBtn", "onCombineAllClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_EQUIP_COMBINE", "onCombineResultMsg", "onDestroy", "targetOff", "unregister<PERSON><PERSON><PERSON>", "tabStatus", "Bag", "node", "active", "equip", "eqCombine", "clear", "prepareCombineAll", "refreshDisplay", "mergeLen", "size", "label", "index", "info", "getByPos", "string", "lubanTables", "TbEquip", "get", "item", "item_id", "name", "tip", "isFull", "next<PERSON>ev", "getCombineResult", "topGrid", "getComponentInChildren", "getByGuid", "guid", "deleteByGuid", "add", "emit", "UpdateBagGrids", "event", "nd", "target", "pos", "parseInt", "deleteByPos", "msg", "bag", "refreshItems", "openUI", "body", "equip_combine", "results", "hideUI", "combine", "combineAll"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC5CC,MAAAA,O;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,oB,kBAAAA,oB;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;gCAGjBiB,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACb,IAAD,C,UAERa,QAAQ,CAACb,IAAD,C,UAERa,QAAQ,CAACd,KAAD,C,UAERc,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,2BAVb,MACaC,cADb,SACoChB,SADpC,CAC8C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAWlCiB,KAXkC,GAWhB,CACtB,WADsB,EAEtB,WAFsB,EAGtB,WAHsB,EAItB,SAJsB,CAXgB;AAAA;;AAkB1CC,QAAAA,MAAM,GAAS;AACX;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,SAAzB,EAAoC,KAAKC,WAAzC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,YAAzB,EAAuC,KAAKC,cAA5C,EAA4D,IAA5D,EAAkE,CAAlE;AACA,eAAKC,UAAL,CAAiBC,uBAAjB;AAAA;AAAA,wCAAqDC,OAArD,CAA6DC,GAAG,IAAI;AAChEA,YAAAA,GAAG,CAACC,QAAJ,CAAa,KAAKC,gBAAlB,EAAoC,IAApC;AACH,WAFD;AAGA,eAAKC,cAAL,CAAqBF,QAArB,CAA8B,KAAKG,kBAAnC,EAAuD,IAAvD;AACA,eAAKC,aAAL,CAAoBJ,QAApB,CAA6B,KAAKK,iBAAlC,EAAqD,IAArD;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA/C,EAAqE,KAAKC,kBAA1E,EAA8F,IAA9F;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaQ,iBAAb,CAA+B;AAAA;AAAA,kCAAQN,EAAR,CAAWC,MAAX,CAAkBC,oBAAjD,EAAuE,KAAKC,kBAA5E,EAAgG,IAAhG;AACH;;AAEOlB,QAAAA,WAAW,CAACsB,SAAD,EAAuB;AACtC,cAAIA,SAAS,IAAI;AAAA;AAAA,sCAAUC,GAA3B,EAAgC;AAC5B,iBAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AACH;;AACD,eAAKD,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACA;AAAA;AAAA,kCAAQC,KAAR,CAAcC,SAAd,CAAwBC,KAAxB;AACA;AAAA;AAAA,kCAAQF,KAAR,CAAcC,SAAd,CAAwBE,iBAAxB;AACA,eAAKC,cAAL;AACH;;AAEOA,QAAAA,cAAc,GAAG;AACrB,gBAAMC,QAAQ,GAAG;AAAA;AAAA,kCAAQL,KAAR,CAAcC,SAAd,CAAwBK,IAAxB,EAAjB;AACA,eAAK7B,UAAL,CAAgBC,uBAAhB,CAAwCxB,KAAxC,EAA+CyB,OAA/C,CAAuD,CAAC4B,KAAD,EAAQC,KAAR,KAAkB;AACrE,kBAAMC,IAAI,GAAG;AAAA;AAAA,oCAAQT,KAAR,CAAcC,SAAd,CAAwBS,QAAxB,CAAiCF,KAAjC,CAAb;;AACA,gBAAIC,IAAJ,EAAU;AAAA;;AACNF,cAAAA,KAAK,CAACI,MAAN,4BAAe;AAAA;AAAA,kCAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8BL,IAAI,CAACM,IAAL,CAAUC,OAAxC,CAAf,qBAAe,sBAAkDC,IAAjE;AACH,aAFD,MAEO;AACHV,cAAAA,KAAK,CAACI,MAAN,GAAe,MAAf;AACH;AACJ,WAPD;AAQA,eAAKO,GAAL,CAASP,MAAT,GAAkB,KAAKzC,KAAL,CAAWmC,QAAX,CAAlB;;AACA,cAAI;AAAA;AAAA,kCAAQL,KAAR,CAAcC,SAAd,CAAwBkB,MAAxB,EAAJ,EAAsC;AAClC,kBAAMC,OAAO,GAAG;AAAA;AAAA,oCAAQpB,KAAR,CAAcC,SAAd,CAAwBoB,gBAAxB,EAAhB;;AACA,gBAAID,OAAJ,EAAa;AACT,mBAAKE,OAAL,CAAaC,sBAAb,CAAoCrE,KAApC,EAA2CyD,MAA3C,GAAoDS,OAAO,CAACH,IAA5D;AACH,aAFD,MAEO;AACH;AAAA;AAAA,wCAAS,SAAT,EAAqB,0CAArB;AACH;AACJ,WAPD,MAOO;AACH,iBAAKK,OAAL,CAAaC,sBAAb,CAAoCrE,KAApC,EAA2CyD,MAA3C,GAAoD,MAApD;AACH;;AACD,eAAK5B,cAAL,CAAoBe,IAApB,CAAyBC,MAAzB,GAAkCM,QAAQ,IAAI,CAA9C;AACA,eAAKpB,aAAL,CAAmBa,IAAnB,CAAwBC,MAAxB,GAAiCM,QAAQ,IAAI,CAA7C;AACH;;AAEO7B,QAAAA,cAAc,CAACuC,IAAD,EAA2B;AAC7C,cAAI,CAAC,KAAKjB,IAAL,CAAUC,MAAf,EAAuB;AACvB,gBAAMU,IAAI,GAAG;AAAA;AAAA,kCAAQT,KAAR,CAAcC,SAAd,CAAwBuB,SAAxB,CAAkCT,IAAI,CAACU,IAAvC,CAAb;;AACA,cAAIhB,IAAJ,EAAU;AACN;AAAA;AAAA,oCAAQT,KAAR,CAAcC,SAAd,CAAwByB,YAAxB,CAAqCX,IAAI,CAACU,IAA1C;AACH,WAFD,MAEO;AACH,gBAAI,CAAC;AAAA;AAAA,oCAAQzB,KAAR,CAAcC,SAAd,CAAwB0B,GAAxB,CAA4BZ,IAA5B,CAAL,EAAwC;AAC3C;;AACD;AAAA;AAAA,oCAASa,IAAT,CAAc;AAAA;AAAA,4CAAaC,cAA3B;AACA,eAAKzB,cAAL;AACH;;AAEOtB,QAAAA,gBAAgB,CAACgD,KAAD,EAAoB;AACxC,gBAAMC,EAAE,GAAGD,KAAK,CAACE,MAAjB;AACA,gBAAMC,GAAG,GAAGC,QAAQ,CAACH,EAAE,CAACd,IAAJ,CAApB;AACA,cAAI,CAAC;AAAA;AAAA,kCAAQjB,KAAR,CAAcC,SAAd,CAAwBS,QAAxB,CAAiCuB,GAAjC,CAAL,EAA4C;AAC5C;AAAA;AAAA,kCAAQjC,KAAR,CAAcC,SAAd,CAAwBkC,WAAxB,CAAoCF,GAApC;AACA,eAAK7B,cAAL;AACA;AAAA;AAAA,oCAASwB,IAAT,CAAc;AAAA;AAAA,4CAAaC,cAA3B;AACH;;AAEOrC,QAAAA,kBAAkB,CAAC4C,GAAD,EAA0B;AAAA;;AAChD;AAAA;AAAA,kCAAQC,GAAR,CAAYC,YAAZ;AACA;AAAA;AAAA,kCAAQtC,KAAR,CAAcC,SAAd,CAAwBC,KAAxB;AACA,eAAKE,cAAL;AACA;AAAA;AAAA,8BAAMmC,MAAN;AAAA;AAAA,yEAAmCH,GAAG,CAACI,IAAvC,0BAAmC,UAAUC,aAA7C,qBAAmC,UAAyBC,OAA5D;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AAEO3D,QAAAA,kBAAkB,GAAG;AACzB,cAAI,CAAC;AAAA;AAAA,kCAAQgB,KAAR,CAAcC,SAAd,CAAwBkB,MAAxB,EAAL,EAAuC;AACvC;AAAA;AAAA,8BAAMoB,MAAN;AAAA;AAAA;AACA;AAAA;AAAA,kCAAQvC,KAAR,CAAcC,SAAd,CAAwB2C,OAAxB;AACH;;AAEO1D,QAAAA,iBAAiB,GAAG;AACxB,cAAI;AAAA;AAAA,kCAAQc,KAAR,CAAcC,SAAd,CAAwBkB,MAAxB,EAAJ,EAAsC;AACtC;AAAA;AAAA,8BAAMoB,MAAN;AAAA;AAAA;AACA;AAAA;AAAA,kCAAQvC,KAAR,CAAcC,SAAd,CAAwB4C,UAAxB;AACH;;AA7GyC,O;;;;;iBAErB,I;;;;;;;iBAEG,I;;;;;;;iBAEN,I;;;;;;;iBAEgB,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, Component, EventTouch, Label, Node } from 'cc';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { DataMgr } from 'db://assets/scripts/Data/DataManager';\nimport { EventMgr } from 'db://assets/scripts/event/EventManager';\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { logError } from 'db://assets/scripts/Utils/Logger';\nimport { ButtonPlus } from '../../../../common/components/button/ButtonPlus';\nimport { TopBlockInputUI } from '../../../../common/TopBlockInputUI';\nimport { UIMgr } from '../../../../UIMgr';\nimport { PlaneCombineResultUI } from '../../PlaneCombineResultUI';\nimport { PlaneUIEvent } from '../../PlaneEvent';\nimport { TabStatus } from '../../PlaneTypes';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('CombineDisplay')\nexport class CombineDisplay extends Component {\n    @property(Node)\n    topGrid: Node|null = null\n    @property(Node)\n    bottomGrid: Node|null = null\n    @property(Label)\n    tip: Label|null = null\n    @property(ButtonPlus)\n    combineOnceBtn: ButtonPlus|null = null\n    @property(ButtonPlus)\n    combineAllBtn: ButtonPlus|null = null\n    private _tips: string[] = [\n        \"选择你想合成的装备\",\n        \"还需要2件相同装备\",\n        \"还需要1件相同装备\",\n        \"一切准备就绪!\",\n    ]\n\n    onLoad(): void {\n        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this)\n        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this, 1)\n        this.bottomGrid!.getComponentsInChildren(ButtonPlus).forEach(btn => {\n            btn.addClick(this.onBottomBtnClick, this)\n        })\n        this.combineOnceBtn!.addClick(this.onCombineOnceClick, this)\n        this.combineAllBtn!.addClick(this.onCombineAllClick, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this)\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this)\n    }\n\n    private onTabChange(tabStatus: TabStatus) {\n        if (tabStatus == TabStatus.Bag) {\n            this.node.active = false;\n            return\n        }\n        this.node.active = true;\n        DataMgr.equip.eqCombine.clear();\n        DataMgr.equip.eqCombine.prepareCombineAll();\n        this.refreshDisplay();\n    }\n\n    private refreshDisplay() {\n        const mergeLen = DataMgr.equip.eqCombine.size()\n        this.bottomGrid.getComponentsInChildren(Label).forEach((label, index) => {\n            const info = DataMgr.equip.eqCombine.getByPos(index)\n            if (info) {\n                label.string = MyApp.lubanTables.TbEquip.get(info.item.item_id)?.name\n            } else {\n                label.string = \"合成材料\"\n            }\n        });\n        this.tip.string = this._tips[mergeLen]\n        if (DataMgr.equip.eqCombine.isFull()) {\n            const nextLev = DataMgr.equip.eqCombine.getCombineResult()\n            if (nextLev) {\n                this.topGrid.getComponentInChildren(Label).string = nextLev.name\n            } else {\n                logError(\"PlaneUI\", `cant get merge result no pos1 equip info`)\n            }\n        } else {\n            this.topGrid.getComponentInChildren(Label).string = \"合成结果\"\n        }\n        this.combineOnceBtn.node.active = mergeLen == 3\n        this.combineAllBtn.node.active = mergeLen != 3\n    }\n\n    private onBagItemClick(item: csproto.cs.ICSItem) {\n        if (!this.node.active) return\n        const info = DataMgr.equip.eqCombine.getByGuid(item.guid)\n        if (info) {\n            DataMgr.equip.eqCombine.deleteByGuid(item.guid)\n        } else {\n            if (!DataMgr.equip.eqCombine.add(item)) return\n        }\n        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)\n        this.refreshDisplay()\n    }\n\n    private onBottomBtnClick(event: EventTouch) {\n        const nd = event.target as Node\n        const pos = parseInt(nd.name)\n        if (!DataMgr.equip.eqCombine.getByPos(pos)) return\n        DataMgr.equip.eqCombine.deleteByPos(pos)\n        this.refreshDisplay()\n        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)\n    }\n\n    private onCombineResultMsg(msg: csproto.cs.IS2CMsg) {\n        DataMgr.bag.refreshItems();\n        DataMgr.equip.eqCombine.clear();\n        this.refreshDisplay()\n        UIMgr.openUI(PlaneCombineResultUI, msg.body?.equip_combine?.results)\n        UIMgr.hideUI(TopBlockInputUI)\n    }\n\n    private onCombineOnceClick() {\n        if (!DataMgr.equip.eqCombine.isFull()) return\n        UIMgr.openUI(TopBlockInputUI)\n        DataMgr.equip.eqCombine.combine();\n    }\n\n    private onCombineAllClick() {\n        if (DataMgr.equip.eqCombine.isFull()) return\n        UIMgr.openUI(TopBlockInputUI)\n        DataMgr.equip.eqCombine.combineAll()\n    }\n}\n\n"]}